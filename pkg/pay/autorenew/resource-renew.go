package autorenew

import (
	"context"
	"time"

	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/pay/order"
	"xiaoshiai.cn/rune/pkg/pay/utils"
)

type ResourceRenewProcess struct {
	OrderService          order.OrderService
	Store                 store.Store
	RenewBeforeExpireDays int
}

func NewOrderRenewProcess(store store.Store, orders order.OrderService) *ResourceRenewProcess {
	return &ResourceRenewProcess{
		OrderService:          orders,
		Store:                 store,
		RenewBeforeExpireDays: 7,
	}
}

func (o *ResourceRenewProcess) Name() string {
	return "order-renew-process"
}

func (o *ResourceRenewProcess) Run(ctx context.Context) error {
	runner := utils.ScheduleRunner{
		CustomFuncs: map[string]func(ctx context.Context) error{
			// Run every day at 6:00
			"0 0 6 * * *": o.renewResources,
		},
	}
	return runner.Run(ctx)
}

const Day = 24 * time.Hour

func (o *ResourceRenewProcess) renewResources(ctx context.Context) error {
	return nil
}
