package types

import (
	"testing"
)

func TestPrice_MarshalBSONValue(t *testing.T) {
	p := NewPrice(-1.23)
	got, got1, err := p.MarshalBSONValue()
	if err != nil {
		t.<PERSON><PERSON><PERSON>("Price.MarshalBSONValue() error = %v", err)
		return
	}
	newp := NewPrice(0)
	if err := newp.UnmarshalBSONValue(got, got1); err != nil {
		t.<PERSON><PERSON><PERSON>("Price.UnmarshalBSONValue() error = %v", err)
		return
	}
	if !newp.Equal(p) {
		t.<PERSON><PERSON><PERSON>("Price.UnmarshalBSONValue() = %v, want %v", newp, p)
	}
}
