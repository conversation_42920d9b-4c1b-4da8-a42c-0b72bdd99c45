package types

import (
	"encoding/json"

	"github.com/shopspring/decimal"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/bsontype"
	"go.mongodb.org/mongo-driver/x/bsonx/bsoncore"
)

type Price struct {
	Amount decimal.Decimal
}

func NewPrice(amount float64) Price {
	return Price{Amount: decimal.NewFromFloat(amount)}
}

func (p Price) Round(precision int32) Price {
	return Price{Amount: p.Amount.Round(precision)}
}

func (p Price) Float64() float64 {
	return p.Amount.InexactFloat64()
}

func (p Price) String() string {
	return p.Amount.String()
}

func (p Price) Add(p2 Price) Price {
	return Price{Amount: p.Amount.Add(p2.Amount)}
}

func (p Price) Sub(p2 Price) Price {
	return Price{Amount: p.Amount.Sub(p2.Amount)}
}

func (p Price) Mul(p2 Price) Price {
	return Price{Amount: p.Amount.Mul(p2.Amount)}
}

func (p Price) Div(p2 Price) Price {
	return Price{Amount: p.Amount.Div(p2.Amount)}
}

func (p Price) Neg() Price {
	return Price{Amount: p.Amount.Neg()}
}

func (p Price) IsZero() bool {
	return p.Amount.IsZero()
}

func (p Price) IsPositive() bool {
	return p.Amount.IsPositive()
}

func (p Price) IsNegative() bool {
	return p.Amount.IsNegative()
}

func (p Price) Equal(p2 Price) bool {
	return p.Amount.Equal(p2.Amount)
}

func (p Price) Cmp(p2 Price) int {
	return p.Amount.Cmp(p2.Amount)
}

func (p Price) GreaterThan(p2 Price) bool {
	return p.Amount.GreaterThan(p2.Amount)
}

func (p Price) GreaterThanOrEqual(p2 Price) bool {
	return p.Amount.GreaterThanOrEqual(p2.Amount)
}

func (p Price) LessThan(p2 Price) bool {
	return p.Amount.LessThan(p2.Amount)
}

func (p Price) LessThanOrEqual(p2 Price) bool {
	return p.Amount.LessThanOrEqual(p2.Amount)
}

func (p Price) MarshalText() ([]byte, error) {
	return []byte(p.Amount.String()), nil
}

func (p *Price) UnmarshalText(text []byte) error {
	amount, err := decimal.NewFromString(string(text))
	if err != nil {
		return err
	}
	p.Amount = amount
	return nil
}

func (p Price) MarshalJSON() ([]byte, error) {
	return json.Marshal(p.Amount.InexactFloat64())
}

func (p *Price) UnmarshalJSON(data []byte) error {
	var amount float64
	if err := json.Unmarshal(data, &amount); err != nil {
		return err
	}
	p.Amount = decimal.NewFromFloat(amount)
	return nil
}

func (p Price) MarshalBSONValue() (bsontype.Type, []byte, error) {
	return bson.MarshalValue(p.Amount.InexactFloat64())
}

func (p *Price) UnmarshalBSONValue(t bsontype.Type, data []byte) error {
	switch t {
	case bson.TypeDouble:
		f64, rem, ok := bsoncore.ReadDouble(data)
		if !ok || len(rem) != 0 {
			return bsoncore.NewInsufficientBytesError(data, rem)
		}
		p.Amount = decimal.NewFromFloat(f64)
		return nil
	case bson.TypeString:
		str, rem, ok := bsoncore.ReadString(data)
		if !ok || len(rem) != 0 {
			return bsoncore.NewInsufficientBytesError(data, rem)
		}
		amount, err := decimal.NewFromString(str)
		if err != nil {
			return err
		}
		p.Amount = amount
		return nil
	default:
		var amount float64
		if err := bson.Unmarshal(data, &amount); err != nil {
			return err
		}
		p.Amount = decimal.NewFromFloat(amount)
		return nil
	}
}

type UnitPrice struct {
	Price Price
	Unit  string
}

func NewUnitPrice(price float64, unit string) UnitPrice {
	return UnitPrice{Price: NewPrice(price), Unit: unit}
}

func (u UnitPrice) String() string {
	return u.Price.String() + " " + u.Unit
}
