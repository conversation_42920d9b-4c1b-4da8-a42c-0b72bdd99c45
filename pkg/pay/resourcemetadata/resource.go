package resourcemetadata

import (
	"context"
	"time"

	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/pay/product"
)

type ResourceMetadataService interface {
	GetMetadata(ctx context.Context, ref store.ResourcedObjectReference) (*ResourceMetadata, error)
}

type ResourceMetadata struct {
	SKU     product.SKUReference `json:"sku,omitempty"`
	Expires time.Time            `json:"expires,omitempty"`
}
