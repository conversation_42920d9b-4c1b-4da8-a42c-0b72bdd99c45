package wallet

import (
	"context"
	"net/http"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/rune/pkg/base"
)

func (a *API) GetTenantWallet(w http.ResponseWriter, r *http.Request) {
	a.onTenantWallet(w, r, func(ctx context.Context, tenant string) (any, error) {
		return a.WalletService.Get(ctx, tenant)
	})
}

func (a *API) onTenantWallet(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, wallet string) (any, error)) {
	api.On(w, r, func(ctx context.Context) (any, error) {
		tenant := api.Path(r, "tenant", "")
		if tenant == "" {
			return "", errors.NewBadRequest("tenant name is required")
		}
		return fn(ctx, tenant)
	})
}

func (a *API) tenantWalletGroup() api.Group {
	return base.
		NewTenantGroup("wallet").
		Route(
			api.GET("").
				To(a.GetTenantWallet).
				Response(&Wallet{}),
		)
}

// currentlly allow organization to get wallet of tenant
func (a *API) GetTenantOganizationWallet(w http.ResponseWriter, r *http.Request) {
	a.onTenantOrganizationWallet(w, r, func(ctx context.Context, tenant, org string) (any, error) {
		return a.WalletService.Get(ctx, tenant)
	})
}

func (a *API) onTenantOrganizationWallet(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, tenant, org string) (any, error)) {
	api.On(w, r, func(ctx context.Context) (any, error) {
		tenant := api.Path(r, "tenant", "")
		if tenant == "" {
			return "", errors.NewBadRequest("tenant name is required")
		}
		organization := api.Path(r, "organization", "")
		if organization == "" {
			return "", errors.NewBadRequest("organization name is required")
		}
		return fn(ctx, tenant, organization)
	})
}

func (a *API) tenantOrganizationWalletGroup() api.Group {
	return base.
		NewTenantOrganizationGroup("wallet").
		Route(
			api.GET("").
				To(a.GetTenantOganizationWallet).
				Response(&Wallet{}),
		)
}
