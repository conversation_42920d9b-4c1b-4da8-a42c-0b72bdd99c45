package wallet

import (
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
)

func NewAPI(store store.Store, walletService WalletService) *API {
	return &API{
		Store:         store,
		WalletService: walletService,
	}
}

type API struct {
	Store         store.Store
	WalletService WalletService
}

func (a *API) Group() api.Group {
	return api.
		NewGroup("").
		Tag("Wallet").
		SubGroup(
			a.adminWalletGroup(),
			a.tenantWalletGroup(),
			a.tenantOrganizationWalletGroup(),
			a.tenantTransactionGroup(),
		)
}
