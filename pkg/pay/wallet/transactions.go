package wallet

import (
	"context"
	"net/http"
	"time"

	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/base"
	"xiaoshiai.cn/rune/pkg/pay/types"
)

type WalletTransaction struct {
	store.ObjectMeta `json:",inline"`
	Wallet           string                `json:"wallet"`
	Kind             WalletTransactionKind `json:"kind"`
	Operator         string                `json:"operator"`
	Organization     string                `json:"organization"`
	Category         string                `json:"category"`
	Amount           types.Price           `json:"amount"`
	Balance          types.Price           `json:"balance"`
	OrderID          string                `json:"orderID"`
	PaymentID        string                `json:"paymentID"`
	Summary          string                `json:"summary"`
}

type WalletTransactionKind string

const (
	WalletTransactionKindPurchase WalletTransactionKind = "Purchase"
	WalletTransactionKindRecharge WalletTransactionKind = "Recharge"
	WalletTransactionKindRefund   WalletTransactionKind = "Refund"
)

func (a *API) ListTenantTransactions(w http.ResponseWriter, r *http.Request) {
	a.onTenantWallet(w, r, func(ctx context.Context, tenant string) (any, error) {
		options := ListTransactionsOptions{
			ListOptions:  api.GetListOptions(r),
			Start:        api.Query(r, "start", time.Time{}),
			End:          api.Query(r, "end", time.Time{}),
			Kind:         api.Query(r, "kind", ""),
			OrderID:      api.Query(r, "orderID", ""),
			Organization: api.Query(r, "organization", ""),
			PaymentID:    api.Query(r, "paymentID", ""),
		}
		return a.WalletService.ListTransactions(ctx, tenant, options)
	})
}

func (a *API) tenantTransactionGroup() api.Group {
	return api.NewGroup("").SubGroup(
		base.
			NewTenantGroup("transactions").
			Route(
				api.GET("").
					To(a.ListTenantTransactions).
					Param(api.PageParams...).
					Param(
						api.QueryParam("start", "").Format("date-time").Optional(),
						api.QueryParam("end", "").Format("date-time").Optional(),
						api.QueryParam("kind", "").In(WalletTransactionKindPurchase, WalletTransactionKindRecharge, WalletTransactionKindRefund).Optional(),
						api.QueryParam("orderID", "").Optional(),
						api.QueryParam("organization", "").Optional(),
						api.QueryParam("paymentID", "").Optional(),
					).
					Response(store.List[WalletTransaction]{}),
			),
	)
}
