package wallet

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	mongodriver "go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/queue"
	"xiaoshiai.cn/common/rand"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/common/store/mongo"
	"xiaoshiai.cn/rune/pkg/base"
	"xiaoshiai.cn/rune/pkg/iam/events"
	"xiaoshiai.cn/rune/pkg/pay/types"
)

const ReasonInsufficientBalance = "InsufficientBalance"

func init() {
	mongo.GlobalObjectsScheme.Register(&Wallet{},
		mongo.ObjectDefination{
			Uniques: []mongo.UnionFields{
				{"name"},
			},
			Indexes: []mongo.UnionFields{
				{"creationTimestamp"},
				{"level"},
				{"state"},
			},
		})

	mongo.GlobalObjectsScheme.Register(&WalletTransaction{},
		mongo.ObjectDefination{
			Uniques: []mongo.UnionFields{
				{"name"},
				{"paymentID"},
			},
			Indexes: []mongo.UnionFields{
				{"tenant"}, // scope
				{"kind"},
				{"wallet"},
				{"orderID"},
				{"creationTimestamp"},
			},
		})
}

type Wallet struct {
	store.ObjectMeta `json:",inline"`
	Balance          types.Price `json:"balance"`
	Disabled         bool        `json:"disabled"`
	Overdue          bool        `json:"overdue"`
}

type RechargeOptions struct {
	Amount    types.Price `json:"amount"`
	Operator  string      `json:"operator"`
	OrderID   string      `json:"orderID"`
	Summary   string      `json:"summary"`
	PaymentID string      `json:"paymentID"`
}

type ListTransactionsOptions struct {
	api.ListOptions `json:",inline"`
	Start           time.Time `json:"start"`
	End             time.Time `json:"end"`
	Kind            string    `json:"kind"`
	OrderID         string    `json:"orderID"`
	Organization    string    `json:"organization"`
	PaymentID       string    `json:"paymentID"`
}

type PurchaseDetails struct {
	Operator     string      `json:"operator"`
	Amount       types.Price `json:"amount"`
	PaymentID    string      `json:"paymentID"`
	OrderID      string      `json:"orderID"`
	Summary      string      `json:"summary"`
	Organization string      `json:"organization"`
	// AllowNegative indicates whether the purchase is allowed to result in a negative balance.
	AllowNegative bool `json:"allowNegative"`
}

type RefundOptions struct {
	Reason       string `json:"reason"`
	Operator     string `json:"operator"`
	Organization string `json:"organization"`
	OrderID      string `json:"orderID"`
	Summary      string `json:"summary"`
	PaymentID    string `json:"paymentID"`
}

var ErrPaymentIDAlreadyExists = errors.NewBadRequest("payment id already exists")

type WalletService interface {
	InitWallet(ctx context.Context, wallet string, balance types.Price) error

	List(ctx context.Context, options api.ListOptions) (store.List[Wallet], error)
	Get(ctx context.Context, wallet string) (*Wallet, error)
	Remove(ctx context.Context, wallet string) error

	Purchase(ctx context.Context, wallet string, details PurchaseDetails) (*WalletTransaction, error)
	Recharge(ctx context.Context, wallet string, options RechargeOptions) (*WalletTransaction, error)
	Refund(ctx context.Context, wallet string, amount types.Price, options RefundOptions) (*WalletTransaction, error)

	ListTransactions(ctx context.Context, wallet string, options ListTransactionsOptions) (store.List[WalletTransaction], error)
	GetTransaction(ctx context.Context, wallet string, transactionid string) (*WalletTransaction, error)
	GetTransactionByPaymentID(ctx context.Context, wallet string, paymentid string) (*WalletTransaction, error)

	SetDisabled(ctx context.Context, wallet string, disabled bool) error
}

func NewWalletService(mongodb *mongo.MongoStorage, queue queue.Queue, recorder events.Recorder) WalletService {
	return &walletService{
		storage:  mongodb,
		col:      mongodb.Database().Collection("wallets"),
		callback: &WalletServiceQueueCallback{Queue: queue},
		recorder: recorder,
	}
}

type WalletServiceQueueCallback struct {
	Queue queue.Queue
}

func (q *WalletServiceQueueCallback) OnWalletEvent(ctx context.Context, event WalletEvent) error {
	data, err := json.Marshal(event)
	if err != nil {
		return err
	}
	return q.Queue.Enqueue(ctx, "", data, queue.EnqueueOptions{})
}

type WalletEvent struct {
	Wallet   string `json:"wallet"`
	Disabled bool   `json:"disabled"`
	Overdue  bool   `json:"overdue"`
}

type WalletEventCallback interface {
	OnWalletEvent(ctx context.Context, event WalletEvent) error
}

type walletService struct {
	storage  store.TransactionStore
	col      *mongodriver.Collection
	recorder events.Recorder
	callback WalletEventCallback
}

// SetDisabled implements WalletService.
func (s *walletService) SetDisabled(ctx context.Context, wallet string, disabled bool) error {
	result := s.col.FindOneAndUpdate(ctx,
		bson.M{"name": wallet},
		bson.M{"$set": bson.M{"disabled": disabled}},
		options.FindOneAndUpdate().SetReturnDocument(options.After),
	)
	if err := result.Err(); err != nil {
		return mongo.ConvetMongoError(err, s.col, wallet)
	}
	after := &Wallet{}
	if err := result.Decode(after); err != nil {
		return err
	}
	return nil
}

// Remove implements WalletService.
func (s *walletService) Remove(ctx context.Context, wallet string) error {
	return s.storage.Transaction(ctx, func(ctx context.Context, storage store.Store) error {
		obj := &Wallet{ObjectMeta: store.ObjectMeta{Name: wallet}}
		if err := storage.Delete(ctx, obj); err != nil {
			return err
		}
		return nil
	})
}

// InitWallet implements WalletService.
func (s *walletService) InitWallet(ctx context.Context, wallet string, balance types.Price) error {
	walletObj := &Wallet{
		ObjectMeta: store.ObjectMeta{Name: wallet},
		Balance:    balance,
	}
	return s.storage.Create(ctx, walletObj)
}

// List implements WalletService.
func (s *walletService) List(ctx context.Context, options api.ListOptions) (store.List[Wallet], error) {
	list := store.List[Wallet]{}
	if options.Sort == "" {
		options.Sort = "disabled,time-"
	}
	opts := base.ListOptionsToStoreListOptions(options)
	opts = append(opts, store.WithSearchFields("name", "alias"))
	if err := s.storage.List(ctx, &list, opts...); err != nil {
		return list, err
	}
	return list, nil
}

// ListRecords implements WalletService.
func (s *walletService) ListTransactions(ctx context.Context, wallet string, options ListTransactionsOptions) (store.List[WalletTransaction], error) {
	reqs := store.Requirements{
		store.RequirementEqual("wallet", wallet),
	}
	reqs = append(reqs, store.NewCreationRangeRequirement(options.Start, options.End)...)
	if options.OrderID != "" {
		reqs = append(reqs, store.RequirementEqual("orderID", options.OrderID))
	}
	if options.Kind != "" {
		reqs = append(reqs, store.RequirementEqual("kind", options.Kind))
	}
	if options.Organization != "" {
		reqs = append(reqs, store.RequirementEqual("organization", options.Organization))
	}
	if options.PaymentID != "" {
		reqs = append(reqs, store.RequirementEqual("paymentID", options.PaymentID))
	}
	opts := base.ListOptionsToStoreListOptions(options.ListOptions)
	opts = append(opts, store.WithFieldRequirements(reqs...))
	list := store.List[WalletTransaction]{}
	if err := s.storage.List(ctx, &list, opts...); err != nil {
		return list, err
	}
	return list, nil
}

// Get implements WalletService.
func (s *walletService) Get(ctx context.Context, wallet string) (*Wallet, error) {
	walletObj := &Wallet{}
	if err := s.storage.Get(ctx, wallet, walletObj); err != nil {
		return nil, err
	}
	return walletObj, nil
}

func NewTransactionID() string {
	return "transaction-" + time.Now().Format("20060102150405") + rand.RandomNumeric(6)
}

func (s *walletService) Purchase(ctx context.Context, wallet string, details PurchaseDetails) (*WalletTransaction, error) {
	delta := details.Amount
	if delta.IsNegative() {
		return nil, errors.NewInvalid("wallet", wallet, fmt.Errorf("purchase amount must be positive"))
	}

	// First get the current wallet state
	walletObj, err := s.Get(ctx, wallet)
	if err != nil {
		return nil, err
	}
	// simple validate balance before purchase
	newBalance := walletObj.Balance.Sub(delta)
	if newBalance.IsNegative() && !details.AllowNegative {
		return nil, errors.NewCustomError(http.StatusBadRequest, ReasonInsufficientBalance, "Insufficient balance")
	}

	recordID := NewTransactionID()
	var transaction *WalletTransaction
	err = s.storage.Transaction(ctx, func(ctx context.Context, storage store.Store) error {
		// Use a more precise update operation that ensures atomicity and consistency
		filter := bson.M{"name": wallet}
		if !details.AllowNegative {
			filter["balance"] = bson.M{"$gte": delta.Float64()}
		}
		result := s.col.FindOneAndUpdate(ctx,
			filter,
			pipelineAddBalance(delta.Neg()),
			options.FindOneAndUpdate().SetReturnDocument(options.After),
		)
		if result.Err() != nil {
			return errors.NewCustomError(http.StatusBadRequest, ReasonInsufficientBalance, "Insufficient balance")
		}
		after := &Wallet{}
		if err := result.Decode(after); err != nil {
			return err
		}
		record := &WalletTransaction{
			ObjectMeta:   store.ObjectMeta{Name: recordID},
			Amount:       delta.Neg(), // -amount
			OrderID:      details.OrderID,
			PaymentID:    details.PaymentID,
			Balance:      after.Balance,
			Organization: details.Organization,
			Summary:      details.Summary,
			Wallet:       wallet,
			Operator:     details.Operator,
			Kind:         WalletTransactionKindPurchase,
		}
		if err := s.storage.Create(ctx, record); err != nil {
			if errors.IsConflict(err) {
				return ErrPaymentIDAlreadyExists
			}
			return err
		}
		transaction = record
		walletObj = after
		return nil
	})
	if err != nil {
		return nil, err
	}
	s.recorder.EventNoAggregateAnnotations(ctx, walletObj, types.ReasonWalletPurchase, fmt.Sprintf("purchase %0.2f", delta.Float64()), map[string]string{
		"delta":   delta.String(),
		"balance": walletObj.Balance.String(),
	})
	if walletObj.Balance.IsNegative() {
		s.recorder.EventNoAggregateAnnotations(ctx, walletObj, types.ReasonWalletOverdue, "wallet overdue", map[string]string{
			"balance": walletObj.Balance.String(),
		})
		s.callback.OnWalletEvent(ctx, WalletEvent{
			Wallet:   wallet,
			Overdue:  walletObj.Overdue,
			Disabled: walletObj.Disabled,
		})
	}
	return transaction, nil
}

func pipelineAddBalance(delta types.Price) mongodriver.Pipeline {
	return mongodriver.Pipeline{
		bson.D{ // Stage 1: update balance
			{Key: "$set", Value: bson.D{
				{Key: "balance", Value: bson.D{
					{Key: "$add", Value: bson.A{"$balance", delta.Float64()}},
				}},
			}},
		},
		bson.D{ // Stage 2: set overdue
			{Key: "$set", Value: bson.D{
				{Key: "overdue", Value: bson.D{
					{Key: "$lt", Value: bson.A{"$balance", 0}},
				}},
			}},
		},
	}
}

func (s *walletService) Recharge(ctx context.Context, wallet string, opts RechargeOptions) (*WalletTransaction, error) {
	delta := opts.Amount
	if delta.IsNegative() {
		return nil, errors.NewInvalid("wallet", wallet, fmt.Errorf("recharge amount must be positive"))
	}
	var transaction *WalletTransaction
	var walletobj *Wallet
	if err := s.storage.Transaction(ctx, func(ctx context.Context, storage store.Store) error {
		recordID := NewTransactionID()
		result := s.col.FindOneAndUpdate(ctx,
			bson.M{"name": wallet},
			pipelineAddBalance(delta),
			options.FindOneAndUpdate().SetReturnDocument(options.After),
		)
		if err := result.Err(); err != nil {
			return errors.NewNotFound("wallets", wallet)
		}
		after := &Wallet{}
		if err := result.Decode(after); err != nil {
			return err
		}
		record := &WalletTransaction{
			ObjectMeta: store.ObjectMeta{Name: recordID},
			Amount:     delta,
			OrderID:    opts.OrderID,
			PaymentID:  opts.PaymentID,
			Summary:    opts.Summary,
			Balance:    after.Balance,
			Wallet:     wallet,
			Operator:   opts.Operator,
			Kind:       WalletTransactionKindRecharge,
		}
		if err := storage.Create(ctx, record); err != nil {
			return err
		}
		transaction = record
		walletobj = after
		return nil
	}); err != nil {
		return nil, err
	}

	s.recorder.EventNoAggregateAnnotations(ctx, walletobj, types.ReasonWalletRecharge, fmt.Sprintf("recharge %0.2f", delta.Float64()), map[string]string{
		"delta":   delta.String(),
		"balance": walletobj.Balance.String(),
	})
	if walletobj.Balance.Sub(delta).IsNegative() {
		s.recorder.EventNoAggregateAnnotations(ctx, walletobj, types.ReasonWalletRestored, "wallet restored", map[string]string{
			"balance": walletobj.Balance.String(),
		})
		s.callback.OnWalletEvent(ctx, WalletEvent{
			Wallet:   wallet,
			Overdue:  walletobj.Overdue,
			Disabled: walletobj.Disabled,
		})
	}
	return transaction, nil
}

// Refund implements WalletService.
func (s *walletService) Refund(ctx context.Context, wallet string, amount types.Price, opts RefundOptions) (*WalletTransaction, error) {
	var transaction *WalletTransaction
	var walletobj *Wallet

	if amount.IsNegative() {
		return nil, errors.NewInvalid("wallet", wallet, fmt.Errorf("refund amount must be positive"))
	}

	if err := s.storage.Transaction(ctx, func(ctx context.Context, storage store.Store) error {
		recordid := NewTransactionID()
		result := s.col.FindOneAndUpdate(ctx,
			bson.M{"name": wallet},
			pipelineAddBalance(amount),
			options.FindOneAndUpdate().SetReturnDocument(options.After),
		)
		if err := result.Err(); err != nil {
			return errors.NewNotFound("wallets", wallet)
		}
		after := &Wallet{}
		if err := result.Decode(after); err != nil {
			return err
		}
		record := &WalletTransaction{
			ObjectMeta:   store.ObjectMeta{Name: recordid},
			Amount:       amount,
			OrderID:      opts.OrderID,
			Summary:      opts.Summary,
			Balance:      after.Balance,
			Wallet:       wallet,
			Organization: opts.Organization,
			Operator:     opts.Operator,
			Kind:         WalletTransactionKindRefund,
			PaymentID:    opts.PaymentID,
		}
		if err := storage.Create(ctx, record); err != nil {
			if errors.IsConflict(err) {
				return ErrPaymentIDAlreadyExists
			}
			return err
		}
		transaction = record
		walletobj = after
		return nil
	}); err != nil {
		return nil, err
	}

	s.recorder.EventNoAggregateAnnotations(ctx, walletobj, types.ReasonWalletRefund, fmt.Sprintf("refund %0.2f", amount.Float64()), map[string]string{
		"delta":   amount.String(),
		"balance": walletobj.Balance.String(),
	})
	if walletobj.Balance.Sub(amount).IsNegative() {
		s.recorder.EventNoAggregateAnnotations(ctx, walletobj, types.ReasonWalletRestored, "wallet restored", map[string]string{
			"balance": walletobj.Balance.String(),
		})
		s.callback.OnWalletEvent(ctx, WalletEvent{
			Wallet:   wallet,
			Overdue:  walletobj.Overdue,
			Disabled: walletobj.Disabled,
		})
	}
	return transaction, nil
}

// GetTransaction implements WalletService.
func (s *walletService) GetTransaction(ctx context.Context, wallet string, transactionid string) (*WalletTransaction, error) {
	transaction := &WalletTransaction{}
	if err := s.storage.Get(ctx, transactionid, transaction, store.WithGetFieldRequirements(store.RequirementEqual("wallet", wallet))); err != nil {
		return nil, err
	}
	return transaction, nil
}

// GetTransactionByOrder implements WalletService.
func (s *walletService) GetTransactionByPaymentID(ctx context.Context, wallet string, paymentid string) (*WalletTransaction, error) {
	transaction := store.List[WalletTransaction]{}
	if err := s.storage.List(ctx, &transaction, store.WithFieldRequirements(
		store.RequirementEqual("wallet", wallet),
		store.RequirementEqual("paymentID", paymentid)),
	); err != nil {
		return nil, err
	}
	if len(transaction.Items) == 0 {
		return nil, errors.NewNotFound("wallets", wallet)
	}
	return &transaction.Items[0], nil
}
