package analytics_test

import (
	"context"
	"testing"
	"time"

	"xiaoshiai.cn/rune/pkg/iam/events"
	"xiaoshiai.cn/rune/pkg/pay"
	"xiaoshiai.cn/rune/pkg/pay/analytics"
	"xiaoshiai.cn/rune/pkg/pay/utils"
	"xiaoshiai.cn/rune/test/e2e/setup"
)

func TestAggrator_RunEveryDay(t *testing.T) {
	ctx := context.Background()

	mongostore, err := setup.SetupMongoStore(ctx)
	if err != nil {
		t.Fatalf("Failed to create mongo store: %v", err)
		return
	}
	etcdstore, err := setup.SetupEtcdStore(ctx)
	if err != nil {
		t.Fatalf("Failed to create etcd store: %v", err)
		return
	}
	paysystem, err := pay.New(ctx, etcdstore, mongostore, events.NoopRecorder{}, pay.NewDefaultOptions())
	if err != nil {
		t.Fatalf("Failed to create pay system: %v", err)
		return
	}
	a := analytics.NewOrderReportAggrator(mongostore, paysystem.OrderService, paysystem.BillingService)

	today := time.Now().Truncate(24 * time.Hour)
	yesterdayCycel := utils.Cycle{From: today, To: today.Add(24 * time.Hour)}

	if err := a.RunCycel(ctx, yesterdayCycel); err != nil {
		t.Errorf("Aggrator.RunEveryDay() error = %v", err)
	}
}
