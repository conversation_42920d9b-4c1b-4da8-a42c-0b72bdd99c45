package analytics

import (
	"context"
	"iter"
	"net/http"
	"slices"
	"time"

	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/base"
	"xiaoshiai.cn/rune/pkg/pay/product"
	"xiaoshiai.cn/rune/pkg/pay/types"
	"xiaoshiai.cn/rune/pkg/pay/utils"
)

type ReportAPI struct {
	Mongo store.Store
}

func NewAPI(mongo store.Store) *ReportAPI {
	return &ReportAPI{Mongo: mongo}
}

type OrganizationReportView struct {
	Summary ReportViewSummary `json:"summary"`
	Items   []ReportViewItem  `json:"items"`
}

type ReportViewSummary struct {
	Total types.Price      `json:"total"`
	Costs []SimeplCostItem `json:"costs"`
}

type SimeplCostItem struct {
	Name  string      `json:"name,omitempty"`
	Price types.Price `json:"price,omitempty"`
}

type ReportViewItem struct {
	Time         time.Time        `json:"time"`
	Tenant       string           `json:"tenant"`
	Organization string           `json:"organization"`
	Total        types.Price      `json:"total"`
	Costs        []SimeplCostItem `json:"costs"`
}

func (a *ReportAPI) GetReport(w http.ResponseWriter, r *http.Request) {
	base.OnSystemOrTenantOrTenantOrganization(w, r, func(ctx context.Context, tenant, org string) (any, error) {
		requirements := store.Requirements{}
		if tenant != "" {
			requirements = append(requirements, store.RequirementEqual("tenant", tenant))
		}
		if org != "" {
			requirements = append(requirements, store.RequirementEqual("organization", org))
		}
		from, to := api.Query(r, "from", time.Time{}), api.Query(r, "to", time.Time{})
		if !from.IsZero() {
			requirements = append(requirements, store.NewRequirement("from", store.GreaterThanOrEqual, from))
		}
		if !to.IsZero() {
			requirements = append(requirements, store.NewRequirement("to", store.LessThanOrEqual, to))
		}

		reports := store.List[OrderReport]{}
		if err := a.Mongo.List(ctx, &reports,
			store.WithSort("from"),
			store.WithFieldRequirements(requirements...)); err != nil {
			return nil, err
		}

		category := api.Query(r, "category", "")

		items := reports.Items
		if org == "" {
			items = MergeOrderReportByTime(items)
		}

		// Create a sequence of all cost items from all reports
		allItems := func(yield func(CostItem) bool) {
			for _, report := range items {
				for _, item := range report.Items {
					if !yield(item) {
						return
					}
				}
			}
		}
		sumfunc := func(i CostItem) (string, bool) {
			// sum by category
			if category != "" {
				if i.Category != category {
					return "", false
				}
				return i.Product, true
			}
			return i.Category, true
		}
		summary := ReportViewSummary{
			Total: sum(items),
			Costs: aggregateBy(allItems, sumfunc),
		}

		viewitems := make([]ReportViewItem, 0, len(items))
		for _, report := range items {
			viewitems = append(viewitems, ReportViewItem{
				Time:         report.From,
				Tenant:       report.Tenant,
				Organization: report.Organization,
				Total:        report.Total,
				Costs:        aggregateBy(slices.Values(report.Items), sumfunc),
			})
		}
		return OrganizationReportView{Summary: summary, Items: viewitems}, nil
	})
}

func sum(items []OrderReport) types.Price {
	total := types.NewPrice(0)
	for _, item := range items {
		total = total.Add(item.Total)
	}
	return total
}

func OnSystemOrTenantOrTenantOrganizationRequirements(w http.ResponseWriter, r *http.Request,
	fn func(ctx context.Context, reqs store.Requirements) (any, error),
) {
	api.On(w, r, func(ctx context.Context) (any, error) {
		reqs := store.Requirements{}
		if tenant := api.Path(r, "tenant", ""); tenant != "" {
			reqs = append(reqs, store.RequirementEqual("tenant", tenant))
		}
		if org := api.Path(r, "organization", ""); org != "" {
			reqs = append(reqs, store.RequirementEqual("organization", org))
		}
		return fn(ctx, reqs)
	})
}

func OnSystemOrTenantOrTenantOrganization(w http.ResponseWriter, r *http.Request,
	fn func(ctx context.Context, opttenant, optorg string) (any, error),
) {
	api.On(w, r, func(ctx context.Context) (any, error) {
		return fn(ctx, api.Path(r, "tenant", ""), api.Path(r, "organization", ""))
	})
}

func aggregateBy(items iter.Seq[CostItem], keyfunc func(i CostItem) (string, bool)) []SimeplCostItem {
	by := map[string]types.Price{}
	for item := range items {
		key, ok := keyfunc(item)
		if !ok {
			continue
		}
		by[key] = by[key].Add(item.Price)
	}
	ret := make([]SimeplCostItem, 0, len(by))
	for k, v := range by {
		ret = append(ret, SimeplCostItem{Name: k, Price: v})
	}
	return sortCostItems(ret)
}

func sortCostItems(items []SimeplCostItem) []SimeplCostItem {
	slices.SortFunc(items, func(a, b SimeplCostItem) int {
		return b.Price.Cmp(a.Price)
	})
	return items
}

func MergeOrderReportByTime(items []OrderReport) []OrderReport {
	bytime := map[utils.Cycle][]OrderReport{}
	for _, item := range items {
		bytime[utils.Cycle{From: item.From, To: item.To}] = append(bytime[utils.Cycle{From: item.From, To: item.To}], item)
	}
	ret := make([]OrderReport, 0, len(bytime))
	for cycel, items := range bytime {
		merge := OrderReport{
			From: cycel.From,
			To:   cycel.To,
		}
		for _, item := range items {
			merge.Total = merge.Total.Add(item.Total)
			merge.Items = append(merge.Items, item.Items...)
		}
		ret = append(ret, merge)
	}
	slices.SortFunc(ret, func(a, b OrderReport) int {
		return a.From.Compare(b.From)
	})
	return ret
}

func (a *ReportAPI) routes() []api.Route {
	listparams := []api.Param{
		api.QueryParam("from", "").Format("date-time").Optional(),
		api.QueryParam("to", "").Format("date-time").Optional(),
		api.QueryParam("category", "sum by category").In(
			product.CategoryApplication,
			product.CategoryCompute,
			product.CategoryPrivateNode,
		).Optional(),
	}
	return []api.Route{
		api.GET("").
			To(a.GetReport).
			Param(listparams...).
			Response(store.List[OrderReport]{}),
	}
}

func (a *ReportAPI) Group() api.Group {
	return api.NewGroup("").
		SubGroup(
			api.NewGroup("/orderreports").Route(
				a.routes()...,
			),
			base.NewTenantGroup("orderreports").Route(
				a.routes()...,
			),
			base.NewTenantOrganizationGroup("orderreports").Route(
				a.routes()...,
			),
		)
}
