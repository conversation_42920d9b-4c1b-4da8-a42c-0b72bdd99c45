package analytics

import (
	"context"
	"maps"
	"slices"
	"time"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/common/store/mongo"
	"xiaoshiai.cn/rune/pkg/pay/billing"
	"xiaoshiai.cn/rune/pkg/pay/order"
	"xiaoshiai.cn/rune/pkg/pay/types"
	"xiaoshiai.cn/rune/pkg/pay/utils"
)

const Day = 24 * time.Hour

func init() {
	mongo.GlobalObjectsScheme.Register(&OrderReport{}, mongo.ObjectDefination{
		Uniques: []mongo.UnionFields{
			{"name"},
		},
		Indexes: []mongo.UnionFields{
			{"from"},
			{"to"},
			{"tenant"},
			{"organization"},
		},
	})
}

type OrderReport struct {
	store.ObjectMeta `json:",inline"`
	From             time.Time   `json:"from,omitempty"`
	To               time.Time   `json:"to,omitempty"`
	Tenant           string      `json:"tenant"`
	Organization     string      `json:"organization"`
	Total            types.Price `json:"total"`
	Items            []CostItem  `json:"items"`
}

type CostItem struct {
	Category string      `json:"category,omitempty"`
	Product  string      `json:"product,omitempty"`
	Kind     string      `json:"kind,omitempty"`
	Price    types.Price `json:"price,omitempty"`
}

type Aggrator struct {
	mongo               store.Store
	order               order.OrderService
	bills               billing.BillingService
	MaxRecoveryDuration time.Duration
}

func NewOrderReportAggrator(mongo store.Store, orders order.OrderService, bills billing.BillingService) *Aggrator {
	maxRecoveryDuration := Day * 3
	return &Aggrator{
		mongo:               mongo,
		order:               orders,
		bills:               bills,
		MaxRecoveryDuration: maxRecoveryDuration,
	}
}

func (a *Aggrator) Run(ctx context.Context) error {
	runner := utils.ScheduleRunner{
		RunEveryDayFunc: a.RunEveryDay,
	}
	go a.recoveryIfNeeded(ctx)
	return runner.Run(ctx)
}

func (a *Aggrator) recoveryIfNeeded(ctx context.Context) error {
	log := log.FromContext(ctx)
	log.Info("recovery order report calculation")

	maxRecoveryDuration := a.MaxRecoveryDuration
	for _, cycle := range utils.CalcHistoryCyclesFromNow(maxRecoveryDuration, Day) {
		// check if already calculated
		log.Info("recovery order report calculation", "cycle", cycle)
		if err := a.RunCycel(ctx, cycle); err != nil {
			return err
		}
	}
	return nil
}

func (a *Aggrator) Name() string {
	return "OrderReportAggrator"
}

type CategoryProduct struct {
	Category string
	Product  string
	Kind     string
}

func (a *Aggrator) RunEveryDay(ctx context.Context) error {
	now := time.Now().Truncate(24 * time.Hour)
	return a.RunCycel(ctx, utils.Cycle{From: now.Add(-24 * time.Hour), To: now})
}

func (a *Aggrator) RunCycel(ctx context.Context, cycel utils.Cycle) error {
	log := log.FromContext(ctx)
	from, to := cycel.From, cycel.To

	orderlist, err := a.order.ListOrders(ctx, order.ListOrderOptions{
		State: order.OrderStatusPhasePaid, From: from, To: to,
		Kinds: []order.OrderKind{order.OrderKindNew, order.OrderKindRenew, order.OrderKindUpgrade, order.OrderKindDegrade},
	})
	if err != nil {
		return err
	}
	billslist, err := a.bills.ListBilling(ctx, billing.ListBillingOptions{State: billing.BillingRecordStatePaid, From: from, To: to})
	if err != nil {
		return err
	}
	type TenantOrganization struct {
		Tenant       string
		Organization string
	}

	byorg := map[TenantOrganization][]CostItem{}
	for _, order := range orderlist.Items {
		org := TenantOrganization{Tenant: order.Tenant, Organization: order.Organization}
		for _, item := range order.Items {
			it := CostItem{
				Category: item.SKU.Category,
				Product:  item.SKU.Product,
				Kind:     string(order.Kind),
				Price:    item.PayableAmount,
			}
			byorg[org] = append(byorg[org], it)
		}
	}
	for _, bill := range billslist.Items {
		org := TenantOrganization{Tenant: bill.Tenant, Organization: bill.Organization}
		for _, item := range bill.Items {
			it := CostItem{
				Category: item.SKU.Category,
				Product:  item.SKU.Product,
				Kind:     "PostPay",
				Price:    item.PayableAmount,
			}
			byorg[org] = append(byorg[org], it)
		}
	}

	for org, costs := range byorg {
		log.Info("aggrating order report", "tenant", org.Tenant, "organization", org.Organization, "orders", len(costs))
		bysku := map[CategoryProduct]CostItem{}
		total := types.NewPrice(0)
		for _, cost := range costs {
			if cost.Price.IsZero() {
				continue
			}
			key := CategoryProduct{
				Kind:     cost.Kind,
				Category: cost.Category,
				Product:  cost.Product,
			}
			bysum, ok := bysku[key]
			if !ok {
				bysum = CostItem{Category: key.Category, Product: key.Product, Kind: string(key.Kind)}
			}
			bysum.Price = bysum.Price.Add(cost.Price)

			total = total.Add(cost.Price)
			bysku[key] = bysum
		}
		report := &OrderReport{
			ObjectMeta:   store.ObjectMeta{Name: getReportName(org.Tenant, org.Organization, cycel)},
			From:         from,
			To:           to,
			Tenant:       org.Tenant,
			Organization: org.Organization,
			Total:        total,
			Items:        slices.Collect(maps.Values(bysku)),
		}
		if err := a.mongo.Create(ctx, report); err != nil {
			if errors.IsAlreadyExists(err) {
				log.Info("order report already exists", "report", report)
				continue
			}
			log.Error(err, "failed to create order report", "report", report)
		}
	}
	return nil
}

const DayOnlyTimeFormat = "20060102"

func getReportName(tenant, org string, cycel utils.Cycle) string {
	timstr := cycel.From.Format(DayOnlyTimeFormat)
	if tenant == "" {
		return timstr
	}
	if org == "" {
		return tenant + "-" + timstr
	}
	return tenant + "-" + org + "-" + timstr
}
