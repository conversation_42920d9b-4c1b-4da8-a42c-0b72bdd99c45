package billing

import (
	"context"
	"time"

	"xiaoshiai.cn/common/controller"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/pay/payment"
)

func NewBillingLocalProcess(mongo store.TransactionStore, billing *DefaultBillingService, payment payment.PaymentService) controller.Runable {
	c := &BillingLocalProcess{
		billing: billing,
		mongo:   mongo,
		payment: payment,
	}
	return controller.NewTypedController("billing-process", c).Watch(
		controller.WatchFuncSource[BillingRecord, string]{
			Store: mongo,
			WatchOptions: []store.WatchOption{
				store.WithSendInitialEvents(),
				store.WithWatchFieldRequirements(store.RequirementEqual("state", BillingRecordStateUnpaid)),
			},
			KeyFunc: func(ctx context.Context, kind store.WatchEventType, obj *BillingRecord) ([]string, error) {
				return []string{obj.Name}, nil
			},
		},
	)
}

type BillingLocalProcess struct {
	billing *DefaultBillingService
	mongo   store.TransactionStore

	payment payment.PaymentService
}

func (d *BillingLocalProcess) Reconcile(ctx context.Context, key string) (controller.Result, error) {
	billing, err := d.billing.GetBilling(ctx, key, GetBillingOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			return controller.Result{}, nil
		}
		return controller.Result{}, err
	}
	if err := d.onBilling(ctx, billing); err != nil {
		return controller.Result{}, err
	}
	if !billing.NotAfter.IsZero() {
		if billing.State == BillingRecordStateUnpaid {
			// check if the billing is due
			left := time.Until(billing.NotAfter)
			// billing is due
			if left <= 0 {
				if billing.State == BillingRecordStateDue {
					return controller.Result{}, nil
				}
				patch := store.MapMergePatch{
					"state":   BillingRecordStateDue,
					"dueTime": time.Now(),
				}
				if err := d.mongo.Patch(ctx, billing, patch); err != nil {
					return controller.Result{}, err
				}
				if err := d.sendEvent(ctx, BillingEvent{ID: billing.Name, State: BillingRecordStateDue}); err != nil {
					return controller.Result{}, err
				}
				return controller.Result{}, nil
			} else {
				// requeue before due
				return controller.Result{Requeue: true, RequeueAfter: left}, nil
			}
		}
	}
	return controller.Result{}, nil
}

func (d *BillingLocalProcess) onBilling(ctx context.Context, obj *BillingRecord) error {
	switch obj.State {
	case BillingRecordStatePaid:
		return d.sendEvent(ctx, BillingEvent{ID: obj.Name, State: BillingRecordStatePaid})
	case BillingRecordStateDue:
		return d.sendEvent(ctx, BillingEvent{ID: obj.Name, State: BillingRecordStateDue})
	case BillingRecordStateUnpaid:
		if paymentid := obj.PaymentID; paymentid != "" {
			paymentstatus, err := d.payment.PaymentStatus(ctx, paymentid)
			if err != nil {
				return err
			}
			return d.billing.OnPaymentEvent(ctx, *paymentstatus)
		}
		// no payment id, create a payment
		paymentoptions := payment.CreatePaymentOptions{
			AutoDebitAgreementID: payment.DefaultAutoDebitAgreementID,
			Description:          obj.Description,
			Creator:              obj.Operator,
			GoodName:             obj.Description,
			Organization:         obj.Organization,
		}
		payment, err := d.payment.CreatePayment(ctx, obj.Tenant, obj.Name, payment.Amount{Total: obj.PayableAmount}, paymentoptions)
		if err != nil {
			return err
		}
		obj.PaymentID = payment.ID
		return d.mongo.Update(ctx, obj)
	case BillingRecordStateCancelled:
		return d.sendEvent(ctx, BillingEvent{ID: obj.Name, State: BillingRecordStateCancelled})
	}
	return nil
}

func (d *BillingLocalProcess) sendEvent(ctx context.Context, e BillingEvent) error {
	if d.billing.callback != nil {
		if err := d.billing.callback.Callback(ctx, e); err != nil {
			return err
		}
	}
	return nil
}
