package paymentcallback

import (
	"context"
	"strings"
	"time"

	"k8s.io/apimachinery/pkg/util/wait"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/queue"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/pay/billing"
	"xiaoshiai.cn/rune/pkg/pay/order"
	"xiaoshiai.cn/rune/pkg/pay/payment"
	"xiaoshiai.cn/rune/pkg/pay/payment/wallet"
)

func NewPaymentEventsConsumer(store store.Store, paymentEvents queue.Queue, orderService order.OrderService, billing billing.BillingService) *PaymentEventConsumer {
	return &PaymentEventConsumer{
		store:          store,
		events:         paymentEvents,
		orderService:   orderService,
		billingService: billing,
	}
}

type PaymentEventConsumer struct {
	store          store.Store
	events         queue.Queue
	orderService   order.OrderService
	billingService billing.BillingService
}

// OnPaymentEvent implements payment.PaymentServiceCallback.
func (o *PaymentEventConsumer) OnPaymentEvent(ctx context.Context, status payment.PaymentEvent) error {
	if IsBillingID(status.OrderID) {
		return o.billingService.OnPaymentEvent(ctx, status)
	} else {
		return o.orderService.OnPaymentEvent(ctx, status)
	}
}

// OnRefundEvent implements payment.PaymentServiceCallback.
func (o *PaymentEventConsumer) OnRefundEvent(ctx context.Context, status payment.RefundEvent) error {
	return o.orderService.OnRefundEvent(ctx, status)
}

func (o *PaymentEventConsumer) Run(ctx context.Context) error {
	return wait.PollUntilContextCancel(ctx, 10*time.Second, true, func(ctx context.Context) (done bool, err error) {
		log.Info("payemnt event consumer running")
		return false, o.run(ctx)
	})
}

func (o *PaymentEventConsumer) Name() string {
	return "PaymentEventConsumer"
}

func (o *PaymentEventConsumer) run(ctx context.Context) error {
	return o.events.Consume(ctx, func(ctx context.Context, _ string, data []byte) error {
		return wallet.ProcessQueueEvent(ctx, data, o)
	}, queue.ConsumeOptions{MaxWorkers: 3})
}

// IsBillingID
// tell out if a id is a billing id
// billingid have a prefix "bill-", see [billing.NewBillingID]
func IsBillingID(id string) bool {
	return strings.HasPrefix(id, billing.BillingIDPrefix)
}
