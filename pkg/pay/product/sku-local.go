package product

import (
	"context"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	mongodriver "go.mongodb.org/mongo-driver/mongo"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/rand"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/common/store/mongo"
	"xiaoshiai.cn/rune/pkg/base"
	"xiaoshiai.cn/rune/pkg/pay/types"
)

type SKU struct {
	store.ObjectMeta  `json:",inline"`
	UpdationTimestamp store.Time   `json:"updationTimestamp"`
	Product           string       `json:"product"`
	Category          string       `json:"category"`
	Price             types.Price  `json:"price"`
	Unit              string       `json:"unit"`
	Enabled           bool         `json:"enabled"`
	Stock             int          `json:"stock"`
	StockPolicy       StockPolicy  `json:"stockPolicy"`
	RefundPolicy      RefundPolicy `json:"refundPolicy"`
	Histories         []SKUHistory `json:"histories"`
	Additional        string       `json:"additional"`
}

func localskuToSku(local *SKU) *SKUData {
	return &SKUData{
		Name:              local.Name,
		Alias:             local.Alias,
		Additional:        local.Additional,
		Description:       local.Description,
		Price:             local.Price,
		Enabled:           local.Enabled,
		Stock:             local.Stock,
		StockPolicy:       local.StockPolicy,
		Category:          local.Category,
		Product:           local.Product,
		CreationTimestamp: local.CreationTimestamp,
		Unit:              local.Unit,
		RefundPolicy:      local.RefundPolicy,
	}
}

type SKUHistory struct {
	Price          types.Price `json:"price"`
	StartTimestamp store.Time  `json:"startTimestamp"`
	EndTimestamp   store.Time  `json:"endTimestamp"`
	Operator       string      `json:"operator"`
}

func init() {
	mongo.GlobalObjectsScheme.Register(&SKU{},
		mongo.ObjectDefination{
			Uniques: []mongo.UnionFields{
				{"name", "product", "category"},
			},
		})
}

func NewSKUID() string {
	return "sku-" + rand.RandomNumeric(10)
}

func NewLocalStoreSKUService(mongodb *mongo.MongoStorage) *LocalStoreSKUService {
	return &LocalStoreSKUService{store: mongodb, col: mongodb.Database().Collection("skus")}
}

var _ SKUService = &LocalStoreSKUService{}

type LocalStoreSKUService struct {
	store store.Store
	col   *mongodriver.Collection
}

func (l *LocalStoreSKUService) CreateSKU(ctx context.Context, category, productid string, sku *SKUData) (*SKUData, error) {
	if sku.Price.IsNegative() {
		return nil, errors.NewBadRequest("price must not be negative")
	}
	name := sku.Name
	if name == "" {
		name = NewSKUID()
	}
	skuobj := &SKU{
		ObjectMeta: store.ObjectMeta{
			Name:        name,
			Alias:       sku.Alias,
			Description: sku.Description,
		},
		Additional:  sku.Additional,
		Enabled:     sku.Enabled,
		Stock:       sku.Stock,
		Price:       sku.Price,
		StockPolicy: sku.StockPolicy,
		Category:    category,
		Product:     productid,
	}
	if err := l.onProduct(category, productid).Create(ctx, skuobj); err != nil {
		return nil, err
	}
	return localskuToSku(skuobj), nil
}

func (l *LocalStoreSKUService) DeleteSKU(ctx context.Context, category, productid string, name string) error {
	sku := &SKU{
		ObjectMeta: store.ObjectMeta{Name: name},
	}
	return l.onProduct(category, productid).Delete(ctx, sku)
}

func (l *LocalStoreSKUService) checkMustHaveAtLeastOneEnabledSKU(ctx context.Context, category, productid string, exclude string) error {
	enabled, err := l.onProduct(category, productid).Count(ctx,
		&SKU{},
		func(co *store.CountOptions) {
			co.FieldRequirements = append(co.FieldRequirements, store.RequirementEqual("enabled", true))
			co.FieldRequirements = append(co.FieldRequirements, store.NewRequirement("name", store.NotEquals, exclude))
		},
	)
	if err != nil {
		return err
	}
	if enabled == 0 {
		return errors.NewBadRequest("must have at least one enabled sku")
	}
	return nil
}

func (l *LocalStoreSKUService) onProduct(cate, id string) store.Store {
	return l.store.Scope(
		store.Scope{Resource: "category", Name: cate},
		store.Scope{Resource: "product", Name: id},
	)
}

func (l *LocalStoreSKUService) GetSKU(ctx context.Context, category, productid string, name string) (*SKUData, error) {
	sku, err := l.getSKU(ctx, category, productid, name)
	if err != nil {
		return nil, err
	}
	return localskuToSku(sku), nil
}

func (l *LocalStoreSKUService) getSKU(ctx context.Context, category, productid string, name string) (*SKU, error) {
	localsku := &SKU{}
	if err := l.onProduct(category, productid).Get(ctx, name, localsku); err != nil {
		return nil, err
	}
	if localsku.Unit == "" {
		localsku.Unit = DefaultSKUUnit
	}
	return localsku, nil
}

func (l *LocalStoreSKUService) ListSKU(ctx context.Context, category, productid string, options *ListSKUOptions) (store.List[SKUData], error) {
	list := store.List[SKU]{}
	listoptions := base.ListOptionsToStoreListOptions(options.ListOptions)
	if !options.WithDisabled {
		listoptions = append(listoptions, store.WithFieldRequirements(store.RequirementEqual("enabled", true)))
	}
	if err := l.onProduct(category, productid).List(ctx, &list, listoptions...); err != nil {
		return store.List[SKUData]{}, err
	}
	for i := range list.Items {
		if list.Items[i].Unit == "" {
			list.Items[i].Unit = DefaultSKUUnit
		}
	}
	ret := store.List[SKUData]{
		Total: list.Total,
		Page:  list.Page,
		Size:  list.Size,
		Items: make([]SKUData, 0, len(list.Items)),
	}
	for _, sku := range list.Items {
		ret.Items = append(ret.Items, *localskuToSku(&sku))
	}
	return ret, nil
}

func (l *LocalStoreSKUService) UpdateSKU(ctx context.Context, category, productid string, name string, sku *SKUData) error {
	if sku.Price.IsNegative() {
		return errors.NewBadRequest("price must not be negative")
	}
	oldsku, err := l.getSKU(ctx, category, productid, name)
	if err != nil {
		return err
	}
	_ = setSKUPriceAndHistory(ctx, oldsku, sku.Price)
	oldsku.Alias = sku.Alias
	oldsku.StockPolicy = sku.StockPolicy
	oldsku.Additional = sku.Additional
	oldsku.Description = sku.Description
	oldsku.Enabled = sku.Enabled
	oldsku.Stock = sku.Stock
	oldsku.Unit = sku.Unit
	oldsku.RefundPolicy = sku.RefundPolicy

	if err := l.onProduct(category, productid).Update(ctx, oldsku); err != nil {
		return err
	}
	return nil
}

func (l *LocalStoreSKUService) GetSKUPrice(ctx context.Context, category, productid string, name string, date time.Time) (*SKUData, error) {
	localsku, err := l.getSKU(ctx, category, productid, name)
	if err != nil {
		return nil, err
	}
	if !localsku.Enabled {
		return nil, errors.NewNotFound("skus", name)
	}
	sku := localskuToSku(localsku)
	sku.Price = getSKUPriceAtTime(localsku, date)
	return sku, nil
}

func getSKUPriceAtTime(sku *SKU, date time.Time) types.Price {
	if date.IsZero() {
		return sku.Price
	}
	for _, history := range sku.Histories {
		if date.After(history.StartTimestamp.Time) && date.Before(history.EndTimestamp.Time) {
			return history.Price
		}
	}
	return sku.Price
}

func setSKUPriceAndHistory(ctx context.Context, sku *SKU, newprice types.Price) bool {
	if sku.Price.Equal(newprice) {
		return false
	}
	now := store.Now()
	updatetime := sku.UpdationTimestamp
	if updatetime.IsZero() {
		updatetime = sku.CreationTimestamp
	}
	newhistory := SKUHistory{
		Price:          sku.Price,
		StartTimestamp: updatetime,
		EndTimestamp:   now,
		Operator:       api.AuthenticateFromContext(ctx).User.Name,
	}
	sku.Histories = append([]SKUHistory{newhistory}, sku.Histories...)
	sku.Price = newprice
	sku.UpdationTimestamp = now
	return true
}

// SetSKUPrice implements SKUService.
func (l *LocalStoreSKUService) SetSKUPrice(ctx context.Context, category string, productid string, name string, price types.Price) error {
	oldsku, err := l.getSKU(ctx, category, productid, name)
	if err != nil {
		return err
	}
	if updated := setSKUPriceAndHistory(ctx, oldsku, price); !updated {
		return nil
	}
	if err := l.onProduct(category, productid).Update(ctx, oldsku); err != nil {
		return err
	}
	return nil
}

func (l *LocalStoreSKUService) IncreaseStock(ctx context.Context, category string, productid string, name string, quantity uint) error {
	oldsku, err := l.getSKU(ctx, category, productid, name)
	if err != nil {
		return err
	}
	var update bson.M
	switch oldsku.StockPolicy {
	case StockPolicyPerQuantity, "":
		// stock-=amount
		update = bson.M{"$inc": bson.M{"stock": int(quantity)}}
	case StockPolicyPerBatch:
		// stock--
		update = bson.M{"$inc": bson.M{"stock": 1}}
	case StockPolicyNone:
		// do nothing
		return nil
	}
	result, err := l.col.UpdateOne(ctx,
		bson.M{"name": name, "product": productid, "category": category},
		update,
	)
	if err != nil {
		return mongo.ConvetMongoError(err, l.col, name)
	}
	if result.MatchedCount == 0 {
		return errors.NewNotFound(l.col.Name(), name)
	}
	return nil
}

// IncreaseStock implements SKUService.
func (l *LocalStoreSKUService) ReduceStock(ctx context.Context, category string, productid string, name string, quantity uint) error {
	oldsku, err := l.getSKU(ctx, category, productid, name)
	if err != nil {
		return err
	}
	var update bson.M
	switch oldsku.StockPolicy {
	case StockPolicyPerQuantity, "":
		// stock-=amount
		update = bson.M{
			"$inc": bson.M{"stock": -int(quantity)},
		}
	case StockPolicyPerBatch:
		// stock--
		update = bson.M{
			"$inc": bson.M{"stock": -1},
		}
	case StockPolicyNone:
		// do nothing
		return nil
	}
	result, err := l.col.UpdateOne(ctx,
		bson.M{
			"name":     name,
			"product":  productid,
			"category": category,
		},
		update,
	)
	if err != nil {
		return mongo.ConvetMongoError(err, l.col, name)
	}
	if result.MatchedCount == 0 {
		return errors.NewNotFound(l.col.Name(), name)
	}
	return nil
}

// CheckStock implements SKUService.
func (l *LocalStoreSKUService) CheckStock(ctx context.Context, category string, productid string, name string, quantity uint) error {
	oldsku, err := l.getSKU(ctx, category, productid, name)
	if err != nil {
		return err
	}
	if !oldsku.Enabled {
		return errors.NewBadRequest("sku is disabled")
	}
	switch oldsku.StockPolicy {
	case StockPolicyPerQuantity, "":
		if oldsku.Stock < int(quantity) {
			return errors.NewBadRequest("stock is not enough")
		}
	case StockPolicyPerBatch:
		if oldsku.Stock <= 0 {
			return errors.NewBadRequest("stock is not enough")
		}
	}
	return nil
}

func (l *LocalStoreSKUService) DeltaStock(ctx context.Context, category string, productid string, name string, amount int) error {
	result, err := l.col.UpdateOne(ctx,
		bson.M{"name": name, "product": productid, "category": category},
		bson.M{"$inc": bson.M{"stock": amount}},
	)
	if err != nil {
		return mongo.ConvetMongoError(err, l.col, name)
	}
	if result.MatchedCount == 0 {
		return errors.NewNotFound(l.col.Name(), name)
	}
	return nil
}
