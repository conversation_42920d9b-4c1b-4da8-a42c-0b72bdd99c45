package product

import (
	"context"
	"time"

	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/pay/types"
)

const (
	CategoryPrivateNode = "privatenode"
	CategoryApplication = "market"  // app license
	CategoryCompute     = "compute" // compute resource
)

const DefaultSKUUnit = "Month"

type SKUReference struct {
	Category string `json:"category"`
	Product  string `json:"product"`
	Name     string `json:"name"`
}

func (s *SKUReference) Summary() string {
	if s == nil {
		return ""
	}
	return s.Category + "-" + s.Product + "-" + s.Name
}

func (s *SKUReference) Empty() bool {
	if s == nil {
		return true
	}
	return s.Name == "" || s.Category == "" || s.Product == ""
}

type SKUData struct {
	// Name is the "id" of the sku, it auto generate if not set when create
	Name              string          `json:"name"`
	Alias             string          `json:"alias"`
	Category          string          `json:"category"`
	Product           string          `json:"product"`
	CreationTimestamp store.Time      `json:"creationTimestamp"`
	Description       string          `json:"description"`
	Price             types.Price     `json:"price"`
	Enabled           bool            `json:"enabled"`
	Stock             int             `json:"stock"`
	Unit              string          `json:"unit"`
	StockPolicy       StockPolicy     `json:"stockPolicy"`
	RefundPolicy      RefundPolicy    `json:"refundPolicy"`
	ServiceDuration   ServiceDuration `json:"serviceDuration"`
	// Additional is use to store the additional information of the sku for caller when create the sku
	Additional string `json:"additional"`
}

type ServiceDuration string

const (
	ServiceDurationMonthly ServiceDuration = "Monthly"
	ServiceDurationYearly  ServiceDuration = "Yearly"
)

// RefundPolicy 退款策略
type RefundPolicy string

const (
	// RefundPolicyNotSupport 不支持退款
	RefundPolicyNotSupport RefundPolicy = "NotSupport"
	// RefundPolicyNoReason 无理由退款, 5 天内无理由退全款
	RefundPolicyNoReason RefundPolicy = "NoReason"
	// RefundPolicyProRated  按比例退款
	RefundPolicyProRated RefundPolicy = "ProRated"
	// RefundPolicyWithPenalty 违约金退款
	RefundPolicyWithPenalty RefundPolicy = "WithPenalty"
)

// StockPolicy 库存扣减策略
type StockPolicy string

const (
	// StockPolicyPerQuantity 按照数量扣减库存
	StockPolicyPerQuantity StockPolicy = "PerQuantity"
	// StockPolicyPerBatch 按照次数扣减库存
	StockPolicyPerBatch StockPolicy = "PerBatch"
	// StockPolicyNone 不扣减库存
	StockPolicyNone StockPolicy = "None"
)

type ListSKUOptions struct {
	api.ListOptions
	WithDisabled bool
}

type SKUService interface {
	CreateSKU(ctx context.Context, category, productid string, sku *SKUData) (*SKUData, error)
	ListSKU(ctx context.Context, category, productid string, options *ListSKUOptions) (store.List[SKUData], error)
	GetSKU(ctx context.Context, category, productid string, name string) (*SKUData, error)
	UpdateSKU(ctx context.Context, category, productid string, name string, sku *SKUData) error
	DeleteSKU(ctx context.Context, category, productid string, name string) error

	DeltaStock(ctx context.Context, category, productid, name string, changes int) error

	// GetSKUPrice get the sku price at the time
	// if time is zero, it will get the current price
	GetSKUPrice(ctx context.Context, category, productid string, name string, attime time.Time) (*SKUData, error)
	SetSKUPrice(ctx context.Context, category, productid string, name string, price types.Price) error

	// CheckStock check the stock of the sku
	CheckStock(ctx context.Context, category, productid, name string, quantity uint) error
	ReduceStock(ctx context.Context, category, productid, name string, quantity uint) error
	IncreaseStock(ctx context.Context, category, productid, name string, quantity uint) error
}
