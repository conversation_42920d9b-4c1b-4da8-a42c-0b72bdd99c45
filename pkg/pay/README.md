# Payment System

A comprehensive payment system that handles orders, payments, and related services.

## Modules

### Payment (`payment/`)

Core payment processing module that handles different payment channels.

Features:

- Supports multiple payment channels (e.g., Wallet)
- Handles payment lifecycle (create, pay, close, refund)
- Transaction management and status tracking
- Supports auto-debit agreements
- Delegate pattern for payment channel extensibility

### Order (`order/`)

Order management system that handles order creation, processing, and payment.

Features:

- Multiple order types support:
  - New purchase
  - Renewal
  - Upgrade
  - Degrade
  - Refund
  - Post-pay
- Order status tracking
- Integration with payment system
- Stock management
- Price calculation and discount application

### Product (`product/`)

SKU (Stock Keeping Unit) management system.

Features:

- SKU creation and management
- Stock tracking and policies
- Price management
- Support for different stock reduction policies:
  - PerQuantity: Reduce stock based on quantity
  - PerBatch: Reduce stock based on batch processing

### Promotion (`promotion`)

Promotion and discount management system.
This module handles the creation and application of discounts and promotions to orders.

Features:

- Global and specific discount rules
- Time-based discounts
- Tenant-specific discounts
- SKU-specific discounts
- Support for different order types

### Shipment (`shipment/`)

Order fulfillment and resource provisioning system.

Features:

- Handles post-payment resource provisioning
- Supports different resource types:
  - Market licenses
  - Private nodes
- Order event processing
- Integration with other platform services

### Wallet (`payment/wallet/`)

Internal wallet system for handling balance-based transactions.
