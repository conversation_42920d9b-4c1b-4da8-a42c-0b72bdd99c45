package payment

import (
	"context"
	"sync"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/store"
)

type Channel string

const (
	ChannelWallet Channel = "Wallet"
)

func NewPaymentChannelManager(store store.Store) *PaymentChannelManager {
	man := &PaymentChannelManager{
		Store:    store,
		channels: make(map[Channel]paymentChannelService),
	}
	return man
}

type PaymentChannelManager struct {
	Store    store.Store
	mu       sync.RWMutex
	channels map[Channel]paymentChannelService
}

func (p *PaymentChannelManager) AddPayment(ctx context.Context, channel Channel, svc PaymentService) error {
	p.mu.Lock()
	defer p.mu.Unlock()
	p.channels[channel] = paymentChannelService{channel: PaymentChannel{ObjectMeta: store.ObjectMeta{Name: string(channel)}}, Service: svc}
	return nil
}

func (p *PaymentChannelManager) RemovePayment(ctx context.Context, channel Channel) error {
	p.mu.Lock()
	defer p.mu.Unlock()
	delete(p.channels, channel)
	return nil
}

type PaymentChannel struct {
	store.ObjectMeta `json:",inline"`
	Params           map[string]string `json:"params,omitempty"`
}

type paymentChannelService struct {
	channel PaymentChannel
	Service PaymentService
}

func (p *PaymentChannelManager) ListChannels(ctx context.Context) ([]PaymentChannel, error) {
	p.mu.RLock()
	defer p.mu.RUnlock()
	channels := make([]PaymentChannel, 0, len(p.channels))
	for _, channel := range p.channels {
		channels = append(channels, channel.channel)
	}
	return channels, nil
}

func (p *PaymentChannelManager) GetPaymentService(ctx context.Context, channel Channel) (PaymentService, error) {
	p.mu.RLock()
	defer p.mu.RUnlock()
	ch, ok := p.channels[channel]
	if !ok {
		return nil, errors.NewNotFound("channel", string(channel))
	}
	return ch.Service, nil
}

func NewPaymentChannelContrller(manager *PaymentChannelManager) *PaymentChannelContrller {
	return &PaymentChannelContrller{Manager: manager}
}

type PaymentChannelContrller struct {
	Manager *PaymentChannelManager
}

// Run watch the channel configuration change and update the channel
func (p *PaymentChannelContrller) Run(ctx context.Context) error {
	<-ctx.Done()
	return nil
}

// Initialize init all payment channels
func (p *PaymentChannelContrller) Initialize(ctx context.Context) error {
	return nil
}
