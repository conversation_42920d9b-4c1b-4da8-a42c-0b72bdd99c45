package wallet

import (
	"context"
	"time"

	"xiaoshiai.cn/common/controller"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/pay/payment"
	"xiaoshiai.cn/rune/pkg/pay/wallet"
)

func NewPaymentProcess(svc *WalletPaymentService, mongo store.TransactionStore) controller.Runable {
	c := &PaymentProcess{
		mongo:                mongo,
		walletPaymentService: svc,
	}
	return controller.NewTypedController("wallet-payment-process", c).Watch(
		controller.WatchFuncSource[WalletPayment, string]{
			Store: mongo,
			WatchOptions: []store.WatchOption{
				store.WithSendInitialEvents(),
				store.WithWatchFieldRequirements(store.RequirementEqual("state", payment.PaymentStateNotPaid)),
			},
			KeyFunc: func(ctx context.Context, kind store.WatchEventType, obj *WalletPayment) ([]string, error) {
				return []string{obj.Name}, nil
			},
		},
	)
}

type PaymentProcess struct {
	walletPaymentService *WalletPaymentService
	mongo                store.TransactionStore
}

func (d *PaymentProcess) Reconcile(ctx context.Context, key string) (controller.Result, error) {
	wp := &WalletPayment{}
	if err := d.mongo.Get(ctx, key, wp); err != nil {
		if errors.IsNotFound(err) {
			return controller.Result{}, nil
		}
		return controller.Result{}, err
	}
	if wp.State != payment.PaymentStateNotPaid {
		return controller.Result{}, nil
	}
	if wp.AutoDebitAgreementID != "" {
		payopt := payment.PayPaymentOptions{
			Operator:      "system",
			AllowNegative: true,
		}
		if err := d.walletPaymentService.Pay(ctx, wp.Account, wp.Name, payopt); err != nil {
			return controller.Result{}, err
		}
		return controller.Result{}, nil
	}
	if wp.ExpireAt != nil {
		timeleft := time.Until(*wp.ExpireAt)
		if timeleft > 0 {
			return controller.Result{Requeue: true, RequeueAfter: timeleft}, nil
		}
		if err := d.walletPaymentService.ClosePayment(ctx, wp.Name); err != nil {
			return controller.Result{}, err
		}
	}
	return controller.Result{}, nil
}

func NewRefundProcess(svc *WalletPaymentService, mongo store.TransactionStore) controller.Runable {
	c := &RefundProcess{
		mongo:                mongo,
		walletPaymentService: svc,
	}
	return controller.NewTypedController("wallet-refund-process", c).Watch(
		controller.WatchFuncSource[WalletRefund, string]{
			Store: mongo,
			WatchOptions: []store.WatchOption{
				store.WithSendInitialEvents(),
				store.WithWatchFieldRequirements(store.RequirementEqual("state", payment.RefundStatePending)),
			},
			KeyFunc: func(ctx context.Context, kind store.WatchEventType, obj *WalletRefund) ([]string, error) {
				return []string{obj.Name}, nil
			},
		},
	)
}

type RefundProcess struct {
	walletPaymentService *WalletPaymentService
	mongo                store.TransactionStore
}

func (d *RefundProcess) Reconcile(ctx context.Context, key string) (controller.Result, error) {
	wp := &WalletRefund{}
	if err := d.mongo.Get(ctx, key, wp); err != nil {
		if errors.IsNotFound(err) {
			return controller.Result{}, nil
		}
		return controller.Result{}, err
	}
	switch wp.State {
	case payment.RefundStatePending:
		if err := d.doRefund(ctx, wp); err != nil {
			return controller.Result{}, err
		}
		return controller.Result{}, nil
	}
	return controller.Result{}, nil
}

func (d *RefundProcess) doRefund(ctx context.Context, walletrefund *WalletRefund) error {
	refundOptions := wallet.RefundOptions{
		PaymentID:    walletrefund.Name,
		OrderID:      walletrefund.OrderID,
		Summary:      walletrefund.GoodName,
		Reason:       walletrefund.Reason,
		Operator:     walletrefund.Operator,
		Organization: walletrefund.Organization,
	}
	transaction, err := d.walletPaymentService.Wallet.Refund(ctx, walletrefund.Account, walletrefund.Amount, refundOptions)
	if err != nil {
		if err == wallet.ErrPaymentIDAlreadyExists {
			existstransaction, err := d.walletPaymentService.Wallet.GetTransactionByPaymentID(ctx, walletrefund.Account, walletrefund.PaymentID)
			if err != nil {
				return err
			}
			transaction = existstransaction
		} else {
			return err
		}
	}
	patch := store.MapMergePatch{
		"state":      payment.RefundStateSuccess,
		"paiedTime":  transaction.CreationTimestamp.Time,
		"refundTime": transaction.CreationTimestamp.Time,
		"payer":      transaction.Operator,
	}
	if err := d.mongo.Patch(ctx, walletrefund, patch); err != nil {
		return err
	}
	d.walletPaymentService.sendRefundEvent(context.Background(), walletRefundToRefundEvent(walletrefund))
	return nil
}
