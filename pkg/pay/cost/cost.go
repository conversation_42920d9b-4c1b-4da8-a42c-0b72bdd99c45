package cost

import (
	"context"

	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/pay/product"
	"xiaoshiai.cn/rune/pkg/pay/types"
)

type EstimateCostOptions struct {
	Tenant       string                    `json:"tenant,omitempty"`
	Organization string                    `json:"organization,omitempty"`
	Period       ResourcePeriod            `json:"period,omitempty"`
	Cluster      store.ObjectReference     `json:"cluster,omitempty"`
	Items        []EstimateCostOptionsItem `json:"items,omitempty"`
}

type EstimateCostOptionsItem struct {
	Labels          map[string]string `json:"labels,omitempty"`
	MeasurementName string            `json:"measurementName,omitempty"`
	Quantity        float64           `json:"quantity,omitempty"`
}

type EstimateCostResult struct {
	OriginalAmount types.Price              `json:"originalAmount,omitempty"`
	PayableAmount  types.Price              `json:"payableAmount,omitempty"`
	Items          []EstimateCostResultItem `json:"items,omitempty"`
}

type EstimateCostResultItem struct {
	SKU            product.SKUReference `json:"sku,omitempty"`
	Period         ResourcePeriod       `json:"period,omitempty"`
	UnitPrice      types.Price          `json:"unitPrice,omitempty"`
	Unit           string               `json:"unit,omitempty"`
	Quantity       float64              `json:"quantity,omitempty"`
	OriginalAmount types.Price          `json:"originalAmount,omitempty"`
	PayableAmount  types.Price          `json:"payableAmount,omitempty"`
}
type ResourcePeriod string

const (
	ResourcePeriodHour  ResourcePeriod = "hour"
	ResourcePeriodDay   ResourcePeriod = "day"
	ResourcePeriodWeek  ResourcePeriod = "week"
	ResourcePeriodMonth ResourcePeriod = "month"
)

type CostService interface {
	EstimateResourceCost(ctx context.Context, options EstimateCostOptions) (*EstimateCostResult, error)
}

type DummyCostService struct{}

func (s DummyCostService) EstimateResourceCost(ctx context.Context, options EstimateCostOptions) (*EstimateCostResult, error) {
	return &EstimateCostResult{
		OriginalAmount: types.NewPrice(0),
		PayableAmount:  types.NewPrice(0),
		Items:          []EstimateCostResultItem{},
	}, nil
}
