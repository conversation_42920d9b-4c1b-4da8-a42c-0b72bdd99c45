package order

import (
	"context"
	"time"

	"xiaoshiai.cn/common/controller"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/pay/payment"
)

func NewLocalOrderProcess(mongo store.TransactionStore, payman *payment.PaymentChannelManager, svc *LocalOrderService) controller.Runable {
	c := &LocalOrderProcess{
		LocalOrderService:     svc,
		PaymentChannelManager: payman,
	}
	return controller.NewTypedController("order-process", c).
		Watch(
			controller.WatchFuncSource[Order, string]{
				Store: mongo,
				WatchOptions: []store.WatchOption{
					store.WithSendInitialEvents(),
					store.WithWatchFieldRequirements(store.Requirement{
						Key:      "state",
						Operator: store.Equals,
						Values:   []any{OrderStatusPhaseNotPaid},
					}),
				},
				KeyFunc: func(ctx context.Context, kind store.WatchEventType, obj *Order) ([]string, error) {
					return []string{obj.Name}, nil
				},
			},
		)
}

type LocalOrderProcess struct {
	LocalOrderService     *LocalOrderService
	PaymentChannelManager *payment.PaymentChannelManager
}

func (c *LocalOrderProcess) Reconcile(ctx context.Context, key string) (controller.Result, error) {
	order, err := c.LocalOrderService.GetOrder(ctx, key, GetOrderOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			return controller.Result{}, nil
		}
		return controller.Result{}, err
	}
	if order.Kind == OrderKindRefund {
		return c.proceeeRefundOrderOnce(ctx, order)
	}
	if order.State != OrderStatusPhaseNotPaid {
		return controller.Result{}, nil
	}
	if order.AutoPay && order.State == OrderStatusPhaseNotPaid {
		return c.proceeeAutoPayOrderOnce(ctx, order)
	}
	if order.State == OrderStatusPhaseNotPaid && order.ExpireAt != nil {
		if timetoexpire := time.Until(*order.ExpireAt); timetoexpire > 0 {
			return controller.Result{Requeue: true, RequeueAfter: timetoexpire}, nil
		}
		log.FromContext(ctx).Info("order expired", "order", order.Name, "tenant", order.Tenant)
		if err := c.LocalOrderService.CloseOrder(ctx, order.Name, CloseOrderOptions{}); err != nil {
			return controller.Result{}, err
		}
	}
	return controller.Result{}, nil
}

func (c *LocalOrderProcess) proceeeRefundOrderOnce(ctx context.Context, order *Order) (controller.Result, error) {
	if err := c.proceeeRefundOrder(ctx, order); err != nil {
		patch := store.MapMergePatch{
			"state":   OrderStatusPhaseError,
			"message": err.Error(),
		}
		if err := c.LocalOrderService.Mongo.Patch(ctx, order, patch); err != nil {
			return controller.Result{}, err
		}
		return controller.Result{}, err
	}
	return controller.Result{}, nil
}

func (c *LocalOrderProcess) proceeeRefundOrder(ctx context.Context, order *Order) error {
	originalorder, err := c.LocalOrderService.GetOrder(ctx, order.OriginalOrder, GetOrderOptions{})
	if err != nil {
		return err
	}
	paymentid, paymentchannel := originalorder.Payment.ID, originalorder.Payment.Channel
	paymentsvc, err := c.PaymentChannelManager.GetPaymentService(ctx, payment.Channel(paymentchannel))
	if err != nil {
		return err
	}

	// check if refund already exists
	existsrefunds, err := paymentsvc.ListRefundByOrder(ctx, order.Name)
	if err != nil {
		if !errors.IsNotFound(err) {
			return err
		}
	}
	var refund *payment.Refund
	// currently one order only allow a refund.
	if len(existsrefunds) == 0 {
		refundopts := payment.RefundOptions{
			Reason:       order.Description,
			Operator:     order.Creator,
			Organization: order.Organization,
		}
		newrefund, err := paymentsvc.CreateRefund(ctx, paymentid, order.Name, payment.Amount{Total: order.PayableAmount.Neg()}, refundopts)
		if err != nil {
			return err
		}
		refund = newrefund
	} else {
		refund = &existsrefunds[0]
	}

	if refund.ID == order.Payment.ID {
		return nil
	}

	patch := store.MapMergePatch{
		"payment": map[string]any{
			"id":      refund.ID,
			"channel": paymentchannel,
		},
	}
	// once refund created, refund event may received immediately
	// use patch instead of update to avoid override the event state
	if err := c.LocalOrderService.Mongo.Patch(ctx, order, patch); err != nil {
		return err
	}
	return nil
}

func (c *LocalOrderProcess) proceeeAutoPayOrderOnce(ctx context.Context, order *Order) (controller.Result, error) {
	if err := c.LocalOrderService.PayOrder(ctx, order.Name, PayOrderOptions{}); err != nil {
		return controller.Result{}, err
	}
	return controller.Result{}, nil
}
