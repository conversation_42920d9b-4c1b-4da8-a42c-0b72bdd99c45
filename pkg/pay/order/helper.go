package order

import (
	"context"

	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/rune/pkg/pay/product"
)

type CreateOrderRequest struct {
	Tenant       string                 `json:"tenant"`
	Organization string                 `json:"organization"`
	Kind         OrderKind              `json:"kind"`
	Description  string                 `json:"description"`
	Items        []CreateOrderOrderItem `json:"items"`
}

func CreateOrderFromSkuOptions(ctx context.Context, orders OrderService, skus product.SKUService, options CreateOrderRequest) (*Order, error) {
	orderoptions := CreateOrderOptions{
		Creator:      api.AuthenticateFromContext(ctx).User.Name,
		Tenant:       options.Tenant,
		Kind:         options.Kind,
		Organization: options.Organization,
		Description:  options.Description,
		Items:        options.Items,
	}
	return orders.CreateOrder(ctx, orderoptions)
}

func EstimateOrderFromSkuOptions(ctx context.Context, orders OrderService, skus product.SKUService, options CreateOrderRequest) (*Order, error) {
	orderoptions := CreateOrderOptions{
		Creator:      api.AuthenticateFromContext(ctx).User.Name,
		Tenant:       options.Tenant,
		Kind:         options.Kind,
		Organization: options.Organization,
		Description:  options.Description,
		Items:        options.Items,
	}
	return orders.EstimateOrder(ctx, orderoptions)
}
