package order

import (
	"context"
	"time"

	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/pay/payment"
	"xiaoshiai.cn/rune/pkg/pay/product"
	"xiaoshiai.cn/rune/pkg/pay/types"
)

type OrderKind string

// 订单类型：
// 1. 新购：采购或开通某种产品或者服务
// 2. 续订：用户通过付费延长资源的有效使用时长
// 3. 升级：付费在有效期内提升产品规格、功能或者服务
// 4. 降级：在有效期内主动降低产品或者服务的配置
// 5. 退订：发起释放资源并按情况退还相应的费用
const (
	OrderKindNew     OrderKind = "New"
	OrderKindRenew   OrderKind = "Renew"
	OrderKindUpgrade OrderKind = "Upgrade"
	OrderKindDegrade OrderKind = "Degrade"
	OrderKindRefund  OrderKind = "Refund"
)

type Order struct {
	store.ObjectMeta `json:",inline"`

	Deleted bool        `json:"deleted,omitempty"`
	Kind    OrderKind   `json:"kind"`
	Items   []OrderItem `json:"items"`

	OriginalAmount types.Price `json:"originalAmount"`
	PayableAmount  types.Price `json:"payableAmount"`

	Creator      string `json:"creator"`
	Tenant       string `json:"tenant"`
	Organization string `json:"organization"`

	ExpireAt *time.Time `json:"expireAt,omitempty"`

	// OriginalOrder is set when the order is a refund order
	OriginalOrder string `json:"originalOrder,omitempty"`

	AutoPay bool `json:"autoPay,omitempty"`

	// State is the order state
	State      OrderState    `json:"state,omitempty"`
	Message    string        `json:"message,omitempty"`
	ClosedTime *time.Time    `json:"closedTime,omitempty"`
	Payment    OrderPayment  `json:"payment"`
	Deliver    OrderDeliver  `json:"deliver,omitempty"`
	Discount   OrderDiscount `json:"discount,omitempty"`
	Refund     OrderRefund   `json:"refund,omitempty"`
}

type OrderItem struct {
	ID          string               `json:"id,omitempty"`
	Kind        OrderKind            `json:"kind,omitempty"`
	Summary     string               `json:"summary,omitempty"`
	Description string               `json:"description,omitempty"`
	SKU         product.SKUReference `json:"sku,omitempty"`

	Quantity  float64     `json:"quantity"`
	UnitPrice types.Price `json:"unitPrice"`
	Unit      string      `json:"unit,omitempty"`

	PayableAmount  types.Price `json:"payableAmount"`
	OriginalAmount types.Price `json:"originalAmount"`

	Discount float64 `json:"discount,omitempty"`

	StartTime time.Time `json:"startTime,omitempty"`
	EndTime   time.Time `json:"endTime,omitempty"`

	RefundPolicy product.RefundPolicy `json:"refundPolicy,omitempty"`

	// Reference is optional, it point to the object this item related to
	// it used in order renewal/refund, it is the reference of the object
	Reference store.ResourcedObjectReference `json:"reference,omitempty"`

	// SKUAdditional is the snapshot of the sku.additional
	SKUAdditional string `json:"skuAdditional,omitempty"`

	// Params pass to shipment
	Params map[string]string `json:"params,omitempty"`

	// refund order item's field
	OriginalOrder string    `json:"originalOrder,omitempty"`
	OriginalItem  string    `json:"originalItem,omitempty"`
	OriginalKind  OrderKind `json:"originalKind,omitempty"`
}

type OrderState string

const (
	OrderStatusPhaseNotPaid  OrderState = "NotPaid"
	OrderStatusPhasePaid     OrderState = "Paid"
	OrderStatusPhaseClosed   OrderState = "Closed"
	OrderStatusPhaseFinished OrderState = "Finished"
	OrderStatusPhaseRefunded OrderState = "Refunded"
	OrderStatusPhaseError    OrderState = "Error"
)

type OrderDiscount struct {
	ID string `json:"id,omitempty"`
}

type OrderRefund struct {
	ID       string     `json:"id,omitempty"`
	Channel  string     `json:"channel,omitempty"`
	Paid     bool       `json:"paid,omitempty"`
	PaidTime *time.Time `json:"paidTime,omitempty"`
}

type OrderDeliver struct {
	ID           string     `json:"id,omitempty"`
	State        string     `json:"state,omitempty"`
	CompleteTime *time.Time `json:"completeTime,omitempty"`
}

type OrderPayment struct {
	ID       string     `json:"id"`
	Channel  string     `json:"channel"`
	Paid     bool       `json:"paid"`
	PaidTime *time.Time `json:"paidTime"`
}

type CreateOrderOptions struct {
	Kind         OrderKind              `json:"kind"`
	Creator      string                 `json:"creator"`
	Tenant       string                 `json:"tenant"`
	Organization string                 `json:"organization"`
	Description  string                 `json:"description"`
	Items        []CreateOrderOrderItem `json:"items"`
	AutoPay      bool                   `json:"autoPay"`
	Timeout      time.Duration          `json:"timeout"`
}

type CreateOrderOrderItem struct {
	Summary  string               `json:"summary,omitempty"`
	SKU      product.SKUReference `json:"sku,omitempty"`
	Quantity float64              `json:"quantity"`
	// Reference is optional, it point to the object this item related to
	// it used in order renewal/refund, it is the reference of the object
	// which will be used to calc the duration of the order item
	Reference store.ResourcedObjectReference `json:"reference,omitempty"`
	// Params pass to shipment
	Params map[string]string `json:"params,omitempty"`
}

type ListOrderOptions struct {
	api.ListOptions `json:",inline"`
	State           OrderState  `json:"phase"`
	Kinds           []OrderKind `json:"kinds"`
	Creator         string      `json:"creator"`
	Tenant          string      `json:"tenant"`
	Organization    string      `json:"organization"`
	From            time.Time   `json:"from"`
	To              time.Time   `json:"to"`
	WithDeleted     bool        `json:"withDeleted"`

	// Reference is optional, list orders contains this reference
	Reference store.ResourcedObjectReference `json:"reference,omitempty"`

	SKU product.SKUReference `json:"sku,omitempty"`
}

type GetOrderOptions struct {
	Tenant       string `json:"tenant"`
	Organization string `json:"organization"`
}

type DeleteOrderOptions struct {
	Tenant       string `json:"tenant"`
	Organization string `json:"organization"`
}

type PayOrderOptions struct {
	Channel  string `json:"channel"`
	Operator string `json:"operator"`
	// IgnoreStock disable stock check/reduce
	IgnoreStock bool `json:"ignoreStock"`
}

type CloseOrderOptions struct {
	Tenant       string `json:"tenant"`
	Organization string `json:"organization"`
}

type OrderServiceCallback interface {
	Callback(ctx context.Context, event OrderEvent) error
}

type CalculateRefundOrderResult struct {
	PayableAmount types.Price               `json:"payableAmount"`
	Items         []EstimateRefundOrderItem `json:"items"`
}

type EstimateRefundOrderItem struct {
	Description    string                         `json:"description,omitempty"`
	SKU            product.SKUReference           `json:"sku,omitempty"`
	Quantity       float64                        `json:"quantity"`
	PayableAmount  types.Price                    `json:"payableAmount"`
	OriginalAmount types.Price                    `json:"originalAmount"`
	Reference      store.ResourcedObjectReference `json:"reference,omitempty"`
}

type EstimateOrder struct {
	Kind           OrderKind   `json:"kind"`
	Tenant         string      `json:"tenant"`
	Organization   string      `json:"organization"`
	Description    string      `json:"description"`
	Items          []OrderItem `json:"items"`
	OriginalAmount types.Price `json:"originalAmount"`
	PayableAmount  types.Price `json:"payableAmount"`
}

type ShipmentOptions struct {
	Reference store.ResourcedObjectReference `json:"reference,omitempty"`
	From      time.Time                      `json:"from,omitempty"`
	To        time.Time                      `json:"to,omitempty"`
}

type CreateRenewalOrderOptions struct {
	Creator      string                   `json:"creator"`
	Tenant       string                   `json:"tenant"`
	Organization string                   `json:"organization"`
	Description  string                   `json:"description"`
	Items        []CreateRenewalOrderItem `json:"items"`
	Timeout      time.Duration            `json:"timeout"`
	// AutoPay will auto pay the order after created
	AutoPay bool `json:"autoPay"`
}

type CreateRenewalOrderItem struct {
	Quantity float64 `json:"quantity"`
	// Reference is optional, it point to the object this item related to
	// it used in order renewal/refund, it is the reference of the object
	// which will be used to calc the duration of the order item
	Reference store.ResourcedObjectReference `json:"reference,omitempty"`
	// Params pass to shipment
	Params map[string]string `json:"params,omitempty"`
}

type CreateRefundOrderOptions struct {
	Creator      string                  `json:"creator"`
	Tenant       string                  `json:"tenant"`
	Organization string                  `json:"organization"`
	Description  string                  `json:"description"`
	Items        []CreateRefundOrderItem `json:"items"`
	Timeout      time.Duration           `json:"timeout"`
}

type CreateRefundOrderItem struct {
	// Reference is optional, it point to the object this item related to
	// it used in order renewal/refund, it is the reference of the object
	// which will be used to calc the duration of the order item
	Reference store.ResourcedObjectReference `json:"reference,omitempty"`
	// Params pass to shipment
	Params map[string]string `json:"params,omitempty"`
}

type OrderService interface {
	OnPaymentEvent(ctx context.Context, event payment.PaymentEvent) error
	OnRefundEvent(ctx context.Context, event payment.RefundEvent) error

	CreateOrder(ctx context.Context, options CreateOrderOptions) (*Order, error)
	EstimateOrder(ctx context.Context, options CreateOrderOptions) (*Order, error)

	ListOrders(ctx context.Context, options ListOrderOptions) (store.List[Order], error)
	GetOrder(ctx context.Context, id string, options GetOrderOptions) (*Order, error)
	DeleteOrder(ctx context.Context, id string, options DeleteOrderOptions) error
	CloseOrder(ctx context.Context, id string, options CloseOrderOptions) error

	PayOrder(ctx context.Context, id string, options PayOrderOptions) error

	ShipmentOrderItem(ctx context.Context, id string, itemid string, options ShipmentOptions) error
}
