package promotion

import (
	"context"
	"net/http"
	"slices"
	"time"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/common/store/mongo"
	"xiaoshiai.cn/rune/pkg/base"
	"xiaoshiai.cn/rune/pkg/pay/product"
	"xiaoshiai.cn/rune/pkg/pay/types"
)

func InitDiscounts(ctx context.Context, mongo store.Store) error {
	discounts := []Discount{
		{
			ObjectMeta: store.ObjectMeta{
				Name:        "global",
				Description: "Global discount",
			},
			Ratio:   0.95,
			Enabled: true,
		},
	}

	for _, discount := range discounts {
		if err := store.CreateIfNotExists(ctx, mongo, &discount); err != nil {
			return err
		}
	}
	return nil
}

type CalculateDiscountOptions struct {
	PromotionCodes []string                `json:"promotionCodes"`
	Annotations    map[string]string       `json:"annotations"`
	Items          []CalculateDiscountItem `json:"items"`
	Tenant         string                  `json:"tenant"`
	Organization   string                  `json:"organization"`
	Kind           string                  `json:"kind"`
	Round          int32                   `json:"round"`
}

type CalculateDiscountItem struct {
	SKU       product.SKUReference `json:"sku,omitempty"`
	Quantity  float64              `json:"quantity,omitempty"`
	UnitPrice types.Price          `json:"unitPrice,omitempty"`
}

type CalculateDiscountResult struct {
	ID             string         `json:"id"`
	OriginalAmount types.Price    `json:"originalAmount"`
	PayableAmount  types.Price    `json:"payableAmount"`
	Items          []DiscountItem `json:"items"`
}

type DiscountItem struct {
	SKU            product.SKUReference `json:"sku,omitempty"`
	Quantity       float64              `json:"quantity,omitempty"`
	UnitPrice      types.Price          `json:"unitPrice,omitempty"`
	OriginalAmount types.Price          `json:"originalAmount,omitempty"`
	PayableAmount  types.Price          `json:"payableAmount,omitempty"`
	Discount       float64              `json:"discount,omitempty"`
}

type EstimateDiscountResult struct {
	OriginalAmount types.Price    `json:"originalAmount"`
	PayableAmount  types.Price    `json:"payableAmount"`
	Items          []DiscountItem `json:"items"`
}

type DiscountService interface {
	EstimateDiscount(ctx context.Context, account string, options CalculateDiscountOptions) (*EstimateDiscountResult, error)

	CalculateDiscount(ctx context.Context, account string, options CalculateDiscountOptions) (*CalculateDiscountResult, error)
	ConfirmDiscount(ctx context.Context, account string, id string) error
	CancelDiscount(ctx context.Context, account string, id string) error
}

type OrderKind string

func NewDefaultDiscountService(mongo store.Store) DiscountService {
	return &DefaultDiscountService{Mongo: mongo}
}

type DefaultDiscountService struct {
	Mongo store.Store
}

func (s *DefaultDiscountService) CalculateDiscount(ctx context.Context, account string, req CalculateDiscountOptions) (*CalculateDiscountResult, error) {
	result, err := s.EstimateDiscount(ctx, account, req)
	if err != nil {
		return nil, err
	}
	ret := &CalculateDiscountResult{
		ID:             "",
		OriginalAmount: result.OriginalAmount,
		PayableAmount:  result.PayableAmount,
		Items:          result.Items,
	}
	return ret, nil
}

// CancelDiscount implements DiscountService.
func (s *DefaultDiscountService) CancelDiscount(ctx context.Context, account string, id string) error {
	return nil
}

// ConfirmDiscount implements DiscountService.
func (s *DefaultDiscountService) ConfirmDiscount(ctx context.Context, account string, id string) error {
	return nil
}

func (s *DefaultDiscountService) EstimateDiscount(ctx context.Context, account string, req CalculateDiscountOptions) (*EstimateDiscountResult, error) {
	now := time.Now()
	requirements := store.Requirements{store.RequirementEqual("enabled", true)}

	discountlist := &store.List[Discount]{}
	if err := s.Mongo.List(ctx, discountlist, store.WithFieldRequirements(requirements...)); err != nil {
		return nil, err
	}
	matchorderPolicies := []Discount{}
	for _, discount := range discountlist.Items {
		if !discount.Enabled {
			continue
		}
		if !matchCond(discount.Tenants, req.Tenant) {
			continue
		}
		if !matchCond(discount.OrderKinds, req.Kind) {
			continue
		}
		if !discount.StartTime.IsZero() && discount.StartTime.After(now) {
			continue
		}
		if !discount.EndTime.IsZero() && discount.EndTime.Time.Before(now) {
			continue
		}
		matchorderPolicies = append(matchorderPolicies, discount)
	}

	resultitems := make([]DiscountItem, 0, len(req.Items))
	totalorignal, totalpayable := types.NewPrice(0), types.NewPrice(0)
	for _, item := range req.Items {
		ratio := 1.0
		for _, discount := range matchorderPolicies {
			if !matchSKU(discount.SKUs, item.SKU) {
				continue
			}
			ratio = min(ratio, discount.Ratio)
		}
		it := DiscountItem{
			SKU:            item.SKU,
			Quantity:       item.Quantity,
			UnitPrice:      item.UnitPrice,
			OriginalAmount: item.UnitPrice.Mul(types.NewPrice(item.Quantity)),
			Discount:       ratio,
		}
		if req.Round > 0 {
			it.OriginalAmount = it.OriginalAmount.Round(req.Round)
		}
		it.PayableAmount = it.OriginalAmount.Mul(types.NewPrice(ratio))
		if req.Round > 0 {
			it.PayableAmount = it.PayableAmount.Round(req.Round)
		}
		totalorignal = totalorignal.Add(it.OriginalAmount)
		totalpayable = totalpayable.Add(it.PayableAmount)
		resultitems = append(resultitems, it)
	}
	ret := &EstimateDiscountResult{
		Items:          resultitems,
		OriginalAmount: totalorignal,
		PayableAmount:  totalpayable,
	}
	return ret, nil
}

func matchCond[T comparable](cond []T, test T) bool {
	return len(cond) == 0 || slices.Contains(cond, test)
}

func matchSKU(cond []product.SKUReference, test product.SKUReference) bool {
	if len(cond) == 0 {
		return true
	}
	for _, sku := range cond {
		if sku.Category != "*" && sku.Category != test.Category {
			continue
		}
		if sku.Product != "*" && sku.Product != test.Product {
			continue
		}
		if sku.Name != "*" && sku.Name != test.Name {
			continue
		}
		return true
	}
	return false
}

func init() {
	mongo.GlobalObjectsScheme.Register(&Discount{},
		mongo.ObjectDefination{
			Uniques: []mongo.UnionFields{
				{"name"},
			},
			Indexes: []mongo.UnionFields{
				{"tenants"},
				{"orderKinds"},
				{"enabled"},
			},
		})
}

type Discount struct {
	store.ObjectMeta `json:",inline"`
	Ratio            float64                `json:"ratio"`
	StartTime        store.Time             `json:"startTime,omitempty"`
	EndTime          store.Time             `json:"endTime,omitempty"`
	Tenants          []string               `json:"tenants"`
	SKUs             []product.SKUReference `json:"skus"`
	OrderKinds       []string               `json:"orderKinds"`
	Enabled          bool                   `json:"enabled,omitempty"`
}

type API struct {
	Mongo store.Store
}

func NewAPI(mongo store.Store) *API {
	return &API{Mongo: mongo}
}

func (s *API) ListDiscounts(w http.ResponseWriter, r *http.Request) {
	s.on(w, r, func(ctx context.Context, storage store.Store) (any, error) {
		reqlistopetions := api.GetListOptions(r)
		if reqlistopetions.Sort == "" {
			reqlistopetions.Sort = "endTime-,creationTimestamp-"
		}
		reqOptions := []store.ListOption{
			store.WithPageSize(reqlistopetions.Page, reqlistopetions.Size),
			store.WithSort(reqlistopetions.Sort),
			store.WithSearch(reqlistopetions.Search),
		}
		list := &store.List[Discount]{}
		if err := storage.List(r.Context(), list, reqOptions...); err != nil {
			return nil, err
		}
		return list, nil
	})
}

func (s *API) CreateDiscount(w http.ResponseWriter, r *http.Request) {
	s.on(w, r, func(ctx context.Context, storage store.Store) (any, error) {
		return base.GenericCreate(r, storage, &Discount{})
	})
}

func (s *API) UpdateDiscount(w http.ResponseWriter, r *http.Request) {
	s.onDiscount(w, r, func(ctx context.Context, storage store.Store, discount string) (any, error) {
		return base.GenericUpdate(r, storage, &Discount{}, discount)
	})
}

func (s *API) GetDiscount(w http.ResponseWriter, r *http.Request) {
	s.onDiscount(w, r, func(ctx context.Context, storage store.Store, discount string) (any, error) {
		return base.GenericGet(r, storage, &Discount{}, discount)
	})
}

func (s *API) DeleteDiscount(w http.ResponseWriter, r *http.Request) {
	s.onDiscount(w, r, func(ctx context.Context, storage store.Store, discount string) (any, error) {
		return base.GenericDelete(r, storage, &Discount{}, discount)
	})
}

func (s *API) onDiscount(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, storage store.Store, discount string) (any, error)) {
	s.on(w, r, func(ctx context.Context, storage store.Store) (any, error) {
		discount := api.Path(r, "discount", "")
		if discount == "" {
			return nil, errors.NewBadRequest("discount is required")
		}
		return fn(ctx, storage, discount)
	})
}

func (s *API) on(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, storage store.Store) (any, error)) {
	api.On(w, r, func(ctx context.Context) (any, error) {
		return fn(ctx, s.Mongo)
	})
}

func (s *API) Group() api.Group {
	return api.
		NewGroup("/discounts").
		Route(
			api.GET("").
				To(s.ListDiscounts).
				Param(api.PageParams...).
				Response(store.List[Discount]{}),

			api.PUT("/{discount}").
				To(s.UpdateDiscount).
				Param(api.BodyParam("discount", Discount{})).
				Response(Discount{}),

			api.POST("").
				To(s.CreateDiscount).
				Param(api.BodyParam("discount", Discount{})).
				Response(Discount{}),
			api.GET("/{discount}").
				To(s.GetDiscount).
				Response(Discount{}),
			api.DELETE("/{discount}").
				To(s.DeleteDiscount),
		)
}
