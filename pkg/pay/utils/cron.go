package utils

import (
	"context"

	"github.com/robfig/cron/v3"
)

type ScheduleRunner struct {
	RunEveryHourFunc  func(ctx context.Context) error
	RunEveryDayFunc   func(ctx context.Context) error
	RunEveryWeekFunc  func(ctx context.Context) error
	RunEveryMonthFunc func(ctx context.Context) error

	// CustomFuncs is a map of cron spec to function. The function will be called when the cron spec matches.
	CustomFuncs map[string]func(ctx context.Context) error
}

func (s *ScheduleRunner) Run(ctx context.Context) error {
	cron := cron.New()
	// Run every hour
	if s.RunEveryHourFunc != nil {
		cron.AddFunc("0 * * * *", func() {
			s.RunEveryHourFunc(ctx)
		})
	}
	// Run every day
	if s.RunEveryDayFunc != nil {
		cron.AddFunc("0 0 * * *", func() {
			s.RunEveryDayFunc(ctx)
		})
	}
	// Run every week
	if s.RunEveryWeekFunc != nil {
		cron.AddFunc("0 0 * * 1", func() {
			s.RunEveryWeekFunc(ctx)
		})
	}
	// Run every month
	if s.RunEveryMonthFunc != nil {
		cron.AddFunc("0 0 1 * *", func() {
			s.RunEveryMonthFunc(ctx)
		})
	}
	for spec, fn := range s.CustomFuncs {
		cron.AddFunc(spec, func() {
			fn(ctx)
		})
	}
	go func() {
		<-ctx.Done()
		stopctx := cron.Stop()
		// Wait for all jobs to finish
		<-stopctx.Done()
	}()
	go cron.Run()
	<-ctx.Done()
	stopctx := cron.Stop()
	<-stopctx.Done()
	return nil
}
