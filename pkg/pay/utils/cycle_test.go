package utils

import (
	"reflect"
	"testing"
	"time"
)

func TestCalcCycles(t *testing.T) {
	type args struct {
		from   time.Time
		to     time.Time
		period time.Duration
	}
	tests := []struct {
		name string
		args args
		want []Cycle
	}{
		{
			name: "hour",
			args: args{
				from:   time.Date(2022, 1, 1, 0, 1, 0, 0, time.UTC),
				to:     time.Date(2022, 1, 1, 6, 12, 0, 0, time.UTC),
				period: time.Hour,
			},
			want: []Cycle{
				{From: time.Date(2022, 1, 1, 0, 0, 0, 0, time.UTC), To: time.Date(2022, 1, 1, 1, 0, 0, 0, time.UTC)},
				{From: time.Date(2022, 1, 1, 1, 0, 0, 0, time.UTC), To: time.Date(2022, 1, 1, 2, 0, 0, 0, time.UTC)},
				{From: time.Date(2022, 1, 1, 2, 0, 0, 0, time.UTC), To: time.Date(2022, 1, 1, 3, 0, 0, 0, time.UTC)},
				{From: time.Date(2022, 1, 1, 3, 0, 0, 0, time.UTC), To: time.Date(2022, 1, 1, 4, 0, 0, 0, time.UTC)},
				{From: time.Date(2022, 1, 1, 4, 0, 0, 0, time.UTC), To: time.Date(2022, 1, 1, 5, 0, 0, 0, time.UTC)},
				{From: time.Date(2022, 1, 1, 5, 0, 0, 0, time.UTC), To: time.Date(2022, 1, 1, 6, 0, 0, 0, time.UTC)},
			},
		},
		{
			name: "day",
			args: args{
				from:   time.Date(2022, 1, 1, 3, 1, 0, 0, time.UTC),
				to:     time.Date(2022, 1, 2, 4, 12, 0, 0, time.UTC),
				period: time.Hour * 24,
			},
			want: []Cycle{
				{From: time.Date(2022, 1, 1, 0, 0, 0, 0, time.UTC), To: time.Date(2022, 1, 2, 0, 0, 0, 0, time.UTC)},
			},
		},
		{
			name: "less than hour",
			args: args{
				from:   time.Date(2022, 1, 1, 0, 1, 0, 0, time.UTC),
				to:     time.Date(2022, 1, 1, 0, 1, 1, 0, time.UTC),
				period: time.Hour,
			},
			want: []Cycle{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := CalcCycles(tt.args.from, tt.args.to, tt.args.period); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("calcCycles() = %v, want %v", got, tt.want)
			}
		})
	}
}
