package utils

import (
	"fmt"
	"time"
)

type Cycle struct {
	From time.Time `json:"from,omitempty"`
	To   time.Time `json:"to,omitempty"`
}

func (c Cycle) Key() string {
	return fmt.Sprintf("%s-%s", c.From.Format(time.RFC3339), c.To.Format(time.RFC3339))
}

func (c Cycle) Duration() time.Duration {
	return c.To.Sub(c.From)
}

func CalcHistoryCyclesFromNow(maxHistory, resolution time.Duration) []Cycle {
	if maxHistory == 0 {
		return []Cycle{}
	}
	now := time.Now()
	return CalcCycles(now.Add(-maxHistory), now, resolution)
}

func CalcCycles(from, to time.Time, resolution time.Duration) []Cycle {
	from, to = from.Truncate(resolution), to.Truncate(resolution)
	cycles := []Cycle{}
	for from.Before(to) {
		cycles = append(cycles, Cycle{From: from, To: from.Add(resolution)})
		from = from.Add(resolution)
	}
	return cycles
}
