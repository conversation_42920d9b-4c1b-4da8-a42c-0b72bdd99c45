package shipment

import (
	"context"
	"crypto/sha256"
	"encoding/json"
	"time"

	"k8s.io/apimachinery/pkg/util/wait"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/queue"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/pay/order"
	"xiaoshiai.cn/rune/pkg/pay/product"
)

const ShipmentLicenseDirectly = true

func NewOrderShipment(store store.Store, _ store.Store, orderEventQueue queue.Queue, orderService order.OrderService, skusvc product.SKUService) *OrderEventConsumer {
	return &OrderEventConsumer{
		orderEventQueue: orderEventQueue,
		orderService:    orderService,
		skus:            skusvc,
		shipments:       map[string]OrderItemShipment{},
	}
}

type OrderEventConsumer struct {
	orderEventQueue queue.Queue
	orderService    order.OrderService
	skus            product.SKUService
	// category -> shipment
	shipments map[string]OrderItemShipment
}

func (o *OrderEventConsumer) Run(ctx context.Context) error {
	return wait.PollUntilContextCancel(ctx, 10*time.Second, true, func(ctx context.Context) (done bool, err error) {
		log.Info("order event consumer running")
		return false, o.run(ctx)
	})
}

func (o *OrderEventConsumer) Name() string {
	return "OrderEventConsumer"
}

func (o *OrderEventConsumer) run(ctx context.Context) error {
	return o.orderEventQueue.Consume(ctx, func(ctx context.Context, _ string, data []byte) error {
		event := &order.OrderEvent{}
		if err := json.Unmarshal(data, event); err != nil {
			return err
		}
		return o.consume(ctx, event.ID)
	}, queue.ConsumeOptions{})
}

func (o *OrderEventConsumer) consume(ctx context.Context, id string) error {
	od, err := o.orderService.GetOrder(ctx, id, order.GetOrderOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			return nil
		}
		return err
	}
	if od.State != order.OrderStatusPhasePaid && od.State != order.OrderStatusPhaseRefunded {
		return nil
	}

	var errs []error
	for _, item := range od.Items {
		skuref := item.SKU
		if skuref.Empty() || skuref.Name == "" {
			continue
		}
		// find shipment
		shipment, ok := o.shipments[skuref.Category]
		if !ok {
			continue
		}
		switch od.Kind {
		case order.OrderKindNew:
			result, err := shipment.OnNew(ctx, od, item)
			if err != nil {
				errs = append(errs, err)
				continue
			}
			if result != nil {
				opt := order.ShipmentOptions{
					Reference: result.Reference,
					From:      result.Start,
					To:        result.End,
				}
				if err := o.orderService.ShipmentOrderItem(ctx, od.Name, item.ID, opt); err != nil {
					errs = append(errs, err)
					continue
				}
			}
		case order.OrderKindRenew:
			result, err := shipment.OnRenew(ctx, od, item)
			if err != nil {
				errs = append(errs, err)
				continue
			}
			if result != nil {
				opt := order.ShipmentOptions{
					Reference: result.Reference,
					From:      result.Start,
					To:        result.End,
				}
				if err := o.orderService.ShipmentOrderItem(ctx, od.Name, item.ID, opt); err != nil {
					errs = append(errs, err)
					continue
				}
			}
		case order.OrderKindRefund:
			if err := shipment.OnRefund(ctx, od, item); err != nil {
				errs = append(errs, err)
				continue
			}
		}
	}
	if len(errs) != 0 {
		return errors.NewAggregate(errs)
	}
	return nil
}

func OrderIDToAlphaNumeric(id string, len int) string {
	h := sha256.New()
	h.Write([]byte(id))
	hash := h.Sum(nil)
	const alphaNumeric = "abcdefghijklmnopqrstuvwxyz0123456789"
	result := make([]byte, len)
	for i := range result {
		result[i] = alphaNumeric[hash[i%sha256.Size]%byte(36)]
	}
	return string(result)
}

const Month = time.Hour * 24 * 30

func CalcStartEndFromNow(months int) (time.Time, time.Time) {
	now := time.Now()
	// start time is next hour
	nexthour := now.Truncate(time.Hour).Add(time.Hour)
	return CalcStartEndFrom(nexthour, months)
}

func CalcStartEndFrom(from time.Time, months int) (time.Time, time.Time) {
	year := from.Year()
	newmonth := from.Month() + time.Month(months)
	for newmonth > 12 {
		newmonth -= 12
		year++
	}
	to := time.Date(year, newmonth, from.Day(), from.Hour(), from.Minute(), from.Second(), from.Nanosecond(), from.Location())
	return from, to
}

type ShipmentResult struct {
	Reference store.ResourcedObjectReference
	Start     time.Time
	End       time.Time
}

type OrderItemShipment interface {
	OnNew(ctx context.Context, od *order.Order, item order.OrderItem) (*ShipmentResult, error)
	OnRenew(ctx context.Context, od *order.Order, item order.OrderItem) (*ShipmentResult, error)
	OnRefund(ctx context.Context, od *order.Order, item order.OrderItem) error
}

func mapvalue(m map[string]string, key string) string {
	if m == nil {
		return ""
	}
	return m[key]
}
