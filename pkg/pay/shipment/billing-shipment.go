package shipment

import (
	"context"
	"encoding/json"
	"time"

	"k8s.io/apimachinery/pkg/util/wait"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/queue"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/pay/billing"
	"xiaoshiai.cn/rune/pkg/pay/order"
)

func NewBillingShipment(store store.Store, events queue.Queue, billing billing.BillingService) *BillingEventConsumer {
	return &BillingEventConsumer{
		store:   store,
		events:  events,
		billing: billing,
	}
}

type BillingEventConsumer struct {
	store   store.Store
	events  queue.Queue
	billing billing.BillingService
}

func (o *BillingEventConsumer) Run(ctx context.Context) error {
	return wait.PollUntilContextCancel(ctx, 10*time.Second, true, func(ctx context.Context) (done bool, err error) {
		log.Info("billing event consumer running")
		return false, o.run(ctx)
	})
}

func (o *BillingEventConsumer) Name() string {
	return "BillingEventConsumer"
}

func (o *BillingEventConsumer) run(ctx context.Context) error {
	return o.events.Consume(ctx, func(ctx context.Context, _ string, data []byte) error {
		event := &order.OrderEvent{}
		if err := json.Unmarshal(data, event); err != nil {
			return err
		}
		switch event.Phase {
		case order.OrderStatusPhasePaid:
			return o.consume(ctx, event.ID)
		default:
			return nil
		}
	}, queue.ConsumeOptions{})
}

func (o *BillingEventConsumer) consume(ctx context.Context, id string) error {
	record, err := o.billing.GetBilling(ctx, id, billing.GetBillingOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			return nil
		}
		return err
	}
	_ = record
	return nil
}
