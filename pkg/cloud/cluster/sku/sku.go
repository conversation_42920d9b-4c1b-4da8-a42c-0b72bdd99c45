package sku

import (
	"context"
	"net/http"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/base"
	"xiaoshiai.cn/rune/pkg/cloud/cluster"
)

type SKU struct {
	store.ObjectMeta `json:",inline"`
	// "modeling", "training", "inference"
	Kind             string        `json:"kind"`
	ResourcePoolName string        `json:"resourcePoolName"`
	Resources        []SKUResource `json:"resources"`
	Config           SKUConfig     `json:"config"`
	Enabled          bool          `json:"enabled"`
	Status           SKUStatus     `json:"status"`
}

type SKUStatus struct {
	Available bool `json:"available"`
}

type SKUConfig map[string]string

type SKUResource struct {
	Name         string              `json:"name"`
	ResourceName corev1.ResourceName `json:"resourceName"` // cpu, memory, gpu
	Limit        resource.Quantity   `json:"limit"`
	Request      resource.Quantity   `json:"request"`
	Labels       map[string]string   `json:"labels"` // node selector labels
}

type API struct {
	Store     store.Store
	CloudInfo cluster.CloudInfoGetter
}

func NewAPI(store store.Store, cloudInfo cluster.CloudInfoGetter) *API {
	return &API{Store: store, CloudInfo: cloudInfo}
}

func (a *API) ListSKU(w http.ResponseWriter, r *http.Request) {
	a.onTenant(w, r, func(ctx context.Context, storage store.Store) (any, error) {
		list := store.List[SKU]{}
		return base.GenericList(r, storage, &list)
	})
}

func (a *API) GetSKU(w http.ResponseWriter, r *http.Request) {
	a.onSKU(w, r, func(ctx context.Context, storage store.Store, sku string) (any, error) {
		return base.GenericGet(r, storage, &SKU{}, sku)
	})
}

func (a *API) CreateSKU(w http.ResponseWriter, r *http.Request) {
	a.onTenant(w, r, func(ctx context.Context, storage store.Store) (any, error) {
		sku := &SKU{}
		if err := api.Body(r, sku); err != nil {
			return nil, errors.NewBadRequest(err.Error())
		}
		if err := validateSKU(sku); err != nil {
			return nil, err
		}
		return base.GenericCreate(r, storage, sku)
	})
}

func validateSKU(sku *SKU) error {
	if sku.Name == "" {
		return errors.NewBadRequest("sku name is required")
	}
	// Add additional validations as needed
	return nil
}

func (a *API) UpdateSKU(w http.ResponseWriter, r *http.Request) {
	a.onSKU(w, r, func(ctx context.Context, storage store.Store, sku string) (any, error) {
		skuObj := &SKU{}
		if err := api.Body(r, skuObj); err != nil {
			return nil, errors.NewBadRequest(err.Error())
		}
		if err := validateSKU(skuObj); err != nil {
			return nil, err
		}
		return base.GenericUpdate(r, storage, skuObj, sku)
	})
}

func (a *API) DeleteSKU(w http.ResponseWriter, r *http.Request) {
	a.onSKU(w, r, func(ctx context.Context, storage store.Store, sku string) (any, error) {
		return base.GenericDelete(r, storage, &SKU{}, sku)
	})
}

func (a *API) onTenant(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, storage store.Store) (any, error)) {
	base.OnTenant(w, r, func(ctx context.Context, tenant string) (any, error) {
		return fn(ctx, a.Store.Scope(base.ScopeTenant(tenant)))
	})
}

func (a *API) onSKU(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, storage store.Store, sku string) (any, error)) {
	a.onTenant(w, r, func(ctx context.Context, storage store.Store) (any, error) {
		sku := api.Path(r, "sku", "")
		if sku == "" {
			return nil, errors.NewBadRequest("sku name is required")
		}
		return fn(ctx, storage, sku)
	})
}

func (a *API) Group() api.Group {
	return base.
		NewClusterGroup().
		Tag("SKU").
		SubGroup(
			api.NewGroup("/skus").
				Route(
					api.GET("").
						Operation("list skus").
						Param(api.PageParams...).
						To(a.ListSKU).
						Response(store.List[SKU]{}),

					api.GET("/{sku}").
						Operation("get sku").
						To(a.GetSKU).
						Response(SKU{}),

					api.POST("").
						Operation("create sku").
						To(a.CreateSKU).
						Param(api.BodyParam("sku", SKU{})).
						Response(SKU{}),

					api.PUT("/{sku}").
						Operation("update sku").
						To(a.UpdateSKU).
						Param(api.BodyParam("sku", SKU{})).
						Response(SKU{}),

					api.DELETE("/{sku}").
						Operation("delete sku").
						To(a.DeleteSKU),
				),
		)
}
