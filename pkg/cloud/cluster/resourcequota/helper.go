package resourcequota

import (
	"context"
	"fmt"
	"strings"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/base"
)

type OverLimited struct {
	ResourceName corev1.ResourceName
	Total        resource.Quantity
	Limit        resource.Quantity
}

func (o OverLimited) String() string {
	return fmt.Sprintf("%s used %s but the limit is %s", o.ResourceName, o.Total.String(), o.Limit.String())
}

type OverLimitedList []OverLimited

func (o OverLimitedList) String() string {
	list := make([]string, 0, len(o))
	for _, overLimited := range o {
		list = append(list, overLimited.String())
	}
	return strings.Join(list, ", ")
}

func CheckOverLimit(total, current corev1.ResourceList) OverLimitedList {
	overLimited := make([]OverLimited, 0)
	for resourceName, val := range current {
		limit := total[resourceName]
		if limit.IsZero() {
			continue
		}
		if val.Cmp(limit) > 0 {
			overLimited = append(overLimited, OverLimited{
				ResourceName: resourceName,
				Total:        val,
				Limit:        limit,
			})
		}
	}
	return overLimited
}

func GetResourceQuota(ctx context.Context, storage store.Store, tenant, org string, clusterref store.ObjectReference) (*ResourceQuota, error) {
	if tenant != "" {
		storage = storage.Scope(base.ScopeTenant(tenant))
		if org != "" {
			storage = storage.Scope(base.ScopeOrganization(org))
		}
	}
	resourcequota := &ResourceQuota{}
	quotaname := ClusterReferenceToQuotaName(clusterref)
	if err := storage.Get(ctx, quotaname, resourcequota); err != nil {
		return nil, err
	}
	return resourcequota, nil
}

func ListClustersUsedByTenant(ctx context.Context, storage store.Store, tenant string) ([]store.ObjectReference, error) {
	resourcequotalist := &store.List[ResourceQuota]{}
	options := []store.ListOption{}
	if err := storage.Scope(base.ScopeTenant(tenant)).List(ctx, resourcequotalist, options...); err != nil {
		return nil, err
	}
	refs := []store.ObjectReference{}
	exists := map[string]struct{}{}
	for _, v := range resourcequotalist.Items {
		ref := v.Cluster
		exists[ref.String()] = struct{}{}
		refs = append(refs, ref)
	}
	return refs, nil
}

// ListClustersUsedByTenantOrganization returns the list of clusters used by the tenant organization
func ListClustersUsedByTenantOrganization(ctx context.Context, storage store.Store, tenant, org string) ([]store.ObjectReference, error) {
	resourcequotalist := &store.List[ResourceQuota]{}
	options := []store.ListOption{}
	if err := storage.Scope(ScopeTenantOrganization(tenant, org)...).List(ctx, resourcequotalist, options...); err != nil {
		return nil, err
	}
	refs := []store.ObjectReference{}
	exists := map[string]struct{}{}
	for _, v := range resourcequotalist.Items {
		ref := v.Cluster
		exists[ref.String()] = struct{}{}
		refs = append(refs, ref)
	}
	return refs, nil
}

func ScopeTenantOrganization(tenant, organization string) []store.Scope {
	return []store.Scope{base.ScopeTenant(tenant), {Resource: "organizations", Name: organization}}
}
