package resourcequota

import (
	"context"

	corev1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/cloud/cluster"
)

type ResourceQuotaOperation struct {
	Clusters cluster.CloudInfoGetter
	Client   store.Store
}

func (r ResourceQuotaOperation) SetTenantResourceQuota(ctx context.Context, tenantname string, obj *ResourceQuota) error {
	used, err := CalcAllOrganizationsUsed(ctx, r.Client, obj.Cluster, tenantname)
	if err != nil {
		return err
	}
	obj.Status.Hard = obj.Hard
	obj.Status.Used = used
	return nil
}

func (r ResourceQuotaOperation) UnsetTenantResourceQuota(ctx context.Context, tenantname string, obj *ResourceQuota) error {
	orgquotas, err := ListAllOrganizationsQuota(ctx, r.Client, obj.Cluster, tenantname)
	if err != nil {
		return err
	}
	var errs []error
	for _, orgquota := range orgquotas {
		// delete organization resource quota about this cluster
		if err := r.Client.Scope(orgquota.Scopes...).Delete(ctx, &orgquota.ResourceQuota); err != nil {
			errs = append(errs, err)
		}
	}
	return errors.NewAggregate(errs)
}

func (r ResourceQuotaOperation) SetOrganizationResourceQuota(ctx context.Context, tenantname, org string, obj *ResourceQuota) error {
	namespace := GetNamespaceFromTenantOrganization(tenantname, org)
	if namespace == "" {
		return nil
	}
	info, err := r.Clusters.Get(ctx, obj.Cluster)
	if err != nil {
		return err
	}
	kubeclients, err := info.KubernetesConfig()
	if err != nil {
		return err
	}
	if err := r.syncOrganizationContainerResourceQuota(ctx, kubeclients, namespace, obj); err != nil {
		return err
	}
	return nil
}

func (r ResourceQuotaOperation) syncOrganizationContainerResourceQuota(ctx context.Context, info *cluster.KubernetesClients, namespace string, obj *ResourceQuota) error {
	ns := &corev1.Namespace{}
	if err := info.Client.Get(ctx, client.ObjectKey{Name: namespace}, ns); err != nil {
		if !apierrors.IsNotFound(err) {
			return err
		}
		if err := info.Client.Create(ctx, &corev1.Namespace{ObjectMeta: metav1.ObjectMeta{Name: namespace}}); err != nil {
			return err
		}
	}

	// set default limit range
	limiterange := &corev1.LimitRange{
		TypeMeta: metav1.TypeMeta{APIVersion: "v1", Kind: "LimitRange"},
		ObjectMeta: metav1.ObjectMeta{
			Name:      "default",
			Namespace: namespace,
		},
		Spec: corev1.LimitRangeSpec{
			Limits: []corev1.LimitRangeItem{
				{
					Type: corev1.LimitTypeContainer,
					Default: corev1.ResourceList{
						corev1.ResourceCPU:    resource.MustParse("1"),
						corev1.ResourceMemory: resource.MustParse("1Gi"),
					},
					DefaultRequest: corev1.ResourceList{
						corev1.ResourceCPU:    resource.MustParse("128m"),
						corev1.ResourceMemory: resource.MustParse("128Mi"),
					},
				},
			},
		},
	}
	// create if not exist
	if err := info.Client.Get(ctx, client.ObjectKeyFromObject(limiterange), limiterange); err != nil {
		if apierrors.IsNotFound(err) {
			if err := info.Client.Create(ctx, limiterange); err != nil {
				return err
			}
		}
	}

	resourceQuota := &corev1.ResourceQuota{ObjectMeta: metav1.ObjectMeta{Name: "default", Namespace: namespace}}
	if _, err := controllerutil.CreateOrUpdate(ctx, info.Client, resourceQuota, func() error {
		limitresources := corev1.ResourceList{}
		for k, v := range obj.Hard {
			if k == cluster.ResourceStorage {
				limitresources["requests."+k] = v
			} else {
				limitresources["limits."+k] = v
			}
		}
		resourceQuota.Spec.Hard = limitresources
		return nil
	}); err != nil {
		return err
	}
	obj.Status.Hard = resourceQuota.Spec.Hard
	obj.Status.Used = resourceQuota.Status.Used
	return nil
}

func (r ResourceQuotaOperation) UnsetOrganizationResourceQuota(ctx context.Context, tenantname, org string, obj *ResourceQuota) error {
	namespace := GetNamespaceFromTenantOrganization(tenantname, org)
	if namespace == "" {
		return nil
	}
	info, err := r.Clusters.Get(ctx, obj.Cluster)
	if err != nil {
		if errors.IsNotFound(err) {
			return nil
		}
		return err
	}
	kubeclients, err := info.KubernetesConfig()
	if err != nil {
		return err
	}
	if err := r.removeOrganizationContainerResourceQuota(ctx, kubeclients, namespace, obj); err != nil {
		return err
	}
	return nil
}

func (r ResourceQuotaOperation) removeOrganizationContainerResourceQuota(ctx context.Context,
	info *cluster.KubernetesClients, namespace string, _ *ResourceQuota,
) error {
	quota := &corev1.ResourceQuota{
		ObjectMeta: metav1.ObjectMeta{Name: "default", Namespace: namespace},
	}
	if FeatureSetToZeroOnRmoveResourceQuota {
		if err := info.Client.Get(ctx, client.ObjectKeyFromObject(quota), quota); err != nil {
			if apierrors.IsNotFound(err) {
				return nil
			}
			return err
		}
		for k, val := range quota.Spec.Hard {
			val.Set(0)
			quota.Spec.Hard[k] = val
		}
		return info.Client.Update(ctx, quota)
	}
	return client.IgnoreNotFound(info.Client.Delete(ctx, quota))
}

func GetNamespaceFromTenantOrganization(tenant, org string) string {
	return tenant + "-" + org
}
