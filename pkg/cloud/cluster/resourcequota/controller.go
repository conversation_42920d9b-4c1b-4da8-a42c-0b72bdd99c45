package resourcequota

import (
	"context"

	"xiaoshiai.cn/common/controller"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/base"
	"xiaoshiai.cn/rune/pkg/cloud/cluster"
)

const ResourceQuotaFinalizer = "resourcequota-controller"

func NewResourceQuotaController(storage store.Store, info cluster.CloudInfoGetter) (*controller.Controller, error) {
	rec := &ResourceQuotaReconciler{
		ResourceQuotaOperation: ResourceQuotaOperation{
			Clusters: info,
			Client:   storage,
		},
	}
	br := controller.NewBetterReconciler(rec, storage,
		controller.WithFinalizer(ResourceQuotaFinalizer),
		controller.WithAutosetStatus(),
	)
	c := controller.NewController("resourcequotas", br).
		Watch(
			controller.NewStoreSource(storage, &ResourceQuota{}),
		)
	return c, nil
}

var _ controller.Reconciler[*ResourceQuota] = &ResourceQuotaReconciler{}

type ResourceQuotaReconciler struct {
	ResourceQuotaOperation
}

// Sync implements Reconciler.
func (r *ResourceQuotaReconciler) Sync(ctx context.Context, obj *ResourceQuota) (controller.Result, error) {
	return base.DispatchOnScopes(ctx, obj, base.ScopeHandler[*ResourceQuota]{
		OnTenant:             r.syncTenantResourceQuota,
		OnTenantOrganization: r.syncTenantOrgResourceQuota,
	})
}

func (r *ResourceQuotaReconciler) syncTenantOrgResourceQuota(ctx context.Context, tenantname, orgname string, obj *ResourceQuota) (controller.Result, error) {
	if err := r.SetOrganizationResourceQuota(ctx, tenantname, orgname, obj); err != nil {
		return controller.Result{}, err
	}
	return controller.Result{}, nil
}

func (r *ResourceQuotaReconciler) syncTenantResourceQuota(ctx context.Context, tenantname string, obj *ResourceQuota) (controller.Result, error) {
	if err := r.SetTenantResourceQuota(ctx, tenantname, obj); err != nil {
		return controller.Result{}, err
	}
	return controller.Result{}, nil
}

// Remove implements Reconciler.
func (r *ResourceQuotaReconciler) Remove(ctx context.Context, obj *ResourceQuota) (controller.Result, error) {
	return base.DispatchOnScopes(ctx, obj, base.ScopeHandler[*ResourceQuota]{
		OnTenant:             r.removeTenantResourceQuota,
		OnTenantOrganization: r.removeOrganizationResourceQuota,
	})
}

func (r *ResourceQuotaReconciler) removeTenantResourceQuota(ctx context.Context, tenantname string, obj *ResourceQuota) (controller.Result, error) {
	if err := r.UnsetTenantResourceQuota(ctx, tenantname, obj); err != nil {
		return controller.Result{}, err
	}
	return controller.Result{}, nil
}

func (r *ResourceQuotaReconciler) removeOrganizationResourceQuota(ctx context.Context, tenantname, org string, obj *ResourceQuota) (controller.Result, error) {
	if err := r.UnsetOrganizationResourceQuota(ctx, tenantname, org, obj); err != nil {
		return controller.Result{}, err
	}
	return controller.Result{}, nil
}
