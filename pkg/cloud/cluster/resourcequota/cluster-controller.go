package resourcequota

import (
	"context"

	"xiaoshiai.cn/common/controller"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/cloud/cluster"
)

func NewResourceQuotaCleanController(storage store.Store, info cluster.CloudInfoGetter) (*controller.Controller, error) {
	rec := &ResourceQuotaCleanReconciler{
		ResourceQuotaOperation: ResourceQuotaOperation{
			Clusters: info,
			Client:   storage,
		},
	}
	br := controller.NewBetterReconciler(rec, storage,
		controller.WithFinalizer("resourcequota-cleaner"),
	)
	c := controller.NewController("cluster-resourcequotas-cleaner", br).
		Watch(
			controller.NewStoreSource(storage, &cluster.Cluster{}, OnDeleteion),
		)
	return c, nil
}

func OnDeleteion(kind store.WatchEventType, obj store.Object) bool {
	return kind == store.WatchEventCreate || kind == store.WatchEventDelete || !obj.GetDeletionTimestamp().IsZero()
}

var _ controller.Reconciler[*cluster.Cluster] = &ResourceQuotaCleanReconciler{}

type ResourceQuotaCleanReconciler struct {
	ResourceQuotaOperation
}

// Sync implements Reconciler.
func (r *ResourceQuotaCleanReconciler) Sync(ctx context.Context, obj *cluster.Cluster) (controller.Result, error) {
	return controller.Result{}, nil
}

// Remove implements Reconciler.
func (r *ResourceQuotaCleanReconciler) Remove(ctx context.Context, obj *cluster.Cluster) (controller.Result, error) {
	// remove all resourcequota about this cluster
	list := store.List[ResourceQuota]{}
	if err := r.Client.List(ctx, &list, store.WithSubScopes()); err != nil {
		return controller.Result{}, err
	}
	log := log.FromContext(ctx)
	ref := store.ObjectReference{
		Name:   obj.Name,
		Scopes: obj.Scopes,
	}
	var errs []error
	for _, rq := range list.Items {
		if rq.Cluster.Equals(ref) {
			log.Info("delete resourcequota on cluster removed", "resourcequota", rq.Name, "cluster", ref.String())
			if err := r.Client.Scope(rq.Scopes...).Delete(ctx, &rq); err != nil {
				errs = append(errs, err)
			}
		}
	}
	return controller.Result{}, errors.NewAggregate(errs)
}
