package cluster

import (
	"context"
	"net/http"

	"k8s.io/apimachinery/pkg/api/resource"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/base"
)

type ResourceMetadata struct {
	Name        string            `json:"name,omitempty" validate:"name"`
	Scopes      []store.Scope     `json:"scopes,omitempty" validate:"omitempty,scope"`
	Type        string            `json:"type,omitempty"`
	Annotations map[string]string `json:"annotations,omitempty"`
	Labels      map[string]string `json:"labels,omitempty"`
	Public      bool              `json:"public,omitempty"`
	Alias       string            `json:"alias,omitempty"`
	Vendor      string            `json:"vendor,omitempty"`
	Description string            `json:"description,omitempty"`
	Capacity    resource.Quantity `json:"capacity,omitempty"`
}

func ResourceMetadataFrom(item client.Object) ResourceMetadata {
	getmap := func(kvs map[string]string, key string) string {
		if kvs == nil {
			return ""
		}
		return kvs[key]
	}
	return ResourceMetadata{
		Name:        item.GetName(),
		Annotations: item.GetAnnotations(),
		Labels:      item.GetLabels(),
		Public:      getmap(item.GetLabels(), base.LabelIsPublic) == "true",
		Alias:       or(getmap(item.GetAnnotations(), base.AnnotationAlias), item.GetName()),
		Description: getmap(item.GetAnnotations(), base.AnnotationDescription),
		Vendor:      getmap(item.GetAnnotations(), base.AnnotationVendor),
		Capacity: func() resource.Quantity {
			if capacity := getmap(item.GetAnnotations(), base.AnnotationCapacity); capacity != "" {
				q, _ := resource.ParseQuantity(capacity)
				return q
			}
			return resource.Quantity{}
		}(),
	}
}

func or(val ...string) string {
	for _, v := range val {
		if v != "" {
			return v
		}
	}
	return ""
}

func ListClusterMetadata(r *http.Request, storage store.Store, withNotPublished bool) ([]ResourceMetadata, error) {
	ctx := r.Context()

	options := []store.ListOption{}

	fields := store.Requirements{}
	if !withNotPublished {
		fields = append(fields, store.RequirementEqual("published", true))
	}
	if len(fields) != 0 {
		options = append(options, store.WithFieldRequirements(fields...))
	}
	clusterlist := &store.List[Cluster]{}
	if err := storage.List(ctx, clusterlist, options...); err != nil {
		return nil, err
	}
	ret := make([]ResourceMetadata, 0, len(clusterlist.Items))
	for _, c := range clusterlist.Items {
		ret = append(ret, ResourceMetadata{
			Name:        c.Name,
			Type:        string(c.Type),
			Description: c.Description,
			Annotations: c.Annotations,
			Scopes:      c.Scopes,
			Labels:      c.Labels,
			Public:      c.Published,
		})
	}
	return ret, nil
}

func GetClusterKubeClient(ctx context.Context, cloudinfo CloudInfoGetter, clusterref store.ObjectReference) (client.Client, error) {
	info, err := cloudinfo.Get(ctx, clusterref)
	if err != nil {
		return nil, err
	}
	cli, err := info.KubernetesConfig()
	if err != nil {
		return nil, err
	}
	return cli.Client, nil
}
