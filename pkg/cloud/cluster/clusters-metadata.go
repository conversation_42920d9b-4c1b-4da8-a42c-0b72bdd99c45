package cluster

import (
	"context"
	"net/http"

	"sigs.k8s.io/controller-runtime/pkg/client"
	"xiaoshiai.cn/common/store"
)

func ListClusterMetadata(r *http.Request, storage store.Store, withNotPublished bool) ([]ResourceMetadata, error) {
	ctx := r.Context()

	options := []store.ListOption{}

	fields := store.Requirements{}
	if !withNotPublished {
		fields = append(fields, store.RequirementEqual("published", true))
	}
	if len(fields) != 0 {
		options = append(options, store.WithFieldRequirements(fields...))
	}
	clusterlist := &store.List[Cluster]{}
	if err := storage.List(ctx, clusterlist, options...); err != nil {
		return nil, err
	}
	ret := make([]ResourceMetadata, 0, len(clusterlist.Items))
	for _, c := range clusterlist.Items {
		ret = append(ret, ResourceMetadata{
			Name:        c.Name,
			Type:        string(c.Type),
			Description: c.Description,
			Annotations: c.Annotations,
			Scopes:      c.Scopes,
			Labels:      c.Labels,
			Public:      c.Published,
		})
	}
	return ret, nil
}

func GetClusterKubeClient(ctx context.Context, cloudinfo CloudInfoGetter, clusterref store.ObjectReference) (client.Client, error) {
	info, err := cloudinfo.Get(ctx, clusterref)
	if err != nil {
		return nil, err
	}
	cli, err := info.KubernetesConfig()
	if err != nil {
		return nil, err
	}
	return cli.Client, nil
}
