package cluster

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"time"

	"xiaoshiai.cn/common/httpclient"
	"xiaoshiai.cn/common/log"
)

const LokiServiceName = "loki"

type LokiQueryRangeOptions struct {
	// The LogQL query to perform.
	Query string
	// The max number of entries to return.
	// It defaults to 100. Only applies to query types which produce a stream (log lines) response.
	Limit int
	// The start time for the query as a nanosecond Unix epoch or another supported format.
	// Defaults to one hour ago.
	// Loki returns results with timestamp greater or equal to this value.
	Start time.Time
	// The end time for the query as a nanosecond Unix epoch or another supported format.
	// Defaults to now. Loki returns results with timestamp lower than this value.
	End time.Time
	// A duration used to calculate start relative to end.
	//  If end is in the future, start is calculated as this duration before now.
	//  Any value specified for start supersedes this parameter.
	Since time.Duration
	// Query resolution step width in duration format or float number of seconds.
	// duration refers to Prometheus duration strings of the form [0-9]+[smhdwy].
	//  For example, 5m refers to a duration of 5 minutes. Defaults to a dynamic value based on start and end.
	// Only applies to query types which produce a matrix response.
	Step time.Duration
	// Only return entries at (or greater than) the specified interval,
	// can be a duration format or float number of seconds.
	// Only applies to queries which produce a stream response.
	// Not to be confused with step, see the explanation under Step versus interval.
	Interval time.Duration
	// Determines the sort order of logs. Supported values are forward or backward. Defaults to backward.
	Direction string
}

func (o LokiQueryRangeOptions) Values() url.Values {
	values := make(url.Values)
	values.Set("query", o.Query)
	if o.Limit > 0 {
		values.Set("limit", fmt.Sprintf("%d", o.Limit))
	}
	if !o.Start.IsZero() {
		values.Set("start", fmt.Sprintf("%d", o.Start.UnixNano()))
	}
	if !o.End.IsZero() {
		values.Set("end", fmt.Sprintf("%d", o.End.UnixNano()))
	}
	if o.Since > 0 {
		values.Set("since", fmt.Sprintf("%d", o.Since))
	}
	if o.Step > 0 {
		values.Set("step", o.Step.String())
	}
	if o.Interval > 0 {
		values.Set("interval", fmt.Sprintf("%d", o.Interval))
	}
	if o.Direction != "" {
		values.Set("direction", o.Direction)
	}
	return values
}

type LokiQueryRangeResponse struct {
	Status string            `json:"status"`
	Data   LokiQueryRangeDta `json:"data"`
}

type LokiQueryRangeDta struct {
	ResultType string                 `json:"resultType"`
	Result     []LokiQueryRangeResult `json:"result"`
	Stats      LokiQueryRangeStats    `json:"stats"`
}

type LokiQueryRangeResult struct {
	Stream map[string]string `json:"stream"`
	// Values is a list of log lines.
	// every log line is a list of strings.
	// The first element is the timestamp in unix nanoseconds.
	// The second element is the log line.
	Values [][]string `json:"values"`
}

type LokiQueryRangeStats map[string]any

func (s ContainerOperation) LokiQueryRange(ctx context.Context, options LokiQueryRangeOptions) (*LokiQueryRangeResponse, error) {
	cliconfig, err := s.Info.GetServiceConfig(LokiServiceName)
	if err != nil {
		return nil, err
	}
	cli := httpclient.NewClientFromClientConfig(cliconfig)
	// we do not use loki querier, import loki will cause lots of dependencies and we only need a simple query
	response := &LokiQueryRangeResponse{}
	if err := cli.Get("/loki/api/v1/query_range").Queries(options.Values()).Return(response).Send(ctx); err != nil {
		return nil, err
	}
	return response, nil
}

type LokiTailOptions struct {
	Query    string
	Limit    int
	DelayFor time.Duration
	Start    time.Time
}

func (o LokiTailOptions) Values() url.Values {
	values := make(url.Values)
	values.Set("query", o.Query)
	if o.Limit > 0 {
		values.Set("limit", fmt.Sprintf("%d", o.Limit))
	}
	if o.DelayFor > 0 {
		values.Set("delay_for", strconv.Itoa(int(o.DelayFor.Seconds())))
	}
	if !o.Start.IsZero() {
		// The start time for the query as a nanosecond Unix epoch. Defaults to one hour ago.
		values.Set("start", fmt.Sprintf("%d", o.Start.UnixNano()))
	}
	return values
}

type LokiTailsMessage struct {
	Streams []LokiQueryRangeResult `json:"streams"`
}

func (s ContainerOperation) LokiTail(ctx context.Context, options LokiTailOptions, onmsg func(ctx context.Context, msg LokiQueryRangeResult) error) (io.ReadCloser, error) {
	cliconfig, err := s.Info.GetServiceProxyConfig(LokiServiceName, true)
	if err != nil {
		return nil, err
	}
	onmsgfunc := func(ctx context.Context, msg []byte) error {
		lokimsg := LokiTailsMessage{}
		if err := json.Unmarshal(msg, &lokimsg); err != nil {
			return err
		}
		for _, stream := range lokimsg.Streams {
			if err := onmsg(ctx, stream); err != nil {
				return err
			}
		}
		return nil
	}
	if err := GetWebSocket(ctx, cliconfig, "/loki/api/v1/tail", options.Values(), onmsgfunc); err != nil {
		return nil, err
	}
	return nil, nil
}

func GetWebSocket(ctx context.Context, cliconfig *httpclient.ClientConfig, reqpath string, queries url.Values, onmsg func(ctx context.Context, msg []byte) error) error {
	if cliconfig.RoundTripper != nil {
		if httptransport, ok := cliconfig.RoundTripper.(*http.Transport); ok {
			tlsconfig := httptransport.TLSClientConfig.Clone()
			tlsconfig.NextProtos = []string{"http/1.1"}
			httptransport.TLSClientConfig = tlsconfig
			cliconfig.RoundTripper = httptransport
		}
	}
	return httpclient.GetWebSocketOptions(ctx, cliconfig, reqpath, httpclient.WebSocketOptions{
		Queries:           queries,
		KeepAliveInterval: 30 * time.Second,
		Header: http.Header{
			// allow agent recover queries from header
			"x-query": []string{queries.Encode()},
		},
		OnMessage: func(ctx context.Context, kind int, msg []byte) error {
			return onmsg(ctx, msg)
		},
	})
}

type LokiSeriesOptions struct {
	// match[]=<selector>: Repeated log stream selector argument that selects the streams to return.
	// At least one match[] argument must be provided.
	// multiple match are or-ed together
	Match []string
	// start=<nanosecond Unix epoch>: Start timestamp.
	Start time.Time
	// end=<nanosecond Unix epoch>: End timestamp.
	End time.Time
	// since: A duration used to calculate start relative to end. If
	// end is in the future, start is calculated as this duration before now.
	Since time.Duration
}

func (o LokiSeriesOptions) Values() url.Values {
	values := make(url.Values)
	for _, match := range o.Match {
		values.Add("match[]", match)
	}
	if !o.Start.IsZero() {
		values.Set("start", fmt.Sprintf("%d", o.Start.UnixNano()))
	}
	if !o.End.IsZero() {
		values.Set("end", fmt.Sprintf("%d", o.End.UnixNano()))
	}
	if o.Since > 0 {
		values.Set("since", fmt.Sprintf("%d", o.Since))
	}
	return values
}

type LokiSeriesResponse struct {
	Status string       `json:"status"`
	Data   []LokiSeries `json:"data"`
}

type LokiSeries map[string]string

func (s ContainerOperation) LokiSeries(ctx context.Context, options LokiSeriesOptions) (*LokiSeriesResponse, error) {
	cliconfig, err := s.Info.GetServiceConfig(LokiServiceName)
	if err != nil {
		return nil, err
	}
	response := &LokiSeriesResponse{}
	log.FromContext(ctx).V(5).Info("loki series", "match", options.Match, "start", options.Start, "end", options.End, "since", options.Since)
	if err := httpclient.NewClientFromClientConfig(cliconfig).Get("/loki/api/v1/series").Queries(options.Values()).Return(response).Send(ctx); err != nil {
		return nil, err
	}
	return response, nil
}
