package cluster

import (
	"context"
	"net/http"
	"strconv"
	"strings"

	"golang.org/x/exp/maps"
	networkingv1 "k8s.io/api/networking/v1"
	storagev1 "k8s.io/api/storage/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/apimachinery/pkg/types"
	"sigs.k8s.io/controller-runtime/pkg/client"
	liberrors "xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/base"
)

func jsonpatchEscape(s string) string {
	return strings.ReplaceAll(s, "/", "~1")
}

func (s *ClusterResourcesAPI) SetMetadata(w http.ResponseWriter, r *http.Request) {
	s.OnClusterInfo(w, r, func(ctx context.Context, kubes *KubernetesClients, meta RequestMetadata) (any, error) {
		metadata := ResourceMetadata{}
		if err := api.Body(meta.Request, &metadata); err != nil {
			return nil, err
		}
		val, err := kubes.Dynamic.
			Resource(meta.GroupVersionResource).
			Namespace(meta.Namespace).
			Get(ctx, meta.Name, metav1.GetOptions{})
		if err != nil {
			return nil, err
		}
		newlabels := map[string]string{base.LabelIsPublic: strconv.FormatBool(metadata.Public)}
		newannotations := map[string]string{
			base.AnnotationDescription: metadata.Description,
			base.AnnotationAlias:       metadata.Alias,
			base.AnnotationVendor:      metadata.Vendor,
			base.AnnotationCapacity:    metadata.Capacity.String(),
		}
		newval := MergeLabelAnnotations(val, newlabels, newannotations)
		return kubes.Dynamic.
			Resource(meta.GroupVersionResource).
			Namespace(meta.Namespace).
			Update(ctx, newval, metav1.UpdateOptions{})
	})
}

func MergeLabelAnnotations[T client.Object](obj T, labels, annotations map[string]string) T {
	newobj := obj.DeepCopyObject().(T)
	existingLabels := newobj.GetLabels()
	if existingLabels == nil {
		existingLabels = map[string]string{}
	}
	existingAnnotations := newobj.GetAnnotations()
	if existingAnnotations == nil {
		existingAnnotations = map[string]string{}
	}
	maps.Copy(existingLabels, labels)
	maps.Copy(existingAnnotations, annotations)
	newobj.SetLabels(existingLabels)
	newobj.SetAnnotations(existingAnnotations)

	return newobj
}

type ResourceMetadata struct {
	Name        string            `json:"name,omitempty" validate:"name"`
	Scopes      []store.Scope     `json:"scopes,omitempty"`
	Type        string            `json:"type,omitempty"`
	Annotations map[string]string `json:"annotations,omitempty"`
	Labels      map[string]string `json:"labels,omitempty"`
	Public      bool              `json:"public,omitempty"`
	Alias       string            `json:"alias,omitempty"`
	Vendor      string            `json:"vendor,omitempty"`
	Description string            `json:"description,omitempty"`
	Capacity    resource.Quantity `json:"capacity,omitempty"`
}

func (s *ClusterResourcesAPI) ResourceMetadataOnlyGroup() api.Group {
	return api.
		NewGroup("/metadata/apis").
		Route(
			api.GET("/{group}/{version}/{resource}").
				Doc("List resources metadata only").
				To(s.ListMetadata).
				Response([]ResourceMetadata{}),

			api.GET("/{group}/{version}/{resource}/{name}").
				Doc("Get resource metadata only").
				To(s.GetMetadata).
				Response(ResourceMetadata{}),
		)
}

var PublicClusterResources = map[schema.GroupVersionResource]schema.GroupVersionKind{
	networkingv1.SchemeGroupVersion.WithResource("ingressclasses"): networkingv1.SchemeGroupVersion.WithKind("IngressClass"),
	storagev1.SchemeGroupVersion.WithResource("storageclasses"):    storagev1.SchemeGroupVersion.WithKind("StorageClass"),
}

func (s *ClusterResourcesAPI) ListMetadata(w http.ResponseWriter, r *http.Request) {
	s.OnClusterInfo(w, r, func(ctx context.Context, kubes *KubernetesClients, meta RequestMetadata) (any, error) {
		gvk, ok := PublicClusterResources[meta.GroupVersionResource]
		if !ok {
			return nil, liberrors.NewForbidden(meta.GroupVersionResource.String(), "", nil)
		}
		list := &unstructured.UnstructuredList{}
		list.SetGroupVersionKind(gvk)
		options := []client.ListOption{client.MatchingLabels{base.LabelIsPublic: "true"}}
		if err := kubes.Client.List(ctx, list, options...); err != nil {
			return nil, err
		}
		ret := []ResourceMetadata{}
		for _, item := range list.Items {
			ret = append(ret, ResourceMetadataFrom(&item))
		}
		return ret, nil
	})
}

func ResourceMetadataFrom(item client.Object) ResourceMetadata {
	getmap := func(kvs map[string]string, key string) string {
		if kvs == nil {
			return ""
		}
		return kvs[key]
	}
	return ResourceMetadata{
		Name:        item.GetName(),
		Annotations: item.GetAnnotations(),
		Labels:      item.GetLabels(),
		Public:      getmap(item.GetLabels(), base.LabelIsPublic) == "true",
		Alias:       or(getmap(item.GetAnnotations(), base.AnnotationAlias), item.GetName()),
		Description: getmap(item.GetAnnotations(), base.AnnotationDescription),
		Vendor:      getmap(item.GetAnnotations(), base.AnnotationVendor),
		Capacity: func() resource.Quantity {
			if capacity := getmap(item.GetAnnotations(), base.AnnotationCapacity); capacity != "" {
				q, _ := resource.ParseQuantity(capacity)
				return q
			}
			return resource.Quantity{}
		}(),
	}
}

func or(val ...string) string {
	for _, v := range val {
		if v != "" {
			return v
		}
	}
	return ""
}

func (s *ClusterResourcesAPI) GetMetadata(w http.ResponseWriter, r *http.Request) {
	s.OnClusterInfo(w, r, func(ctx context.Context, kubes *KubernetesClients, meta RequestMetadata) (any, error) {
		gvk, ok := PublicClusterResources[meta.GroupVersionResource]
		if !ok {
			return nil, liberrors.NewForbidden(meta.GroupVersionResource.String(), "", nil)
		}
		item := &unstructured.Unstructured{}
		item.SetGroupVersionKind(gvk)
		if err := kubes.Client.Get(ctx, types.NamespacedName{Name: meta.Name}, item); err != nil {
			return nil, err
		}
		return ResourceMetadataFrom(item), nil
	})
}
