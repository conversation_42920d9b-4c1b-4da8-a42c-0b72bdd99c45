package cluster

import (
	"context"
	"fmt"
	"net/http"

	appsv1 "k8s.io/api/apps/v1"
	batchv1 "k8s.io/api/batch/v1"
	corev1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/selection"
	"k8s.io/apimachinery/pkg/types"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"xiaoshiai.cn/common/rest/api"
)

func (s *ClusterResourcesAPI) NodeCordon(w http.ResponseWriter, r *http.Request) {
	s.OnClusterInfo(w, r, func(ctx context.Context, kubes *KubernetesClients, meta RequestMetadata) (any, error) {
		return kubes.Kubernetes.CoreV1().Nodes().Patch(
			ctx, meta.Name, types.StrategicMergePatchType, []byte(`{"spec":{"unschedulable":true}}`), metav1.PatchOptions{},
		)
	})
}

func (s *ClusterResourcesAPI) NodeUncordon(w http.ResponseWriter, r *http.Request) {
	s.OnClusterInfo(w, r, func(ctx context.Context, kubes *KubernetesClients, meta RequestMetadata) (any, error) {
		return kubes.Kubernetes.CoreV1().Nodes().Patch(
			ctx, meta.Name, types.StrategicMergePatchType, []byte(`{"spec":{"unschedulable":false}}`), metav1.PatchOptions{},
		)
	})
}

func (s *ClusterResourcesAPI) ResourcePods(w http.ResponseWriter, r *http.Request) {
	s.OnClusterInfo(w, r, func(ctx context.Context, kubes *KubernetesClients, meta RequestMetadata) (any, error) {
		return ListResourcePods(ctx, kubes, meta.Resource, meta.Namespace, meta.Name)
	})
}

func (s *ClusterResourcesAPI) ResourceChildren(w http.ResponseWriter, r *http.Request) {
	s.OnClusterInfo(w, r, func(ctx context.Context, kubes *KubernetesClients, meta RequestMetadata) (any, error) {
		return ListResourceChildren(ctx, kubes, meta.Resource, meta.Namespace, meta.Name)
	})
}

func ListResourcePods(ctx context.Context, info *KubernetesClients, resource, namespace, name string) (*corev1.PodList, error) {
	var matchLabels map[string]string
	switch resource {
	case "deployments":
		deployment, err := info.Kubernetes.AppsV1().Deployments(namespace).Get(ctx, name, metav1.GetOptions{})
		if err != nil {
			return nil, err
		}
		matchLabels = deployment.Spec.Selector.MatchLabels
	case "statefulsets":
		statefulSet, err := info.Kubernetes.AppsV1().StatefulSets(namespace).Get(ctx, name, metav1.GetOptions{})
		if err != nil {
			return nil, err
		}
		matchLabels = statefulSet.Spec.Selector.MatchLabels
	case "daemonsets":
		daemonSet, err := info.Kubernetes.AppsV1().DaemonSets(namespace).Get(ctx, name, metav1.GetOptions{})
		if err != nil {
			return nil, err
		}
		matchLabels = daemonSet.Spec.Selector.MatchLabels
	case "jobs":
		job, err := info.Kubernetes.BatchV1().Jobs(namespace).Get(ctx, name, metav1.GetOptions{})
		if err != nil {
			// job may not exist long time after it's done
			// so we return empty list here instead of error on not found
			if apierrors.IsNotFound(err) {
				return &corev1.PodList{}, nil
			}
			return nil, err
		}
		matchLabels = job.Spec.Selector.MatchLabels
	case "cronjobs":
		// jobs
		jobs, err := info.Kubernetes.BatchV1().Jobs(namespace).List(ctx, metav1.ListOptions{LabelSelector: labels.FormatLabels(map[string]string{"cronjob-name": name})})
		if err != nil {
			return nil, err
		}
		if len(jobs.Items) == 0 {
			return &corev1.PodList{}, nil
		}
		uidlist := make([]string, 0, len(jobs.Items))
		for _, job := range jobs.Items {
			uidlist = append(uidlist, string(job.UID))
		}
		req, err := labels.NewRequirement("controller-uid", selection.In, uidlist)
		if err != nil {
			return nil, err
		}
		return info.Kubernetes.CoreV1().Pods(namespace).List(ctx, metav1.ListOptions{LabelSelector: labels.NewSelector().Add(*req).String()})
	default:
		return nil, fmt.Errorf("unsupported resource type %s", resource)
	}
	opts := metav1.ListOptions{
		LabelSelector: labels.FormatLabels(matchLabels),
	}
	return info.Kubernetes.CoreV1().Pods(namespace).List(ctx, opts)
}

func ListResourceChildren(ctx context.Context, info *KubernetesClients, resource, namespace, name string) (any, error) {
	cli := info.Client

	listPods := func(ctx context.Context, cli client.Client, ns string, selector map[string]string) ([]corev1.Pod, error) {
		podlist := &corev1.PodList{}
		if err := cli.List(ctx, podlist, client.InNamespace(ns), client.MatchingLabels(selector)); err != nil {
			return nil, err
		}
		return podlist.Items, nil
	}

	switch resource {
	case "deployments":
		deployment := &appsv1.Deployment{}
		if err := cli.Get(ctx, types.NamespacedName{Namespace: namespace, Name: name}, deployment); err != nil {
			return nil, err
		}
		// replica set
		rsList := &appsv1.ReplicaSetList{}
		if err := cli.List(ctx, rsList,
			client.InNamespace(namespace),
			client.MatchingLabels(deployment.Spec.Selector.MatchLabels),
		); err != nil {
			return nil, err
		}
		return rsList.Items, nil
	case "replicasets":
		replicaSet := &appsv1.ReplicaSet{}
		if err := cli.Get(ctx, types.NamespacedName{Namespace: namespace, Name: name}, replicaSet); err != nil {
			return nil, err
		}
		return listPods(ctx, cli, namespace, replicaSet.Spec.Selector.MatchLabels)
	case "statefulsets":
		statefulSet := &appsv1.StatefulSet{}
		if err := cli.Get(ctx, types.NamespacedName{Namespace: namespace, Name: name}, statefulSet); err != nil {
			return nil, err
		}
		return listPods(ctx, cli, namespace, statefulSet.Spec.Selector.MatchLabels)
	case "daemonsets":
		daemonSet := &appsv1.DaemonSet{}
		if err := cli.Get(ctx, types.NamespacedName{Namespace: namespace, Name: name}, daemonSet); err != nil {
			return nil, err
		}
		return listPods(ctx, cli, namespace, daemonSet.Spec.Selector.MatchLabels)
	case "jobs":
		job := &batchv1.Job{}
		if err := cli.Get(ctx, types.NamespacedName{Namespace: namespace, Name: name}, job); err != nil {
			return nil, err
		}
		return listPods(ctx, cli, namespace, job.Spec.Selector.MatchLabels)
	case "cronjobs":
		cronJob := &batchv1.CronJob{}
		if err := cli.Get(ctx, types.NamespacedName{Namespace: namespace, Name: name}, cronJob); err != nil {
			return nil, err
		}
		// job
		joblist := &batchv1.JobList{}
		if err := cli.List(ctx, joblist, client.InNamespace(namespace),
			client.MatchingLabels(cronJob.Spec.JobTemplate.Spec.Selector.MatchLabels),
		); err != nil {
			return nil, err
		}
		return joblist.Items, nil
	default:
		return nil, fmt.Errorf("unsupported resource type %s", resource)
	}
}

func (s *ClusterResourcesAPI) ResourceExtensionGroup() api.Group {
	return api.
		NewGroup("/apis").
		Route(
			api.POST("/core/v1/nodes/{name}:cordon").
				Doc("Cordon node").
				To(s.NodeCordon),
			api.POST("/core/v1/nodes/{name}:uncordon").
				Doc("Uncordon node").
				To(s.NodeUncordon),
			api.GET("/core/v1/events").
				Doc("ListWatch events").
				To(s.ListWatchEvents).
				Param(
					api.QueryParam("namespace", "namespace").Optional(),
					api.QueryParam("kind", "pod,deployment etc").Optional(),
					api.QueryParam("name", "resource name").Optional(),
					api.QueryParam("watch", "watch events").Optional(),
					api.QueryParam("type", "event type").In("Normal", "Warning").Optional(),
					api.QueryParam("size", "Size").Optional(),
				),
			api.GET("/core/v1/namespaces/{namespace}/{resource}/{name}:pods").
				Doc("List resource pods").
				To(s.ResourcePods),
			api.GET("/core/v1/namespaces/{namespace}/{resource}/{name}:children").
				Doc("List resource pods").
				To(s.ResourceChildren),
			api.GET("/core/v1/namespaces/{namespace}/pods/{name}:log").
				Doc("Get pod log").
				To(s.PodLog).
				Param(
					api.QueryParam("container", "").Optional(),
					api.QueryParam("follow", "").Optional(),
					api.QueryParam("previous", "").Optional(),
					api.QueryParam("sinceSeconds", "").Optional(),
					api.QueryParam("sinceTime", "").Optional(),
					api.QueryParam("timestamps", "").Optional(),
					api.QueryParam("tailLines", "").Optional(),
					api.QueryParam("limitBytes", "").Optional(),
					api.QueryParam("insecureSkipTLSVerifyBackend", "").Optional(),
				),
			api.GET("/core/v1/namespaces/{namespace}/pods/{name}:exec").
				Doc("Exec in pod").
				To(s.PodExec).
				Param(
					api.QueryParam("stdin", "").Optional(),
					api.QueryParam("stdout", "").Optional(),
					api.QueryParam("stderr", "").Optional(),
					api.QueryParam("tty", "").Optional(),
					api.QueryParam("container", "").Optional(),
					api.QueryParam("command", "").Optional(),
				),
		)
}
