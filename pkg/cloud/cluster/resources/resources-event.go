package resources

import (
	"context"
	"net/http"
	"sort"
	"strings"

	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/fields"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/apimachinery/pkg/watch"
	"k8s.io/utils/ptr"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/rune/pkg/cloud/cluster"
)

func (s *ClusterResourcesAPI) ListWatchEvents(w http.ResponseWriter, r *http.Request) {
	s.OnClusterInfo(w, r, func(ctx context.Context, kubes *cluster.KubernetesClients, meta RequestMetadata) (any, error) {
		namespace := api.Query(r, "namespace", "")
		kind := api.Query(r, "kind", "")
		resourceName := api.Query(r, "name", "")
		eventType := api.Query(r, "type", "")
		size := api.Query(r, "size", 0)

		var fieldsSelectors []fields.Selector
		if namespace != "" {
			fieldsSelectors = append(fieldsSelectors, fields.OneTermEqualSelector("involvedObject.namespace", namespace))
		}
		if kind != "" {
			fieldsSelectors = append(fieldsSelectors, fields.OneTermEqualSelector("involvedObject.kind", kind))
		}
		if resourceName != "" {
			fieldsSelectors = append(fieldsSelectors, fields.OneTermEqualSelector("involvedObject.name", resourceName))
		}
		listOptions := metav1.ListOptions{
			FieldSelector: fields.AndSelectors(fieldsSelectors...).String(),
			// SendInitialEvents: &sendInitial,
		}
		eventInterface := kubes.Kubernetes.CoreV1().Events(namespace)

		eventsList, err := eventInterface.List(ctx, listOptions)
		if err != nil {
			return nil, err
		}
		needWatch := api.Query(r, "watch", false)
		var filteredEvents []corev1.Event
		for _, e := range eventsList.Items {
			if eventType != "" && !strings.EqualFold(eventType, e.Type) {
				continue
			}
			if e.GetObjectKind().GroupVersionKind().Empty() {
				e.SetGroupVersionKind(schema.GroupVersionKind{
					Version: "v1",
					Kind:    "Event",
				})
			}
			filteredEvents = append(filteredEvents, e)
		}
		sort.Slice(filteredEvents, func(i, j int) bool {
			return !filteredEvents[i].LastTimestamp.Before(&filteredEvents[j].LastTimestamp)
		})
		if size > 0 && len(filteredEvents) > size {
			filteredEvents = filteredEvents[:size]
		}
		if !needWatch {
			return filteredEvents, nil
		}
		writer, err := api.NewStreamEncoderFromRequest[any](w, r)
		if err != nil {
			return nil, err
		}
		defer writer.Close()
		// send init
		for _, sortE := range filteredEvents {
			if err := writer.Encode("events", sortE); err != nil {
				return nil, err
			}
		}
		watchEvents, err := eventInterface.Watch(ctx, listOptions)
		if err != nil {
			return nil, err
		}
		defer watchEvents.Stop()
		for {
			select {
			case e, ok := <-watchEvents.ResultChan():
				if !ok {
					return nil, nil
				}
				// 事件删除，不返回
				if e.Type == watch.Deleted || e.Type == watch.Added {
					continue
				}
				ev, ok := e.Object.(*corev1.Event)
				if !ok {
					continue
				}
				if eventType != "" && !strings.EqualFold(eventType, ev.Type) {
					continue
				}
				if err := writer.Encode("events", ev); err != nil {
					return nil, err
				}
			case <-ctx.Done():
				return nil, ctx.Err()
			}
		}
	})
}

func (s *ClusterResourcesAPI) Events(w http.ResponseWriter, r *http.Request) {
	s.OnClusterInfo(w, r, func(ctx context.Context, kubes *cluster.KubernetesClients, meta RequestMetadata) (any, error) {
		listOptions := metav1.ListOptions{}
		if err := metav1.Convert_url_Values_To_v1_ListOptions(ptr.To(r.URL.Query()), &listOptions, nil); err != nil {
			return nil, err
		}
		if meta.Name != "" {
			if meta.Kind == "" && meta.Resource != "" {
				gvk, err := kubes.Client.RESTMapper().KindFor(meta.GroupVersionResource)
				if err != nil {
					return nil, err
				}
				meta.Kind = gvk.Kind
			}
			fieldsSelectors := []fields.Selector{
				fields.OneTermEqualSelector("involvedObject.kind", meta.Kind),
				fields.OneTermEqualSelector("involvedObject.apiVersion", meta.GroupVersion().String()),
				fields.OneTermEqualSelector("involvedObject.name", meta.Name),
			}
			if meta.Namespace != "" {
				fieldsSelectors = append(fieldsSelectors, fields.OneTermEqualSelector("involvedObject.namespace", meta.Namespace))
			}
			listOptions.FieldSelector = fields.AndSelectors(fieldsSelectors...).String()
		}
		list, err := kubes.Kubernetes.CoreV1().Events(meta.Namespace).List(r.Context(), listOptions)
		if err != nil {
			return nil, err
		}
		events := list.Items

		// combine related events
		relatedEvents, err := listRelatedEvents(r.Context(), kubes.Client, meta)
		if err != nil {
			log := log.FromContext(r.Context())
			log.Error(err, "failed to list related events")
		}
		events = append(events, relatedEvents...)

		// page the list
		pageoptions := api.GetListOptions(r)
		pageoptions.Sort = ""
		// events should sort by it .LastTimestamp (in kubectl) but reversed
		sort.Sort((SortableEvents(events)))
		pageed := api.PageFromListOptions(events, pageoptions,
			func(e corev1.Event) string { return e.InvolvedObject.Name }, nil)
		return pageed, nil
	})
}

func listRelatedEvents(ctx context.Context, cli client.Client, meta RequestMetadata) ([]corev1.Event, error) {
	var events []corev1.Event
	switch meta.GroupVersionResource {
	case appsv1.SchemeGroupVersion.WithResource("deployments"):
		deployment := &appsv1.Deployment{}
		if err := cli.Get(ctx, types.NamespacedName{Namespace: meta.Namespace, Name: meta.Name}, deployment); err != nil {
			return nil, err
		}
		// replica set
		rsList := &appsv1.ReplicaSetList{}
		if err := cli.List(ctx, rsList, client.InNamespace(meta.Namespace), client.MatchingLabels(deployment.Spec.Selector.MatchLabels)); err != nil {
			return nil, err
		}
		for _, rs := range rsList.Items {
			rsEvents, err := listEvents(ctx, cli, rs.GetObjectKind().GroupVersionKind(), meta.Namespace, rs.Name)
			if err != nil {
				return nil, err
			}
			events = append(events, rsEvents...)
		}
	}
	return events, nil
}

func listEvents(ctx context.Context, cli client.Client, gvk schema.GroupVersionKind, namespace, name string) ([]corev1.Event, error) {
	fieldsSelectors := []fields.Selector{
		fields.OneTermEqualSelector("involvedObject.kind", gvk.Kind),
		fields.OneTermEqualSelector("involvedObject.apiVersion", gvk.GroupVersion().String()),
	}
	if name != "" {
		fieldsSelectors = append(fieldsSelectors, fields.OneTermEqualSelector("involvedObject.name", name))
	}
	if namespace != "" {
		fieldsSelectors = append(fieldsSelectors, fields.OneTermEqualSelector("involvedObject.namespace", namespace))
	}
	events := &corev1.EventList{}
	if err := cli.List(ctx, events, client.MatchingFieldsSelector{Selector: fields.AndSelectors(fieldsSelectors...)}); err != nil {
		return nil, err
	}
	return events.Items, nil
}

// SortableEvents implements sort.Interface for []api.Event based on the Timestamp field
type SortableEvents []corev1.Event

func (list SortableEvents) Len() int {
	return len(list)
}

func (list SortableEvents) Swap(i, j int) {
	list[i], list[j] = list[j], list[i]
}

func (list SortableEvents) Less(i, j int) bool {
	return !list[i].LastTimestamp.Time.Before(list[j].LastTimestamp.Time)
}
