package metadata

import (
	"context"
	"net/http"
	"slices"

	networkingv1 "k8s.io/api/networking/v1"
	storagev1 "k8s.io/api/storage/v1"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/base"
	"xiaoshiai.cn/rune/pkg/cloud/cluster"
	"xiaoshiai.cn/rune/pkg/cloud/cluster/resourcequota"
)

// MetadtaLister
// list metadata for cluster according to permission of organization or tenant
type MetadtaLister struct {
	CloudInfo cluster.CloudInfoGetter
	Store     store.Store
}

func (m MetadtaLister) ListSystemClusters(r *http.Request, tenant string) ([]cluster.ResourceMetadata, error) {
	metadatas, err := cluster.ListClusterMetadata(r, m.Store, false)
	if err != nil {
		return nil, err
	}
	// check by tenant and organization
	if tenant != "" {
		// make sure the cluster is used by the tenant
		refclusters, err := resourcequota.ListClustersUsedByTenant(r.Context(), m.Store, tenant)
		if err != nil {
			return nil, err
		}
		metadatas = slices.DeleteFunc(metadatas, func(i cluster.ResourceMetadata) bool {
			return !slices.ContainsFunc(refclusters, func(ref store.ObjectReference) bool {
				return ref.Equals(store.ObjectReference{Name: i.Name, Scopes: i.Scopes})
			})
		})
	}
	return metadatas, nil
}

func (m MetadtaLister) ListIngressClass(ctx context.Context, tenant string, clusterref store.ObjectReference) ([]cluster.ResourceMetadata, error) {
	cli, err := cluster.GetClusterKubeClient(ctx, m.CloudInfo, clusterref)
	if err != nil {
		return nil, err
	}
	ingressclasslist := &networkingv1.IngressClassList{}
	if err := cli.List(ctx, ingressclasslist); err != nil {
		return nil, err
	}
	useable := []cluster.ResourceMetadata{}
	for _, ingressclass := range ingressclasslist.Items {
		item := cluster.ResourceMetadataFrom(&ingressclass)
		// check permission
		ok, err := permitIngressClass(ctx, m.Store, clusterref, item, tenant)
		if err != nil {
			return nil, err
		}
		if !ok {
			continue
		}
		useable = append(useable, item)
	}
	return useable, nil
}

func (m MetadtaLister) ListStorageClass(ctx context.Context, tenant string, clusterref store.ObjectReference) ([]cluster.ResourceMetadata, error) {
	cli, err := cluster.GetClusterKubeClient(ctx, m.CloudInfo, clusterref)
	if err != nil {
		return nil, err
	}
	storageclasslist := &storagev1.StorageClassList{}
	if err := cli.List(ctx, storageclasslist); err != nil {
		return nil, err
	}
	useable := []cluster.ResourceMetadata{}
	for _, storageclass := range storageclasslist.Items {
		item := cluster.ResourceMetadataFrom(&storageclass)
		// check permission
		ok, err := permitStorageClass(ctx, clusterref, item, tenant)
		if err != nil {
			return nil, err
		}
		if !ok {
			continue
		}
		useable = append(useable, item)
	}
	return useable, nil
}

func PermitCluster(ctx context.Context, store store.Store, clusterref store.ObjectReference, tenant, org string) (bool, error) {
	// check by tenant and organization
	if tenant != "" {
		quota, err := resourcequota.GetResourceQuota(ctx, store, tenant, org, clusterref)
		if err != nil {
			if errors.IsNotFound(err) {
				return false, nil
			}
			return false, err
		}
		_ = quota
	}
	return true, nil
}

func PermitIngressClass(ctx context.Context, store store.Store, cli client.Client, clusterref store.ObjectReference, ingressClassName string, tenant, org string) (bool, error) {
	ingressclass := &networkingv1.IngressClass{}
	if err := cli.Get(ctx, client.ObjectKey{Name: ingressClassName}, ingressclass); err != nil {
		return false, err
	}
	metadata := cluster.ResourceMetadataFrom(ingressclass)
	return permitIngressClass(ctx, store, clusterref, metadata, tenant)
}

func permitIngressClass(ctx context.Context, storage store.Store, clusterref store.ObjectReference, metadata cluster.ResourceMetadata, tenant string) (bool, error) {
	// system cluster
	if len(clusterref.Scopes) == 0 {
		// public
		if metadata.Public {
			return true, nil
		}
		// this tenant's gateway
		if metadata.Labels != nil && metadata.Labels[base.LabelTenant] == tenant {
			return true, nil
		}
		return false, nil
	} else {
		// not public
		if metadata.Public {
			return true, nil
		}
		// not this tenant's cluster
		if tenant != "" {
			if clustertenant := base.TenantFromScopes(clusterref.Scopes...); clustertenant != tenant {
				return false, nil
			}
		}
		return true, nil
	}
}

func PermitStorageClass(ctx context.Context, store store.Store, cli client.Client, clusterref store.ObjectReference, storageClassName string, tenant, org string) (bool, error) {
	storageclass := &storagev1.StorageClass{}
	if err := cli.Get(ctx, client.ObjectKey{Name: storageClassName}, storageclass); err != nil {
		return false, err
	}
	metadata := cluster.ResourceMetadataFrom(storageclass)
	return permitStorageClass(ctx, clusterref, metadata, tenant)
}

func permitStorageClass(_ context.Context, clusterref store.ObjectReference, metadata cluster.ResourceMetadata, tenant string) (bool, error) {
	if !metadata.Public {
		return false, nil
	}
	// if is system cluster
	// allow tenant self storage class
	if len(clusterref.Scopes) == 0 {
		// public storage class
		if metadata.Public {
			return true, nil
		}
		// or tenant storage class
		if metadata.Labels != nil && metadata.Labels[base.LabelTenant] == tenant {
			return true, nil
		}
		return false, nil
	} else {
		// not public
		if !metadata.Public {
			return false, nil
		}
		// not this tenant's cluster
		if tenant != "" {
			// not this tenant's cluster
			if clustertenant, _ := base.TenantOrganizationFromScopes(clusterref.Scopes...); clustertenant != tenant {
				return false, nil
			}
		}
		return true, nil
	}
}
