package metadata

import (
	"context"
	"net/http"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/base"
	"xiaoshiai.cn/rune/pkg/cloud/cluster"
)

func NewAPI(store store.Store, cloudinfo cluster.CloudInfoGetter) *API {
	return &API{
		Store:     store,
		CloudInfo: cloudinfo,
	}
}

type API struct {
	Store     store.Store
	CloudInfo cluster.CloudInfoGetter
}

func (a *API) ListSystemClusterMetadata(w http.ResponseWriter, r *http.Request) {
	base.OnTenant(w, r, func(ctx context.Context, tenant string) (any, error) {
		return MetadtaLister{CloudInfo: a.CloudInfo, Store: a.Store}.ListSystemClusters(r, tenant)
	})
}

func (a *API) ListSystemClusterIngressClass(w http.ResponseWriter, r *http.Request) {
	a.onMetadataLister(w, r, func(ctx context.Context, lister MetadtaLister, tenant, clustername string) (any, error) {
		return lister.ListIngressClass(ctx, tenant, store.ObjectReference{Name: clustername})
	})
}

func (a *API) ListSystemClusterStorageClass(w http.ResponseWriter, r *http.Request) {
	a.onMetadataLister(w, r, func(ctx context.Context, lister MetadtaLister, tenant, clustername string) (any, error) {
		return lister.ListStorageClass(ctx, tenant, store.ObjectReference{Name: clustername})
	})
}

func (a *API) clustersGroup() api.Group {
	return api.
		NewGroup("/clustermetadata").
		Route(
			api.GET("").
				To(a.ListSystemClusterMetadata).
				Operation("list available cluster metadata").
				Param(api.QueryParam("marketapplication-type", "filter clusters by type, Container Virtualmachine").Optional()).
				Response([]cluster.ResourceMetadata{}),

			// container cluster resources
			api.GET("/{cluster}/ingressclasses").
				To(a.ListSystemClusterIngressClass).
				Operation("list available ingress class").
				Response([]cluster.ResourceMetadata{}),

			api.GET("/{cluster}/storageclasses").
				To(a.ListSystemClusterStorageClass).
				Operation("list available storage class").
				Response([]cluster.ResourceMetadata{}),
		)
}

func (a *API) onMetadataLister(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, lister MetadtaLister, tenant, clustername string) (any, error)) {
	base.OnTenant(w, r, func(ctx context.Context, tenant string) (any, error) {
		clustername := api.Path(r, "cluster", "")
		if clustername == "" {
			return nil, errors.NewBadRequest("cluster name is required")
		}
		lister := MetadtaLister{
			CloudInfo: a.CloudInfo, Store: a.Store,
		}
		return fn(ctx, lister, tenant, clustername)
	})
}

func (a *API) Group() api.Group {
	return api.
		NewGroup("").
		Tag("Clusters Metadata").
		SubGroup(
			a.clustersGroup(),
		)
}
