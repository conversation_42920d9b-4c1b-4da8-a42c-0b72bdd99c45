package operation

import (
	"context"
	"fmt"
	"io"
	"net/url"
	"time"

	"xiaoshiai.cn/common/httpclient"
)

const (
	PrometheusServiceName   = "prometheus"
	AlertManagerServiceName = "alertmanager"
)

type PrometheusQueryRangeOptions struct {
	// Prometheus expression query string.
	Query string
	// start=<rfc3339 | unix_timestamp>: Start timestamp, inclusive.
	Start time.Time
	// end=<rfc3339 | unix_timestamp>: End timestamp, inclusive.
	End time.Time
	// Query resolution step width in duration format or float number of seconds.
	Step time.Duration
	// Evaluation timeout. Optional. Defaults to and is capped by the value of the -query.timeout flag.
	Timeout time.Duration
}

func (o PrometheusQueryRangeOptions) Values() url.Values {
	values := make(url.Values)
	values.Set("query", o.Query)
	if !o.Start.IsZero() {
		values.Set("start", o.Start.Format(time.RFC3339))
	}
	if !o.End.IsZero() {
		values.Set("end", o.End.Format(time.RFC3339))
	}
	if o.Step > 0 {
		values.Set("step", o.Step.String())
	}
	if o.Timeout > 0 {
		values.Set("timeout", o.Timeout.String())
	}
	return values
}

func (s ContainerOperation) PrometheusQueryRange(ctx context.Context, options PrometheusQueryRangeOptions, into any) error {
	cliconfig, err := s.Info.GetServiceConfig(PrometheusServiceName)
	if err != nil {
		return err
	}
	cli := httpclient.NewClientFromClientConfig(cliconfig)
	return cli.Get("/api/v1/query_range").Queries(options.Values()).Return(into).Send(ctx)
}

type PrometheusQueryOptions struct {
	// Prometheus expression query string.
	Query string
	// Time Evaluation timestamp. Optional.
	Time time.Time
	// Evaluation timeout. Optional. Defaults to and is capped by the value of the -query.timeout flag.
	Timeout time.Duration
}

func (o PrometheusQueryOptions) Values() url.Values {
	values := make(url.Values)
	values.Set("query", o.Query)
	if !o.Time.IsZero() {
		values.Set("time", o.Time.Format(time.RFC3339))
	}
	if o.Timeout > 0 {
		values.Set("timeout", o.Timeout.String())
	}
	return values
}

// PrometheusQuery
// https://prometheus.io/docs/prometheus/latest/querying/api/#instant-queries
func (s ContainerOperation) PrometheusQuery(ctx context.Context, options PrometheusQueryOptions, into any) error {
	cliconfig, err := s.Info.GetServiceConfig(PrometheusServiceName)
	if err != nil {
		return err
	}
	cli := httpclient.NewClientFromClientConfig(cliconfig)
	return cli.Get("/api/v1/query").Queries(options.Values()).Return(into).Send(ctx)
}

// PrometheusRules
// https://prometheus.io/docs/prometheus/latest/querying/api/#rules
type PrometheusRulesOptions struct {
	Type           string // alert|record
	RuleName       []string
	RuleGroup      []string
	File           []string
	ExcludeAlerts  bool
	Match          []string
	GroupLimit     int
	GroupNextToken string
}

func (o PrometheusRulesOptions) Values() url.Values {
	values := make(url.Values)
	if o.Type != "" {
		values.Set("type", o.Type)
	}
	if len(o.RuleName) > 0 {
		values["rule_name"] = o.RuleName
	}
	if len(o.RuleGroup) > 0 {
		values["rule_group"] = o.RuleGroup
	}
	if o.ExcludeAlerts {
		values.Set("exclude_alerts", "true")
	}
	if len(o.Match) > 0 {
		values["match"] = o.Match
	}
	if o.GroupLimit > 0 {
		values.Set("group_limit", fmt.Sprintf("%d", o.GroupLimit))
	}
	if o.GroupNextToken != "" {
		values.Set("group_next_token", o.GroupNextToken)
	}
	return values
}

func (s ContainerOperation) PrometheusRules(ctx context.Context, options PrometheusRulesOptions, into any) error {
	cliconfig, err := s.Info.GetServiceConfig(PrometheusServiceName)
	if err != nil {
		return err
	}
	cli := httpclient.NewClientFromClientConfig(cliconfig)
	return cli.Get("/api/v1/rules").Queries(options.Values()).Return(into).Send(ctx)
}

// 查询所有的指标名称
func (s ContainerOperation) PrometheusMetricsNames(ctx context.Context, into any) error {
	cliconfig, err := s.Info.GetServiceConfig(PrometheusServiceName)
	if err != nil {
		return err
	}
	cli := httpclient.NewClientFromClientConfig(cliconfig)
	return cli.Get("/api/v1/label/__name__/values").Return(into).Send(ctx)
}

func (s ContainerOperation) AlertManagerSilence(ctx context.Context, body io.Reader, into any) error {
	cliconfig, err := s.Info.GetServiceConfig(AlertManagerServiceName)
	if err != nil {
		return err
	}
	cli := httpclient.NewClientFromClientConfig(cliconfig)
	return cli.Post("/api/v2/silences").Body(body, "application/json").Return(into).Send(ctx)
}

func (s ContainerOperation) AlertManagerCancelSilence(ctx context.Context, id string) error {
	cliconfig, err := s.Info.GetServiceConfig(AlertManagerServiceName)
	if err != nil {
		return err
	}
	cli := httpclient.NewClientFromClientConfig(cliconfig)
	url := fmt.Sprintf("/api/v2/silence/%s", id)
	return cli.Delete(url).Send(ctx)
}

func (s ContainerOperation) AlertManagerGetSilence(ctx context.Context, id string, into any) error {
	cliconfig, err := s.Info.GetServiceConfig(AlertManagerServiceName)
	if err != nil {
		return err
	}
	cli := httpclient.NewClientFromClientConfig(cliconfig)
	url := fmt.Sprintf("/api/v2/silence/%s", id)
	return cli.Get(url).Return(into).Send(ctx)
}
