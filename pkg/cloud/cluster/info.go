package cluster

import (
	"context"
	"fmt"
	"hash/fnv"
	"net/http"
	"net/url"
	"strings"
	"sync"

	"k8s.io/apimachinery/pkg/api/meta"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/dynamic"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
	"k8s.io/utils/ptr"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/client/apiutil"
	ctrlcluster "sigs.k8s.io/controller-runtime/pkg/cluster"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/httpclient"
	"xiaoshiai.cn/common/store"
	apiserverscheme "xiaoshiai.cn/rune/pkg/cloud/kube"
)

type CloudInfoGetter interface {
	Get(ctx context.Context, ref store.ObjectReference) (CloudInfo, error)
}

type CloudInfoHolder interface {
	CloudInfoGetter
	Sync(ctx context.Context, cluster *Cluster) (CloudInfo, error)
	Remove(ctx context.Context, ref store.ObjectReference) error
}

type CloudInfo interface {
	Reference() store.ObjectReference
	Type() ClusterType
	KubernetesConfig() (*KubernetesClients, error)
}

var _ CloudInfoHolder = &defaultCloudInfoHolder{}

func NewDefaultCloudInfoHolder(ctx context.Context) *defaultCloudInfoHolder {
	return &defaultCloudInfoHolder{
		basectx: ctx,
		infos:   make(map[string]*defaultCloudInfo),
	}
}

type defaultCloudInfoHolder struct {
	mu      sync.RWMutex
	basectx context.Context
	infos   map[string]*defaultCloudInfo
}

// Get implements CloudInfoHolder.
func (d *defaultCloudInfoHolder) Get(ctx context.Context, ref store.ObjectReference) (CloudInfo, error) {
	if ref.Name == "" {
		return nil, errors.NewInvalid("cluster reference", ref.String(), fmt.Errorf(".name is required"))
	}
	d.mu.RLock()
	defer d.mu.RUnlock()
	info, ok := d.infos[ref.String()]
	if !ok {
		return nil, errors.NewNotFound("cluster info", ref.String())
	}
	return info, nil
}

// Sync implements CloudInfoHolder.
func (d *defaultCloudInfoHolder) Sync(ctx context.Context, cluster *Cluster) (CloudInfo, error) {
	hash := computeHash(cluster.Kube, cluster.Params)
	ref := store.ObjectReferenceFrom(cluster)

	d.mu.RLock()
	existsinfo, ok := d.infos[ref.String()]
	d.mu.RUnlock()
	if ok && existsinfo.confighash == hash {
		return existsinfo, nil
	}
	// cancel the old info
	if existsinfo != nil {
		existsinfo.Close()
	}
	d.mu.Lock()
	defer d.mu.Unlock()
	// update the info
	infoctx, cancel := context.WithCancel(ctx)

	newinfo := &defaultCloudInfo{
		ref:         ref,
		clustertype: cluster.Type,
		confighash:  hash,
		cancel:      cancel,
		ready:       false,
	}
	d.infos[ref.String()] = newinfo

	restconfig, err := d.GetRestConfig(cluster)
	if err != nil {
		return nil, err
	}
	newkubeclients, err := NewKubernetesClients(infoctx, restconfig)
	if err != nil {
		return nil, err
	}
	newkubeclients.KubeCluster = cluster.Kube
	newinfo.kubernetes = newkubeclients

	newinfo.ready = true

	return newinfo, nil
}

func (d *defaultCloudInfoHolder) GetCloudConfig(cluster *Cluster, kubeclients *KubernetesClients) (*httpclient.ClientConfig, error) {
	if kube := cluster.Kube; kube.Config != "" {
		clicoonfig := kubeclients.GetServiceProxyAddr(def(kube.Namespace, "ismc"), def(kube.Service, "ismc-agent"), def(kube.Port, 80))
		return clicoonfig, nil
	}
	return nil, errors.NewInvalid("cluster", cluster.Name, fmt.Errorf("cloud config is not supported"))
}

func (d *defaultCloudInfoHolder) GetRestConfig(cluster *Cluster) (*rest.Config, error) {
	if cluster.Kube.Config != "" {
		kubeconfig, err := clientcmd.RESTConfigFromKubeConfig([]byte(cluster.Kube.Config))
		if err != nil {
			return nil, err
		}
		return kubeconfig, nil
	}
	return nil, errors.NewInvalid("cluster", cluster.Name, fmt.Errorf("rest config is not set"))
}

// Remove implements CloudInfoHolder.
func (d *defaultCloudInfoHolder) Remove(ctx context.Context, ref store.ObjectReference) error {
	d.mu.Lock()
	defer d.mu.Unlock()
	key := ref.String()
	info, ok := d.infos[key]
	if !ok {
		return nil
	}
	info.Close()
	delete(d.infos, key)
	return nil
}

var _ CloudInfo = &defaultCloudInfo{}

type defaultCloudInfo struct {
	ref         store.ObjectReference
	clustertype ClusterType
	kubernetes  *KubernetesClients
	// ready is true if the info is ready
	ready bool
	// confighash is the hash of the config
	// it is used to check if the config is changed
	confighash uint32
	cancel     context.CancelFunc
}

func (d *defaultCloudInfo) Close() {
	d.cancel()
}

// KubernetesConfig implements CloudInfo.
func (d *defaultCloudInfo) KubernetesConfig() (*KubernetesClients, error) {
	if !d.ready {
		return nil, errors.NewInvalid("cloud info", d.ref.Name, fmt.Errorf("cloud info is not ready"))
	}
	if d.kubernetes == nil {
		return nil, errors.NewUnsupported(fmt.Sprintf("cluster type %s not a kubernetes cluster", d.clustertype))
	}
	return d.kubernetes, nil
}

// Name implements CloudInfo.
func (d *defaultCloudInfo) Reference() store.ObjectReference {
	return d.ref
}

// Type implements CloudInfo.
func (d *defaultCloudInfo) Type() ClusterType {
	return d.clustertype
}

func computeHash(values ...any) uint32 {
	hasher := fnv.New32a()
	hasher.Reset()
	fmt.Fprintf(hasher, "%#v", values...)
	return hasher.Sum32()
}

type KubernetesClients struct {
	RestConfig    *rest.Config
	cancel        context.CancelFunc
	Client        client.Client
	Cached        client.Client
	Kubernetes    kubernetes.Interface
	Dynamic       dynamic.Interface
	BaseURL       url.URL
	Transport     http.RoundTripper
	KubeCluster   KubeCluster
	ProxyServices map[string]ServiceProxyTarget
}

type ServiceProxyTarget struct {
	Namespace  string
	Service    string
	Port       int
	PathPrefix string
}

func NewKubernetesClients(ctx context.Context, restconfig *rest.Config) (*KubernetesClients, error) {
	restconfig = rest.CopyConfig(restconfig)
	// set default QPS
	restconfig.QPS = 1024
	restconfig.Burst = 4096

	tranport := restconfig.Transport
	if tranport == nil {
		newtranport, err := rest.TransportFor(restconfig)
		if err != nil {
			return nil, err
		}
		tranport = newtranport
	}
	httpcli := &http.Client{
		Transport: tranport,
		Timeout:   restconfig.Timeout,
	}
	baseURL, _, err := rest.DefaultServerUrlFor(restconfig)
	if err != nil {
		return nil, err
	}
	dynamiccli, err := dynamic.NewForConfigAndClient(restconfig, httpcli)
	if err != nil {
		return nil, err
	}
	kubernetescs, err := kubernetes.NewForConfigAndClient(restconfig, httpcli)
	if err != nil {
		return nil, err
	}
	ctrlc, err := ctrlcluster.New(restconfig, func(o *ctrlcluster.Options) {
		o.Scheme = apiserverscheme.Scheme
		o.HTTPClient = httpcli
		o.Client.Cache = &client.CacheOptions{Unstructured: true}
	})
	if err != nil {
		return nil, err
	}

	ctrlclient, err := client.New(restconfig, client.Options{
		HTTPClient: httpcli, Scheme: apiserverscheme.Scheme,
	})
	if err != nil {
		return nil, err
	}
	cli := VersionedClient{Client: ctrlclient}

	ctx, cancel := context.WithCancel(ctx)
	go ctrlc.Start(ctx)

	newclients := &KubernetesClients{
		cancel:        cancel,
		Client:        cli,
		Cached:        ctrlc.GetClient(),
		Kubernetes:    kubernetescs,
		Dynamic:       dynamiccli,
		RestConfig:    restconfig,
		BaseURL:       *baseURL,
		Transport:     tranport,
		ProxyServices: map[string]ServiceProxyTarget{},
	}
	return newclients, nil
}

func (c *KubernetesClients) Close() {
	if c.cancel != nil {
		c.cancel()
	}
}

func (c *KubernetesClients) GetServiceConfig(svc string) (*httpclient.ClientConfig, error) {
	return c.GetServiceProxyConfig(svc, false)
}

func (c *KubernetesClients) GetServiceProxyConfig(svc string, websocket bool) (*httpclient.ClientConfig, error) {
	dest, ok := c.ProxyServices[svc]
	if !ok {
		return nil, errors.NewUnsupported(fmt.Sprintf("service %s not defined", svc))
	}
	target := ptr.To(c.BaseURL)
	// apiserver proxy not support websocket query
	// we proxy to agent service for websocket
	if websocket {
		target := ptr.To(c.BaseURL)
		target.Path = fmt.Sprintf("/api/v1/namespaces/%s/services/%s:%d/proxy/proxy/%s.%s:%d",
			def(c.KubeCluster.Namespace, "rune"), def(c.KubeCluster.Service, "rune-agent"), def(c.KubeCluster.Port, 80),
			dest.Service, dest.Namespace, dest.Port)
		return &httpclient.ClientConfig{Server: target, RoundTripper: c.Transport, DialContext: c.RestConfig.Dial}, nil
	} else {
		target.Path += fmt.Sprintf("/api/v1/namespaces/%s/services/%s:%d/proxy/", dest.Namespace, dest.Service, dest.Port)
	}
	return &httpclient.ClientConfig{Server: target, RoundTripper: c.Transport, DialContext: c.RestConfig.Dial}, nil
}

func (c *KubernetesClients) GetServiceProxyAddr(namespace, name string, port int) *httpclient.ClientConfig {
	target := ptr.To(c.BaseURL)
	target.Path += fmt.Sprintf("/api/v1/namespaces/%s/services/%s:%d/proxy/", namespace, name, port)
	return &httpclient.ClientConfig{Server: target, RoundTripper: c.Transport, DialContext: c.RestConfig.Dial}
}

func (c *KubernetesClients) GetWebsocketServiceProxyAddr(namespace, name string, port int) *httpclient.ClientConfig {
	if kube := c.KubeCluster; kube.Config != "" {
		target := ptr.To(c.BaseURL)
		target.Path = fmt.Sprintf("/api/v1/namespaces/%s/services/%s:%d/proxy/proxy/%s.%s:%d",
			def(kube.Namespace, "rune"), def(kube.Service, "rune-agent"), def(kube.Port, 80),
			name, namespace, port)
		return &httpclient.ClientConfig{Server: target, RoundTripper: c.Transport, DialContext: c.RestConfig.Dial}
	}
	return c.GetServiceProxyAddr(namespace, name, port)
}

func def[T comparable](v, def T) T {
	var zero T
	if v == zero {
		return def
	}
	return v
}

func (c *KubernetesClients) GetKubernetesAddr() *httpclient.ClientConfig {
	return &httpclient.ClientConfig{
		Server:       &c.BaseURL,
		RoundTripper: c.Transport,
		DialContext:  c.RestConfig.Dial,
	}
}

var _ client.Client = &VersionedClient{}

// VersionedClient is a client.Client that sets the GroupVersionKind on objects it reads.
// When use an no cache client, th returned object will not have the GroupVersionKind set.
// it cause by the client.Client use [serializer.WithoutConversionCodecFactory] as default codec factory.
type VersionedClient struct {
	client.Client
}

// Get implements client.Client.
func (v VersionedClient) Get(ctx context.Context, key types.NamespacedName, obj client.Object, opts ...client.GetOption) error {
	defer v.setGroupVersionKind(obj)
	return v.Client.Get(ctx, key, obj, opts...)
}

// List implements client.Client.
func (v VersionedClient) List(ctx context.Context, list client.ObjectList, opts ...client.ListOption) error {
	defer v.setListGroupVersionKind(list)
	return v.Client.List(ctx, list, opts...)
}

func (v VersionedClient) setGroupVersionKind(obj client.Object) {
	gvk, err := apiutil.GVKForObject(obj, v.Client.Scheme())
	if err != nil || gvk.Kind == "" {
		return
	}
	obj.GetObjectKind().SetGroupVersionKind(gvk)
}

func (v VersionedClient) setListGroupVersionKind(list client.ObjectList) {
	listgvk, err := apiutil.GVKForObject(list, v.Client.Scheme())
	if err != nil || listgvk.Kind == "" {
		return
	}
	list.GetObjectKind().SetGroupVersionKind(listgvk)

	gvk := listgvk
	gvk.Kind = strings.TrimSuffix(gvk.Kind, "List")
	meta.EachListItem(list, func(obj runtime.Object) error {
		obj.GetObjectKind().SetGroupVersionKind(gvk)
		return nil
	})
}
