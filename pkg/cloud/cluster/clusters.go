package cluster

import (
	"context"
	"fmt"
	"net/http"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/utils/ptr"
	ctrllog "sigs.k8s.io/controller-runtime/pkg/log"
	"xiaoshiai.cn/common/controller"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/rand"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/base"
)

func init() {
	ctrllog.SetLogger(log.DefaultLogger)
}

const (
	ResourceCPU     = corev1.ResourceCPU
	ResourceMemory  = corev1.ResourceMemory
	ResourceStorage = corev1.ResourceStorage
)

type Cluster struct {
	store.ObjectMeta `json:",inline"`
	//  Type is the type of the cluster
	Type ClusterType `json:"type"`
	// Published then tenants can use the cluster
	Published bool `json:"published"`
	// Kube is the kubernetes cluster information
	Kube KubeCluster `json:"kube,omitempty"`
	// Params is the parameters of the cluster
	Params map[string]string `json:"params,omitempty"`
	// Status is the status of the cluster
	Status ClusterStatus `json:"status"`
}

type KubeCluster struct {
	// Config is the kubeconfig
	Config    string `json:"config"`
	Namespace string `json:"namespace"`
	Service   string `json:"service"`
	Port      int    `json:"port"`
}

type EdgeCluster struct {
	DeviceID string `json:"deviceID,omitempty"`
	Token    string `json:"token,omitempty"`
}

// +enum
type ClusterType string

const (
	// container cluster
	ClisterXCMP           ClusterType = "XCMP"
	ClusterTypeKubernetes ClusterType = "Kubernetes"
	// virtual machine cluster
	ClusterTypeVmware ClusterType = "VMware"
	ClusterTypeAliyun ClusterType = "Aliyun"
	ClusterTypeHuawei ClusterType = "Huawei"
)

// +enum
type ClusterPhase string

const (
	ClusterPhaseHealthy ClusterPhase = "Healthy"
	ClusterPhaseUnknown ClusterPhase = "Unkown"
	ClusterPhaseOffline ClusterPhase = "Offline"
)

type ClusterStatus struct {
	Connected                 bool                   `json:"connected"`
	LastConnectedTimestamp    store.Time             `json:"lastConnectedTimestamp"`
	LastDisconnectedTimestamp store.Time             `json:"lastDisconnectedTimestamp"`
	Phase                     ClusterPhase           `json:"phase"`
	Version                   VersionInfo            `json:"version,omitempty"`
	AgentVersion              VersionInfo            `json:"agentVersion,omitempty"`
	Conditions                []controller.Condition `json:"conditions"`
}

type VersionInfo struct {
	Name       string `json:"name"`
	Vendor     string `json:"vendor"`
	GitVersion string `json:"gitVersion"`
	GitCommit  string `json:"gitCommit"`
	BuildDate  string `json:"buildDate"`
}

type API struct {
	Storage   store.Store
	CloudInfo CloudInfoGetter
	Resources *ClusterResourcesAPI
}

func NewAPI(storage store.Store, clusterinfo CloudInfoGetter) *API {
	return &API{
		Storage:   storage,
		CloudInfo: clusterinfo,
		Resources: &ClusterResourcesAPI{
			Storage:   storage,
			CloudInfo: clusterinfo,
		},
	}
}

type ListClustersOptions struct {
	api.ListOptions `json:",inline"`
	Type            ClusterType `json:"type"`
	Published       *bool       `json:"published,omitempty"`
}

type CreateClusterOptions struct {
	DeviceIDPrefix string `json:"deviceIDPrefix"`
}

// CreateCluster implements ClusterService.
func CreateCluster(ctx context.Context, storage store.Store, obj *Cluster, options CreateClusterOptions) (*Cluster, error) {
	if obj.Name == "" {
		obj.Name = "cluster-" + rand.RandomHex(8)
	}
	if err := validateCluster(obj); err != nil {
		return nil, err
	}
	if err := storage.Create(ctx, obj); err != nil {
		return nil, err
	}
	return obj, nil
}

// GetCluster implements ClusterService.

type GetClusterOptions struct {
	Published bool `json:"published"`
}

func GetCluster(ctx context.Context, scoped store.Store, name string, options GetClusterOptions) (*Cluster, error) {
	cluster := &Cluster{}
	if err := scoped.Get(ctx, name, cluster); err != nil {
		return nil, err
	}
	if options.Published && !cluster.Published {
		return nil, errors.NewNotFound("clusters", name)
	}
	return cluster, nil
}

// ListClusters implements ClusterService.
func ListClusters(ctx context.Context, scoped store.Store, options ListClustersOptions) (store.List[Cluster], error) {
	list := store.List[Cluster]{}
	opts := base.ListOptionsToStoreListOptions(options.ListOptions)
	if options.Type != "" {
		opts = append(opts, store.WithFieldRequirements(store.RequirementEqual("type", string(options.Type))))
	}
	if pub := options.Published; pub != nil {
		opts = append(opts, store.WithFieldRequirements(store.RequirementEqual("published", *pub)))
	}
	if err := scoped.List(ctx, &list, opts...); err != nil {
		return list, err
	}
	return list, nil
}

// RemoveCluster implements ClusterService.
func RemoveCluster(ctx context.Context, scoped store.Store, name string) (*Cluster, error) {
	cluster := &Cluster{ObjectMeta: store.ObjectMeta{Name: name}}
	if err := scoped.Delete(ctx, cluster, store.WithDeletePropagation(store.DeletePropagationForeground)); err != nil {
		return nil, err
	}
	return cluster, nil
}

// UpdateCluster implements ClusterService.
func UpdateCluster(ctx context.Context, scoped store.Store, name string, cluster *Cluster) (*Cluster, error) {
	if cluster.Name != name {
		return nil, errors.NewBadRequest("cluster name is not match")
	}
	exists, err := GetCluster(ctx, scoped, name, GetClusterOptions{})
	if err != nil {
		return nil, err
	}
	// do not update cluster type
	cluster.Type = exists.Type
	if cluster.Labels == nil {
		cluster.Labels = map[string]string{}
	}
	if err := validateCluster(cluster); err != nil {
		return nil, err
	}
	if err := scoped.Update(ctx, cluster); err != nil {
		return nil, err
	}
	return cluster, nil
}

func validateCluster(c *Cluster) error {
	if c.Kube.Config == "" {
		return errors.NewInvalid("cluster", c.Name, fmt.Errorf("kube config is required"))
	}
	return nil
}

func (a *API) ListClusters(w http.ResponseWriter, r *http.Request) {
	OnOrOnTenantScope(w, r, func(ctx context.Context, scopes []store.Scope) (any, error) {
		listopt := ListClustersOptions{
			ListOptions: api.GetListOptions(r),
		}
		return ListClusters(ctx, a.Storage.Scope(scopes...), listopt)
	})
}

func (a *API) CreateCluster(w http.ResponseWriter, r *http.Request) {
	OnOrOnTenantScope(w, r, func(ctx context.Context, scopes []store.Scope) (any, error) {
		obj := &Cluster{}
		if err := api.Body(r, obj); err != nil {
			return nil, err
		}
		return CreateCluster(ctx, a.Storage.Scope(scopes...), obj, CreateClusterOptions{})
	})
}

func (a *API) GetCluster(w http.ResponseWriter, r *http.Request) {
	OnCluster(w, r, func(ctx context.Context, ref store.ObjectReference) (any, error) {
		return GetCluster(ctx, a.Storage.Scope(ref.Scopes...), ref.Name, GetClusterOptions{})
	})
}

func (a *API) UpdateCluster(w http.ResponseWriter, r *http.Request) {
	OnCluster(w, r, func(ctx context.Context, ref store.ObjectReference) (any, error) {
		obj := &Cluster{}
		if err := api.Body(r, obj); err != nil {
			return nil, err
		}
		return UpdateCluster(ctx, a.Storage.Scope(ref.Scopes...), ref.Name, obj)
	})
}

func (a *API) DeleteCluster(w http.ResponseWriter, r *http.Request) {
	OnCluster(w, r, func(ctx context.Context, ref store.ObjectReference) (any, error) {
		return RemoveCluster(ctx, a.Storage.Scope(ref.Scopes...), ref.Name)
	})
}

func (a *API) ListClusterSummaries(w http.ResponseWriter, r *http.Request) {
	OnOrOnTenantScope(w, r, func(ctx context.Context, scopes []store.Scope) (any, error) {
		listopt := ListClustersOptions{
			ListOptions: api.GetListOptions(r),
			Published:   ptr.To(true),
		}
		clusters, err := ListClusters(ctx, a.Storage.Scope(scopes...), listopt)
		if err != nil {
			return nil, err
		}
		// remove unnecessary fields
		// list cluster do not return full config
		// this api is also opened to normal user
		for i, item := range clusters.Items {
			clusters.Items[i] = OmmitClusterConfig(item)
		}
		return clusters, nil
	})
}

func OmmitClusterConfig(c Cluster) Cluster {
	c.Kube = KubeCluster{}
	c.Params = nil
	return c
}

func (a *API) GetClusterSummary(w http.ResponseWriter, r *http.Request) {
	OnCluster(w, r, func(ctx context.Context, ref store.ObjectReference) (any, error) {
		c, err := GetCluster(ctx, a.Storage.Scope(ref.Scopes...), ref.Name, GetClusterOptions{Published: true})
		if err != nil {
			return nil, err
		}
		return OmmitClusterConfig(*c), nil
	})
}

func OnClusterInfo(w http.ResponseWriter, r *http.Request, infos CloudInfoGetter, fn func(ctx context.Context, kubes *KubernetesClients, meta RequestMetadata) (any, error)) {
	OnCluster(w, r, func(ctx context.Context, ref store.ObjectReference) (any, error) {
		metadata := ResourceMetaFromRequest(r)
		info, err := infos.Get(ctx, ref)
		if err != nil {
			return nil, err
		}
		kubes, err := info.KubernetesConfig()
		if err != nil {
			return ContainerOperation{}, err
		}
		return fn(r.Context(), kubes, metadata)
	})
}

func OnCluster(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, ref store.ObjectReference) (any, error)) {
	OnOrOnTenantScope(w, r, func(ctx context.Context, scopes []store.Scope) (any, error) {
		cluster := api.Path(r, "cluster", "")
		if cluster == "" {
			return nil, errors.NewBadRequest("cluster name is required")
		}
		ref := store.ObjectReference{Name: cluster, Scopes: scopes}
		return fn(ctx, ref)
	})
}

func OnOrOnTenantScope(w http.ResponseWriter, r *http.Request, f func(ctx context.Context, scopes []store.Scope) (any, error)) {
	base.On(w, r, func(ctx context.Context) (any, error) {
		var scopes []store.Scope
		if tenant := api.Path(r, "tenant", ""); tenant != "" {
			scopes = append(scopes, base.ScopeTenant(tenant))
		}
		return f(ctx, scopes)
	})
}

func (a *API) clustersGroup(prefix string) api.Group {
	return api.
		NewGroup(prefix).
		SubGroup(
			api.NewGroup("/cluster-summaries").
				Route(
					api.GET("").
						Doc("List clusters summaries").
						To(a.ListClusterSummaries).
						Param(api.PageParams...).
						Response(store.List[Cluster]{}),
					api.GET("/{cluster}").
						Doc("Get cluster summaries").
						To(a.GetClusterSummary).
						Response(Cluster{}),
				),
			api.NewGroup("/clusters").
				Route(
					api.GET("").
						Doc("List clusters").
						To(a.ListClusters).Param(api.PageParams...).
						Response(store.List[Cluster]{}),
					api.POST("").
						Doc("Create cluster").
						To(a.CreateCluster).Param(api.BodyParam("cluster", Cluster{})).Response(Cluster{}),
					api.GET("/{cluster}").
						Doc("Get cluster").
						To(a.GetCluster).Response(Cluster{}),
					api.PUT("/{cluster}").
						Doc("Update cluster").
						To(a.UpdateCluster).Param(api.BodyParam("cluster", Cluster{})).Response(Cluster{}),
					api.DELETE("/{cluster}").
						Doc("Delete cluster").
						To(a.DeleteCluster),
				).
				SubGroup(
					api.NewGroup("/{cluster}").
						SubGroup(
							a.Resources.AllResourcesGroup(),
							a.Resources.ResourceMetadataOnlyGroup(),
							a.Resources.ProxyGroup(),
						),
				),
		)
}

func (a *API) Group() api.Group {
	return api.NewGroup("").
		Tag("Clusters").
		SubGroup(
			a.clustersGroup(""),
		)
}
