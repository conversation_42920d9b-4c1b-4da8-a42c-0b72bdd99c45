package cluster

import (
	"context"

	"xiaoshiai.cn/common/controller"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/base"
)

const (
	ConditionTypeConfigValid = "ConfigValid"
)

const (
	ClusterFinalizer = base.LabelClusterPrefix + "/finalizer"
)

func NewClusterController(ctx context.Context, storage store.Store, cloudinfo CloudInfoHolder) (*controller.Controller, error) {
	rec := &ClusterReconciler{
		Client: storage,
		Holder: cloudinfo,
	}
	better := controller.NewBetterReconciler(rec,
		storage,
		controller.WithFinalizer(ClusterFinalizer),
		controller.WithAutosetStatus())
	c := controller.
		NewController("clusters", better).
		Watch(controller.NewStoreSource(storage, &Cluster{}))
	return c, nil
}

type ClusterReconciler struct {
	Client store.Store
	Holder CloudInfoHolder
}

func (c *ClusterReconciler) Remove(ctx context.Context, key *Cluster) (controller.Result, error) {
	return c.remove(ctx, key)
}

func (c *ClusterReconciler) Sync(ctx context.Context, key *Cluster) (controller.Result, error) {
	return c.sync(ctx, key)
}

func (c *ClusterReconciler) sync(ctx context.Context, cluster *Cluster) (controller.Result, error) {
	if err := c.syncKubernetes(ctx, cluster); err != nil {
		cond := controller.Condition{
			Type:    ConditionTypeConfigValid,
			Status:  controller.ConditionFalse,
			Reason:  "FailedLoad",
			Message: err.Error(),
		}
		cluster.Status.Phase = ClusterPhaseUnknown
		controller.SetStatusCondition(&cluster.Status.Conditions, cond)
		return controller.Result{}, err
	}
	cluster.Status.Phase = ClusterPhaseHealthy
	cond := controller.Condition{
		Type:   ConditionTypeConfigValid,
		Status: controller.ConditionTrue,
	}
	controller.SetStatusCondition(&cluster.Status.Conditions, cond)
	return controller.Result{}, nil
}

func (c *ClusterReconciler) syncKubernetes(ctx context.Context, cluster *Cluster) error {
	log := log.FromContext(ctx)
	cloudinfo, err := c.Holder.Sync(ctx, cluster)
	if err != nil {
		log.Error(err, "failed to load kubeconfig")
		cond := controller.Condition{
			Type:    ConditionTypeConfigValid,
			Status:  controller.ConditionFalse,
			Reason:  "FailedLoad",
			Message: err.Error(),
		}
		cluster.Status.Phase = ClusterPhaseUnknown
		if controller.SetStatusCondition(&cluster.Status.Conditions, cond) {
			return c.Client.Status().Update(ctx, cluster)
		}
		return err
	}
	kubeclients, err := cloudinfo.KubernetesConfig()
	if err != nil {
		return err
	}
	info, err := kubeclients.Kubernetes.Discovery().ServerVersion()
	if err != nil {
		return err
	}
	cluster.Status.Version = VersionInfo{
		Name:       "kubernetes " + info.String(),
		Vendor:     "kubernetes",
		GitVersion: info.GitVersion,
		GitCommit:  info.GitCommit,
		BuildDate:  info.BuildDate,
	}
	return nil
}

func (c *ClusterReconciler) remove(ctx context.Context, cluster *Cluster) (controller.Result, error) {
	if err := c.removeKubernetes(ctx, cluster); err != nil {
		return controller.Result{}, err
	}
	if err := c.Holder.Remove(ctx, store.ObjectReferenceFrom(cluster)); err != nil {
		log.FromContext(ctx).Error(err, "failed to remove cluster info", "cluster", cluster.Name)
		return controller.Result{}, err
	}
	log.FromContext(ctx).Info("removed cluster info", "cluster", cluster.Name)
	return controller.Result{}, nil
}

func (c *ClusterReconciler) removeKubernetes(ctx context.Context, cluster *Cluster) error {
	_, _ = ctx, cluster
	// do nothing
	return nil
}
