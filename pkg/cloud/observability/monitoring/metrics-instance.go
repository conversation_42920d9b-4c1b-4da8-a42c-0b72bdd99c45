package monitoring

import (
	"context"
	"fmt"
	"net/http"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/base"
	"xiaoshiai.cn/rune/pkg/cloud/cluster/operation"
	"xiaoshiai.cn/rune/pkg/cloud/cluster/resources"
	"xiaoshiai.cn/rune/pkg/cloud/observability/monitoring/dashboard"
	"xiaoshiai.cn/rune/pkg/cloud/workspace"
)

func (a *API) GetApplicationMetrics(w http.ResponseWriter, r *http.Request) {
	base.OnInstance(w, r, func(ctx context.Context, tenant, workspacename, app string) (any, error) {
		kubes, namespace, err := workspace.GetWorkspaceCluster(ctx, a.CloudInfo, a.Store, tenant, workspacename)
		if err != nil {
			return nil, err
		}
		op := operation.NewContainerOperation(kubes)
		cond, params := appFilterParams(namespace, app)
		return GetApplicationMetrics(r, op, cond, params)
	})
}

func appFilterParams(namespace, name string) (string, map[string]string) {
	params := map[string]string{"namespace": namespace, "instance": name}
	cond := fmt.Sprintf(`namespace="%s",instance="%s"`, namespace, name)
	return cond, params
}

// GetApplicationResourceMetrics
func (a *API) GetApplicationResourceMetrics(w http.ResponseWriter, r *http.Request) {
	base.OnInstance(w, r, func(ctx context.Context, tenant, workspacename, app string) (any, error) {
		kubes, namespace, err := workspace.GetWorkspaceCluster(ctx, a.CloudInfo, a.Store, tenant, workspacename)
		if err != nil {
			return nil, err
		}
		op := operation.NewContainerOperation(kubes)

		meta := resources.ResourceMetaFromRequest(r)
		meta.Namespace = namespace

		return GetResourceMetrics(r, op, meta)
	})
}

func (a *API) ListPrometheusMonitorDashboards(w http.ResponseWriter, r *http.Request) {
	base.OnInstance(w, r, func(ctx context.Context, tenant, workspacename, app string) (any, error) {
		storage := a.Store.Scope(base.ScopeTenant(tenant), base.ScopeWorkspace(workspacename), base.ScopeInstance(app))
		return base.GenericList(r, storage, &store.List[MetricsDashboard]{}, store.WithSort("name"))
	})
}

func (a *API) CreatePrometheusMonitorDashboard(w http.ResponseWriter, r *http.Request) {
	base.OnInstance(w, r, func(ctx context.Context, tenant, workspacename, app string) (any, error) {
		storage := a.Store.Scope(base.ScopeTenant(tenant), base.ScopeWorkspace(workspacename), base.ScopeInstance(app))
		return base.GenericCreate(r, storage, &MetricsDashboard{})
	})
}

func (a *API) UpdatePrometheusMonitorDashboard(w http.ResponseWriter, r *http.Request) {
	a.onDashboard(w, r, func(ctx context.Context, tenant, workspacename, app, dashboard string) (any, error) {
		storage := a.Store.Scope(base.ScopeTenant(tenant), base.ScopeWorkspace(workspacename), base.ScopeInstance(app))
		return base.GenericUpdate(r, storage, &MetricsDashboard{}, dashboard)
	})
}

func (a *API) GetPrometheusMonitorDashboard(w http.ResponseWriter, r *http.Request) {
	a.onDashboard(w, r, func(ctx context.Context, tenant, workspacename, app, dashboard string) (any, error) {
		storage := a.Store.Scope(base.ScopeTenant(tenant), base.ScopeWorkspace(workspacename), base.ScopeInstance(app))
		return base.GenericGet(r, storage, &MetricsDashboard{}, dashboard)
	})
}

func (a *API) DeletePrometheusMonitorDashboard(w http.ResponseWriter, r *http.Request) {
	a.onDashboard(w, r, func(ctx context.Context, tenant, workspacename, app, dashboard string) (any, error) {
		storage := a.Store.Scope(base.ScopeTenant(tenant), base.ScopeWorkspace(workspacename), base.ScopeInstance(app))
		return base.GenericDelete(r, storage, &MetricsDashboard{}, dashboard)
	})
}

func (a *API) onDashboard(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, tenant, workspacename, app, dashboard string) (any, error)) {
	base.OnInstance(w, r, func(ctx context.Context, tenant, workspacename, app string) (any, error) {
		dashboard := api.Path(r, "dashboard", "")
		if dashboard == "" {
			return nil, errors.NewBadRequest("monitor dashboard name is required")
		}
		return fn(ctx, tenant, workspacename, app, dashboard)
	})
}

func (a *API) QueryPrometheusMonitorDashboard(w http.ResponseWriter, r *http.Request) {
	a.onInstanceDashboard(w, r, func(ctx context.Context, tenant, workspacename, instance, dashboardname string, op operation.ContainerOperation, namespace string) (any, error) {
		storage := a.Store.Scope(base.ScopeTenant(tenant), base.ScopeWorkspace(workspacename), base.ScopeInstance(instance))
		config, err := a.getInstanceDashboard(ctx, storage, dashboardname)
		if err != nil {
			return nil, err
		}
		options := a.getInstanceRenderDashboardOptions(r, namespace, instance)
		return dashboard.RenderDashboardSimple(r, op, config, options)
	})
}

func (a *API) ListInstanceDashboardParams(w http.ResponseWriter, r *http.Request) {
	a.onInstanceDashboard(w, r, func(ctx context.Context, tenant, workspacename, app, dashboardname string, op operation.ContainerOperation, namespace string) (any, error) {
		storage := a.Store.Scope(base.ScopeTenant(tenant), base.ScopeWorkspace(workspacename), base.ScopeInstance(app))
		config, err := a.getInstanceDashboard(ctx, storage, dashboardname)
		if err != nil {
			return nil, err
		}
		options := a.getInstanceRenderDashboardOptions(r, namespace, app)
		return dashboard.ListDashboardParams(ctx, op, config, options)
	})
}

func (a *API) getInstanceDashboard(ctx context.Context, storage store.Store, dashboardname string) (dashboard.DashboardConfiguration, error) {
	// use built-in dashboards first
	fullname := dashboard.GetBuiltApplicationDashboardName(dashboardname)
	config, ok := a.BuiltInDashboards[fullname]
	if !ok {
		// built in dashboards are prefixed to avoid conflict with other dashboards
		record := &MetricsDashboard{}
		if err := storage.Get(ctx, dashboardname, record); err != nil {
			return dashboard.DashboardConfiguration{}, err
		}
		config = record.Dashboard
	}
	return config, nil
}

func (a *API) getInstanceRenderDashboardOptions(r *http.Request, namespace, instance string) dashboard.RenderDashboardOptions {
	params := map[string]string{
		"instance":  instance,
		"namespace": namespace,
		// some dashboard use service or job as "instance"
		"job":     instance,
		"service": instance,
		// ingress
		"controller_namespace": namespace,
		"controller_class":     "gateway.xiaoshiai.cn/" + instance,
	}
	return dashboard.GetRenderDashboardOptions(r, params)
}

func (a *API) QueryInstanceResourceMetricsDashboard(w http.ResponseWriter, r *http.Request) {
	a.onInstanceDashboard(w, r, func(ctx context.Context, tenant, workspacename, app, dashboardname string, op operation.ContainerOperation, namespace string) (any, error) {
		meta := resources.ResourceMetaFromRequest(r)
		meta.Namespace = namespace

		storage := a.Store.Scope(base.ScopeTenant(tenant), base.ScopeWorkspace(workspacename), base.ScopeInstance(app))

		config, err := a.getClusterDashboard(ctx, storage, meta.Resource, dashboardname)
		if err != nil {
			return nil, err
		}
		options := getClusterRenderDashboardOptions(r, meta)
		return dashboard.RenderDashboardSimple(r, op, config, options)
	})
}

func (a *API) ListInstanceResourceDashboardParams(w http.ResponseWriter, r *http.Request) {
	a.onInstanceDashboard(w, r, func(ctx context.Context, tenant, workspacename, app, dashboardname string, op operation.ContainerOperation, namespace string) (any, error) {
		meta := resources.ResourceMetaFromRequest(r)
		meta.Namespace = namespace

		storage := a.Store.Scope(base.ScopeTenant(tenant), base.ScopeWorkspace(workspacename), base.ScopeInstance(app))

		config, err := a.getInstanceDashboard(ctx, storage, dashboardname)
		if err != nil {
			return nil, err
		}
		options := getClusterRenderDashboardOptions(r, meta)
		return dashboard.ListDashboardParams(ctx, op, config, options)
	})
}

func (a *API) onInstanceDashboard(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, tenant, workspace, app, dashboard string, op operation.ContainerOperation, namespace string) (any, error)) {
	base.OnInstance(w, r, func(ctx context.Context, tenant, workspacename, app string) (any, error) {
		dashboard := api.Path(r, "dashboard", "")
		if dashboard == "" {
			return nil, errors.NewBadRequest("monitor dashboard name is required")
		}
		kubes, namespace, err := workspace.GetWorkspaceCluster(ctx, a.CloudInfo, a.Store, tenant, workspacename)
		if err != nil {
			return nil, err
		}
		op := operation.NewContainerOperation(kubes)

		return fn(ctx, tenant, workspacename, app, dashboard, op, namespace)
	})
}

var MetricsDashboardParams = []api.Param{
	api.PathParam("dashboard", "monitor dashboard name").Def("basic"),
	api.QueryParam("from", "start time").Format("datatime").Optional(),
	api.QueryParam("to", "end time").Format("datatime").Optional(),
	api.QueryParam("step", "step duration").Format("duration").Optional(),
	api.QueryParam("params", "query parameters,params='pod=instance-abc-def,foo=bar'").Optional(),
}

func (a *API) InstanceGroup() api.Group {
	return base.
		NewInstanceGroup().
		SubGroup(
			api.NewGroup("/metrics").
				Route(
					api.GET("/{metrics}").
						Doc("Get instance metrics").
						Param(GetMetricsParams...).
						Param(MetricsParams...).
						To(a.GetApplicationMetrics),
				),
			api.NewGroup("/metrics-dashboards").
				Route(
					api.GET("").Operation("list monitor dashboards").
						To(a.ListPrometheusMonitorDashboards).
						Param(api.PageParams...).
						Response(store.List[MetricsDashboard]{}),

					api.POST("").Operation("import monitor dashboard").
						To(a.CreatePrometheusMonitorDashboard).
						Param(api.BodyParam("dashboard", MetricsDashboard{})).
						Response(MetricsDashboard{}),

					api.GET("/{dashboard}").Operation("get monitor dashboard").
						To(a.GetPrometheusMonitorDashboard).
						Response(MetricsDashboard{}),

					api.PUT("/{dashboard}").Operation("update monitor dashboard").
						To(a.UpdatePrometheusMonitorDashboard).
						Param(api.BodyParam("dashboard", MetricsDashboard{})).
						Response(MetricsDashboard{}),

					api.DELETE("/{dashboard}").Operation("delete monitor dashboard").
						To(a.DeletePrometheusMonitorDashboard),
				),
			api.NewGroup("/metrics-dashboards").
				Route(
					api.GET("/{dashboard}/query").Operation("query monitor dashboard").
						To(a.QueryPrometheusMonitorDashboard).
						Param(MetricsDashboardParams...).
						Response(QueryResponse{}),

					api.GET("/{dashboard}/params").Operation("list available params").
						To(a.ListInstanceDashboardParams).
						Response([]dashboard.DashboardParam{}),
				),

			api.NewGroup("/apis/{group}/{version}/{resource}/{name}").
				Route(
					api.GET("/metrics/{metrics}").
						Doc("Get tenant application metrics").
						Param(GetMetricsParams...).
						Param(MetricsParams...).
						To(a.GetApplicationResourceMetrics),
					api.GET("/metrics-dashboards/{dashboard}/query").
						Doc("Get tenant application metrics").
						Param(MetricsDashboardParams...).
						To(a.QueryInstanceResourceMetricsDashboard),
					api.GET("/metrics-dashboards/{dashboard}/params").
						Operation("list available params").
						To(a.ListInstanceResourceDashboardParams).
						Response([]dashboard.DashboardParam{}),
				),
		)
}
