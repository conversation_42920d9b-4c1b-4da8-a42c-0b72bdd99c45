package dashboard

import (
	"context"
	"fmt"
	"maps"
	"net/http"
	"net/url"
	"regexp"
	"slices"
	"strconv"
	"strings"
	"time"

	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/rune/pkg/cloud/cluster/operation"
	"xiaoshiai.cn/rune/pkg/cloud/observability/monitoring/prometheus"
)

func ParseIntervalStringToTimeDuration(interval string) (time.Duration, error) {
	formattedInterval := strings.Replace(strings.Replace(interval, "<", "", 1), ">", "", 1)
	isPureNum, err := regexp.MatchString(`^\d+$`, formattedInterval)
	if err != nil {
		return time.Duration(0), err
	}
	if isPureNum {
		formattedInterval += "s"
	}
	parsedInterval, err := ParseDuration(formattedInterval)
	if err != nil {
		return time.Duration(0), err
	}
	return parsedInterval, nil
}

func ParseDuration(inp string) (time.Duration, error) {
	dur, period, err := parse(inp)
	if err != nil {
		return 0, err
	}
	if period == "" {
		return dur, nil
	}
	const daysInAYear = 365.25
	const day = 24 * time.Hour
	const week = 7 * day
	const year = time.Duration(float64(day) * daysInAYear)
	const month = time.Duration(float64(year) / 12)
	switch period {
	case "d":
		return dur * day, nil
	case "w":
		return dur * week, nil
	case "M":
		return dur * month, nil
	case "y":
		return dur * year, nil
	}
	return 0, fmt.Errorf("invalid duration %q", inp)
}

var dateUnitPattern = regexp.MustCompile(`^(\d+)([dwMy])$`)

func parse(inp string) (time.Duration, string, error) {
	result := dateUnitPattern.FindSubmatch([]byte(inp))
	if len(result) != 3 {
		dur, err := time.ParseDuration(inp)
		return dur, "", err
	}
	num, err := strconv.Atoi(string(result[1]))
	if err != nil {
		return 0, "", err
	}
	return time.Duration(num), string(result[2]), nil
}

// FormatInterval converts a duration into the units that Grafana uses
func FormatInterval(inter time.Duration) string {
	year := time.Hour * 24 * 365
	day := time.Hour * 24
	if inter >= year {
		return fmt.Sprintf("%dy", inter/year)
	}
	if inter >= day {
		return fmt.Sprintf("%dd", inter/day)
	}
	if inter >= time.Hour {
		return fmt.Sprintf("%dh", inter/time.Hour)
	}
	if inter >= time.Minute {
		return fmt.Sprintf("%dm", inter/time.Minute)
	}
	if inter >= time.Second {
		return fmt.Sprintf("%ds", inter/time.Second)
	}
	if inter >= time.Millisecond {
		return fmt.Sprintf("%dms", inter/time.Millisecond)
	}
	return "1ms"
}

type SimpleQueryResponse struct {
	ShowName string `json:"showName"`
	Unit     string `json:"unit"`
	Values   any    `json:"values"`
	Error    string `json:"error,omitempty"`
}

func RenderDashboard(r *http.Request, op operation.ContainerOperation, config DashboardConfiguration, options RenderDashboardOptions) (*RenderedDashboard, error) {
	render := NewDashboardRender(
		// prometheus
		DefaultPrometheusDatasource{
			Prometheus: prometheus.ContainerOperationPrometheus{Operation: op},
		},
	)
	return render.Render(r.Context(), config, options)
}

func GetRenderDashboardOptions(r *http.Request, params map[string]string) RenderDashboardOptions {
	queryparams, _ := url.ParseQuery(api.Query(r, "params", ""))
	kvs := map[string]string{}
	for k, v := range queryparams {
		kvs[k] = v[0]
	}
	maps.Copy(kvs, params)
	return RenderDashboardOptions{
		From:       GetQueryTime(r, "from", "start"),
		To:         GetQueryTime(r, "to", "end"),
		Step:       api.Query(r, "step", time.Duration(0)),
		Parameters: kvs,
	}
}

func GetQueryTime(r *http.Request, keys ...string) time.Time {
	queries := r.URL.Query()
	for _, key := range keys {
		val := queries.Get(key)
		if len(val) == 0 {
			continue
		}
		t, _ := time.Parse(time.RFC3339, val)
		if !t.IsZero() {
			return t
		}
	}
	return time.Time{}
}

func RenderDashboardSimple(r *http.Request, op operation.ContainerOperation, config DashboardConfiguration, options RenderDashboardOptions) ([]SimpleQueryResponse, error) {
	result, err := RenderDashboard(r, op, config, options)
	if err != nil {
		return nil, err
	}
	// converttosimpleresponse
	simplelist := make([]SimpleQueryResponse, 0, len(result.Panels))
	for _, panel := range result.Panels {
		item := SimpleQueryResponse{
			ShowName: panel.Title,
			Unit:     panel.Unit,
			Error:    panel.Error,
			Values:   mergePanelValues(panel.Values),
		}
		simplelist = append(simplelist, item)
	}
	return simplelist, nil
}

func mergePanelValues(values []any) any {
	if len(values) == 0 {
		return nil
	}
	results := make([]any, 0, len(values))
	for _, val := range values {
		if vmap, ok := val.(map[string]any); ok {
			result := vmap["result"]
			if resultslice, ok := result.([]any); ok {
				results = append(results, resultslice...)
			} else {
				results = append(results, result)
			}
		}
	}
	// sort by title
	slices.SortFunc(results, func(a, b any) int {
		return strings.Compare(getstringfrommap(a, "title"), getstringfrommap(b, "title"))
	})
	return results
}

func getstringfrommap(m any, key string) string {
	if m == nil {
		return ""
	}
	mmap, ok := m.(map[string]any)
	if !ok {
		return ""
	}
	val, ok := mmap[key]
	if !ok {
		return ""
	}
	strval, ok := val.(string)
	if !ok {
		return ""
	}
	return strval
}

func ListDashboardParams(ctx context.Context, op operation.ContainerOperation, config DashboardConfiguration, options RenderDashboardOptions) ([]DashboardParam, error) {
	render := NewDashboardRender(
		// prometheus
		DefaultPrometheusDatasource{
			Prometheus: prometheus.ContainerOperationPrometheus{Operation: op},
		},
	)
	return render.ListParameters(ctx, config, options)
}
