package dashboard

import (
	"embed"
)

//go:embed dashboards/*
var BuiltInDashboardsFS embed.FS

func NewBuiltInDashboards() (map[string]DashboardConfiguration, error) {
	return LoadDashboards(BuiltInDashboardsFS)
}

func GetBuiltClusterDashboardName(resource string, dashboard string) string {
	prefix := "cluster-"
	if resource != "" {
		prefix += "resource-" + resource + "-"
	}
	return prefix + dashboard
}

func GetBuiltApplicationDashboardName(dashboard string) string {
	return "app-" + dashboard
}
