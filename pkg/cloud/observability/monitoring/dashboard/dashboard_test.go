package dashboard

import (
	"os"
	"reflect"
	"testing"
	"time"
)

func TestLoadDashboardConfiguration(t *testing.T) {
	tests := []struct {
		file    string
		wantErr bool
	}{
		{
			file: "../../testdata/dashboards/basic.json",
		},
	}
	for _, tt := range tests {
		t.Run(tt.file, func(t *testing.T) {
			data, err := os.ReadFile(tt.file)
			if err != nil {
				t.<PERSON><PERSON>rf("LoadDashboardConfiguration() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			got, err := LoadDashboardConfiguration(data)
			if (err != nil) != tt.wantErr {
				t.Errorf("LoadDashboardConfiguration() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got == nil {
				t.<PERSON>rrorf("LoadDashboardConfiguration() = %v, want %v", got, tt.wantErr)
			}
		})
	}
}

func TestParseTimeExpr(t *testing.T) {
	now := time.Now()
	tests := []struct {
		expr    string
		want    time.Time
		wantErr bool
	}{
		{
			expr: "2021-01-01T00:00:00Z",
			want: time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC),
		},
		{
			expr: "now-1d",
			want: now.Add(-1 * 24 * time.Hour),
		},
		{
			expr: "now+5h",
			want: now.Add(5 * time.Hour),
		},
		{
			expr: "now",
			want: now,
		},
		{
			expr: "now-",
			want: now,
		},
	}
	for _, tt := range tests {
		t.Run(tt.expr, func(t *testing.T) {
			got, err := ParseTimeExprWithNow(now, tt.expr)
			if (err != nil) != tt.wantErr {
				t.Errorf("ParseTimeExpr() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ParseTimeExpr() = %v, want %v", got, tt.want)
			}
		})
	}
}
