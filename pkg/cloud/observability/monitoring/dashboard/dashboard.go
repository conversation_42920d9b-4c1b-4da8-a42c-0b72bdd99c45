package dashboard

import (
	"context"
	"fmt"
	"maps"
	"regexp"
	"strings"
	"time"

	"sigs.k8s.io/yaml"
	"xiaoshiai.cn/rune/pkg/cloud/observability/monitoring/prometheus"
)

func LoadDashboardConfiguration(data []byte) (*DashboardConfiguration, error) {
	dashboard := &DashboardConfiguration{}
	if err := yaml.Unmarshal(data, dashboard); err != nil {
		return nil, err
	}
	dashboard = DefaultDashboradConfiguration(dashboard)
	return dashboard, nil
}

func DefaultDashboradConfiguration(dashboard *DashboardConfiguration) *DashboardConfiguration {
	if len(dashboard.Inputs) == 0 {
		dashboard.Inputs = []DashboardInput{
			{
				Name:       "DS_PROMETHEUS",
				Type:       "datasource",
				PluginId:   "prometheus",
				PluginName: "Prometheus",
				Label:      "Prometheus",
			},
		}
	}
	if dashboard.Time.From == "" && dashboard.Time.To == "" {
		dashboard.Time.From = "now-1h"
		dashboard.Time.To = "now"
	}
	for _, param := range dashboard.Templating.List {
		if param.Type == "" {
			param.Type = "custom"
		}
	}
	for i, panel := range dashboard.Panels {
		if panel.Type == "" {
			dashboard.Panels[i].Type = "timeseries"
		}
		if panel.Datasource.Type == "" {
			dashboard.Panels[i].Datasource = DashboardDataSource{Type: "prometheus", UID: "${DS_PROMETHEUS}"}
		}
	}
	return dashboard
}

func NewDashboardRender(datasources ...Datasource) *DashboardRender {
	return &DashboardRender{datasources: datasources}
}

type DashboardRender struct {
	datasources []Datasource
}

type RenderDashboardOptions struct {
	From time.Time     `json:"from,omitempty"`
	To   time.Time     `json:"to,omitempty"`
	Step time.Duration `json:"step,omitempty"`
	// Parameters are used to replace variables in the expression.
	Parameters map[string]string `json:"parameters,omitempty"`
}

type RenderedDashboard struct {
	Title  string          `json:"title,omitempty"`
	Panels []RenderedPanel `json:"panels,omitempty"`
}

type RenderedPanel struct {
	Title  string             `json:"title,omitempty"`
	Unit   string             `json:"unit,omitempty"`
	Min    int                `json:"min,omitempty"`
	Max    int                `json:"max,omitempty"`
	Type   DashboardPanelType `json:"type,omitempty"`
	Values []any              `json:"values,omitempty"`
	Error  string             `json:"error,omitempty"`
}

func (r *DashboardRender) Render(ctx context.Context, dashboard DashboardConfiguration, options RenderDashboardOptions) (*RenderedDashboard, error) {
	if options.From.IsZero() || options.To.IsZero() {
		defaultfrom, err := ParseTimeExpr(dashboard.Time.From)
		if err != nil {
			return nil, fmt.Errorf("parse from: %v", err)
		}
		defaultto, err := ParseTimeExpr(dashboard.Time.To)
		if err != nil {
			return nil, fmt.Errorf("parse to: %v", err)
		}
		options.From, options.To = defaultfrom, defaultto
	}
	if options.Step == 0 {
		options.Step = 1 * time.Minute
	}

	params := buildParams(dashboard, options)

	pannels := make([]RenderedPanel, 0, len(dashboard.Panels))
	for _, panel := range dashboard.Panels {
		var values []any
		var errors []error
		for _, target := range panel.Targets {
			ds, err := r.getPrometheusDatasource(target.Datasource, panel.Datasource)
			if err != nil {
				errors = append(errors, err)
				continue
			}
			expr := templExpr(target.Expr, params)
			if target.Range {
				value, err := ds.QueryRange(ctx, expr, options.From, options.To, options.Step)
				if err != nil {
					errors = append(errors, fmt.Errorf("range query: %v", err))
					continue
				}
				values = append(values, injectMetricsTitle(value, target.LegendFormat))
			} else {
				value, err := ds.Query(ctx, expr, options.To)
				if err != nil {
					errors = append(errors, fmt.Errorf("query: %v", err))
					continue
				}
				values = append(values, injectMetricsTitle(value, target.LegendFormat))
			}
		}
		panel := RenderedPanel{
			Title:  panel.Title,
			Unit:   string(panel.FieldConfig.Defaults.Unit),
			Type:   panel.Type,
			Min:    panel.FieldConfig.Defaults.Min,
			Max:    panel.FieldConfig.Defaults.Max,
			Values: values,
		}
		if len(errors) > 0 {
			var msgs []string
			for _, err := range errors {
				msgs = append(msgs, err.Error())
			}
			panel.Error = strings.Join(msgs, ", ")
		}
		pannels = append(pannels, panel)
	}
	ret := &RenderedDashboard{
		Title:  dashboard.Title,
		Panels: pannels,
	}
	return ret, nil
}

type DashboardParam struct {
	Name        string                `json:"name"`
	Label       string                `json:"label"` // show name
	Description string                `json:"description"`
	Multi       bool                  `json:"multi"`
	Items       []DashboardParamValue `json:"items"`
}

type DashboardParamValue struct {
	Name        string `json:"name"`
	Selected    bool   `json:"selected"`
	Value       string `json:"value"`
	Description string `json:"description"`
}

func (r *DashboardRender) ListParameters(ctx context.Context, dashboard DashboardConfiguration, options RenderDashboardOptions) ([]DashboardParam, error) {
	ret := make([]DashboardParam, 0, len(dashboard.Templating.List))

	params := buildParams(dashboard, options)

	for _, param := range dashboard.Templating.List {
		dashboardparam := DashboardParam{
			Name:        param.Name,
			Description: param.Description,
			Label:       param.Label,
			Items:       []DashboardParamValue{},
		}
		if dashboardparam.Label == "" {
			dashboardparam.Label = param.Name
		}
		switch param.Type {
		case "query":
			definition := param.Definition
			if definition == "" {
				continue
			}
			prometheus, err := r.getPrometheusDatasource(param.Datasource)
			if err != nil {
				return nil, err
			}
			values, err := ExecutePrometheusExpr(ctx, definition, prometheus, params)
			if err != nil {
				return nil, fmt.Errorf("execute query: %v", err)
			}
			// parse expr
			var matchRegexp *regexp.Regexp
			if newregex, err := regexp.Compile(param.Regex); err != nil {
				return nil, err
			} else {
				matchRegexp = newregex
			}
			for i, value := range values {
				if matchRegexp != nil && !matchRegexp.MatchString(value) {
					continue
				}
				dashboardparam.Items = append(dashboardparam.Items, DashboardParamValue{
					Name:     value,
					Value:    value,
					Selected: i == 0,
				})
			}
		case "custom":
			for _, item := range param.Options {
				dashboardparam.Items = append(dashboardparam.Items, DashboardParamValue{
					Name:     item.Text,
					Value:    item.Value,
					Selected: item.Selected,
				})
			}
		default:
			// skip unrecognized type
			continue
		}
		ret = append(ret, dashboardparam)
	}
	return ret, nil
}

func QueryPrometheusExpr(ctx context.Context, prometheus PrometheusDatasource, expr string, options RenderDashboardOptions) ([]string, error) {
	return nil, nil
}

func injectMetricsTitle(metrics *prometheus.ResponseData, legendFormat string) any {
	anyval, err := metrics.Any()
	if err != nil {
		return metrics
	}
	result, ok := anyval["result"]
	if !ok {
		return metrics
	}
	switch result := result.(type) {
	case []any:
		for i := range result {
			val, ok := result[i].(map[string]any)
			if !ok {
				continue
			}
			metrics, ok := val["metric"].(map[string]any)
			if !ok {
				continue
			}
			if title := templateLegend(legendFormat, metrics); title != "" {
				val["title"] = title
			}
		}
	case map[string]any:
		if title := templateLegend(legendFormat, result); title != "" {
			result["title"] = title
		}
	}
	return anyval
}

func templateLegend(legend string, params map[string]any) string {
	for k, v := range params {
		strval, ok := v.(string)
		if !ok {
			continue
		}
		legend = strings.ReplaceAll(legend, fmt.Sprintf("{{ %s }}", k), strval)
	}
	return legend
}

func (r *DashboardRender) getPrometheusDatasource(targets ...DashboardDataSource) (PrometheusDatasource, error) {
	ds, err := r.getDatasource(targets...)
	if err != nil {
		return nil, err
	}
	prometheus, ok := ds.(PrometheusDatasource)
	if !ok {
		return nil, fmt.Errorf("datasource %s is not prometheus", ds.Type())
	}
	return prometheus, nil
}

func (r *DashboardRender) getDatasource(candidates ...DashboardDataSource) (Datasource, error) {
	for _, candidate := range candidates {
		if candidate.Type == "" {
			continue
		}
		for _, ds := range r.datasources {
			if ds.Type() == candidate.Type {
				return ds, nil
			}
		}
	}
	return nil, fmt.Errorf("datasource not found")
}

func buildParams(dashboard DashboardConfiguration, options RenderDashboardOptions) map[string]string {
	rangeduration := options.To.Sub(options.From)
	timezone := dashboard.Timezone
	if timezone == "" {
		timezone = "utc"
	}
	builtins := map[string]string{
		"__dashboard": dashboard.Title,

		"__from": options.From.Format(time.RFC3339),
		"__to":   options.To.Format(time.RFC3339),

		"__range":    rangeduration.String(),
		"__range_ms": fmt.Sprintf("%d", rangeduration.Milliseconds()),
		"__range_s":  fmt.Sprintf("%d", int64(rangeduration.Seconds())),

		"__rate_interval":    fmt.Sprintf("%s", options.Step),
		"__rate_interval_ms": fmt.Sprintf("%d", options.Step.Milliseconds()),

		"__timezone": timezone,
	}

	for _, v := range dashboard.Templating.List {
		builtins[v.Current.Text.String()] = v.Current.Value.String()
	}

	maps.Copy(builtins, options.Parameters)
	return builtins
}

func templExpr(expr string, params map[string]string) string {
	for k, v := range params {
		expr = strings.ReplaceAll(expr, fmt.Sprintf("$%s", k), v)
		expr = strings.ReplaceAll(expr, fmt.Sprintf("${%s}", k), v)
	}
	return expr
}

// ParseTimeExpr parses a time expression.
// eg "now-1h" "now+1d5h" or "2021-01-01T00:00:00Z"
func ParseTimeExpr(expr string) (time.Time, error) {
	return ParseTimeExprWithNow(time.Now(), expr)
}

func ParseTimeExprWithNow(now time.Time, expr string) (time.Time, error) {
	if strings.HasPrefix(expr, "now") {
		return parseNowExpr(now, expr)
	}
	return time.Parse(time.RFC3339, expr)
}

var timeExprRegexp = regexp.MustCompile(`^now([+-]\d+[smhdwMy])?$`)

func parseNowExpr(now time.Time, expr string) (time.Time, error) {
	var fn, op, durexp string
	i := strings.IndexFunc(expr, func(r rune) bool {
		return r == '+' || r == '-'
	})
	if i == -1 {
		fn, op, durexp = expr, "", ""
	} else if i == len(expr)-1 {
		fn, op, durexp = expr[:i], expr[i:i+1], ""
	} else {
		fn, op, durexp = expr[:i], expr[i:i+1], expr[i+1:]
	}
	switch fn {
	case "now":
	default:
		return time.Time{}, fmt.Errorf("invalid time expression: %s", expr)
	}
	if len(durexp) == 0 {
		return now, nil
	}
	dur, err := ParseDuration(durexp)
	if err != nil {
		return time.Time{}, err
	}
	switch op {
	case "+":
		return now.Add(dur), nil
	case "-":
		return now.Add(-dur), nil
	default:
		return time.Time{}, fmt.Errorf("invalid time expression: %s", expr)
	}
}
