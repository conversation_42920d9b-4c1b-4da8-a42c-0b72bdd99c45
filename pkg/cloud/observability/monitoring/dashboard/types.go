package dashboard

import (
	"encoding/json"
	"strings"
	"time"
)

// DashboardConfiguration is the configuration of a dashboard.
// it use grafana dashboard json format
type DashboardConfiguration struct {
	Inputs               []DashboardInput    `json:"__inputs,omitempty"`
	Elements             any                 `json:"__elements,omitempty"`
	Requires             any                 `json:"__requires,omitempty"`
	Annotaions           DashboardAnnotaions `json:"annotations,omitempty"`
	Editable             bool                `json:"editable,omitempty"`
	FiscalYearStartMonth int                 `json:"fiscalYearStartMonth,omitempty"`
	GnetId               int                 `json:"gnetId,omitempty"`
	GraphTooltip         int                 `json:"graphTooltip,omitempty"`
	ID                   any                 `json:"id,omitempty"`
	Links                []DashboardLink     `json:"links,omitempty"`
	LiveNow              bool                `json:"liveNow,omitempty"`
	Panels               []DashboardPanel    `json:"panels,omitempty"`
	Refresh              DashboardDuration   `json:"refresh,omitempty"`
	Revision             int                 `json:"revision,omitempty"`
	SchemaVersion        int                 `json:"schemaVersion,omitempty"`
	Style                string              `json:"style,omitempty"`
	Tags                 []string            `json:"tags,omitempty"`
	Templating           DashboardTemplating `json:"templating,omitempty"`
	Time                 DashboardTime       `json:"time,omitempty"`
	Timepicker           DashboardTimepicker `json:"timepicker,omitempty"`
	Timezone             string              `json:"timezone,omitempty"`
	Title                string              `json:"title,omitempty"`
	Version              int                 `json:"version,omitempty"`
	UID                  string              `json:"uid,omitempty"`
	WeekStart            string              `json:"weekStart,omitempty"`
}

type DashboardTemplating struct {
	List []DashboardTemplatingItem `json:"list,omitempty"`
}

type DashboardTemplatingItem struct {
	AllValue    string                          `json:"allValue,omitempty"`
	Current     DashboardTemplatingItemCurrent  `json:"current,omitempty"`
	Hide        int                             `json:"hide,omitempty"`
	IncludeAll  bool                            `json:"includeAll,omitempty"`
	Label       string                          `json:"label,omitempty"`
	Multi       bool                            `json:"multi,omitempty"`
	Name        string                          `json:"name,omitempty"`
	Description string                          `json:"description,omitempty"`
	Options     []DashboardTemplatingItemOption `json:"options,omitempty"`
	Query       DashboardTemplatingQuery        `json:"query,omitempty"`
	QueryValue  string                          `json:"queryValue,omitempty"`
	Definition  string                          `json:"definition,omitempty"`
	Refresh     int                             `json:"refresh,omitempty"`
	Regex       string                          `json:"regex,omitempty"`
	SkipUrlSync bool                            `json:"skipUrlSync,omitempty"`
	Type        string                          `json:"type,omitempty"`
	Datasource  DashboardDataSource             `json:"datasource,omitempty"`
	Sort        int                             `json:"sort,omitempty"`
}

type DashboardTemplatingItemOption struct {
	Text     string `json:"text,omitempty"`
	Value    string `json:"value,omitempty"`
	Selected bool   `json:"selected,omitempty"`
}

type DashboardTemplatingQuery struct {
	Query string `json:"query,omitempty"`
	RefId string `json:"refId,omitempty"`
}

func (q *DashboardTemplatingQuery) UnmarshalJSON(data []byte) error {
	var query string
	if err := json.Unmarshal(data, &query); err == nil {
		q.Query = query
		return nil
	}
	type Alias DashboardTemplatingQuery
	aux := &struct {
		*Alias
	}{
		Alias: (*Alias)(q),
	}
	if err := json.Unmarshal(data, &aux); err != nil {
		return err
	}
	return nil
}

type DashboardTemplatingItemCurrent struct {
	Selected bool                `json:"selected,omitempty"`
	Text     StringOrStringArray `json:"text,omitempty"`
	Value    StringOrStringArray `json:"value,omitempty"`
}

type DashboardTimepicker struct {
	RefreshIntervals []DashboardDuration `json:"refresh_intervals,omitempty"`
	TimeOptions      []DashboardDuration `json:"time_options,omitempty"`
}

type DashboardDuration time.Duration

func (d *DashboardDuration) UnmarshalJSON(data []byte) error {
	if string(data) == "null" || string(data) == "\"\"" || string(data) == "false" {
		return nil
	}
	var duration string
	if err := json.Unmarshal(data, &duration); err != nil {
		return err
	}
	parsedDuration, err := ParseIntervalStringToTimeDuration(duration)
	if err != nil {
		return err
	}
	*d = DashboardDuration(parsedDuration)
	return nil
}

func (d DashboardDuration) String() string {
	return FormatInterval(time.Duration(d))
}

type DashboardTime struct {
	From string `json:"from,omitempty"`
	To   string `json:"to,omitempty"`
}

type DashboardLink struct {
	Icon        string `json:"icon,omitempty"`
	Tags        []any  `json:"tags,omitempty"`
	Title       string `json:"title,omitempty"`
	TargetBlank bool   `json:"targetBlank,omitempty"`
	Type        string `json:"type,omitempty"`
	URL         string `json:"url,omitempty"`
}

type DashboardPanel struct {
	Collapsed   bool                      `json:"collapsed,omitempty"`
	Datasource  DashboardDataSource       `json:"datasource,omitempty"`
	Description string                    `json:"description,omitempty"`
	FieldConfig DashboardPanelFieldConfig `json:"fieldConfig,omitempty"`
	GridPos     GridPosition              `json:"gridPos,omitempty"`
	ID          int                       `json:"id,omitempty"`
	Panels      []DashboardPanel          `json:"panels,omitempty"`
	Targets     []DashboardPanelTarget    `json:"targets,omitempty"`
	Title       string                    `json:"title,omitempty"`
	Type        DashboardPanelType        `json:"type,omitempty"`
	Options     any                       `json:"options,omitempty"`
}

type DashboardPanelFieldConfig struct {
	Defaults  DashboardPanelFieldConfigDefaults `json:"defaults,omitempty"`
	Overrides []any                             `json:"overrides,omitempty"`
}

type DashboardPanelFieldConfigDefaults struct {
	Color      DashboardPanelFieldConfigDefaultsColor `json:"color,omitempty"`
	Decimals   int                                    `json:"decimals,omitempty"`
	Links      []DashboardLink                        `json:"links,omitempty"`
	Mappings   []any                                  `json:"mappings,omitempty"`
	Max        int                                    `json:"max,omitempty"`
	Min        int                                    `json:"min,omitempty"`
	Thresholds DashboardPanelFieldConfigThresholds    `json:"thresholds,omitempty"`
	Unit       DashboardPanelUnit                     `json:"unit,omitempty"`
	Custom     any                                    `json:"custom,omitempty"`
}

type DashboardPanelUnit string

const (
	DashboardPanelUnitCores       DashboardPanelUnit = "cores"       // cpu cores
	DashboardPanelUnitPercent     DashboardPanelUnit = "percent"     // 0-100
	DashboardPanelUnitPercentunit DashboardPanelUnit = "percentunit" // 0-1.0
	DashboardPanelUnitDecbytes    DashboardPanelUnit = "decbytes"    // bytes(SI) 1000
	DashboardPanelUnitBytes       DashboardPanelUnit = "bytes"       // bytes(IEC) 1024
	DashboardPanelUnitBps         DashboardPanelUnit = "bps"         // bytes per second
	DashboardPanelUnitPps         DashboardPanelUnit = "pps"         // packets per second
	DashboardPanelUnitIops        DashboardPanelUnit = "iops"        // iops
	DashboardPanelUnitShort       DashboardPanelUnit = "short"       // short
	DashboardPanelUnitHertz       DashboardPanelUnit = "hertz"       // Hz
	DashboardPanelUnitCelsius     DashboardPanelUnit = "celsius"     // .c
	DashboardPanelUnitBool        DashboardPanelUnit = "bool"        // bool
	DashboardPanelUnitBoolYesNo   DashboardPanelUnit = "bool_yes_no" // bool yes no
	DashboardPanelUnitBoolOnOff   DashboardPanelUnit = "bool_on_off" // bool on off
)

type DashboardPanelFieldConfigThresholds struct {
	Mode  string               `json:"mode,omitempty"`
	Steps []DashboardPanelStep `json:"steps,omitempty"`
}

type DashboardPanelStep struct {
	Color string `json:"color,omitempty"`
	Value any    `json:"value,omitempty"`
}

type DashboardPanelFieldConfigDefaultsColor struct {
	Mode string `json:"mode,omitempty"`
}

type GridPosition struct {
	H int `json:"h,omitempty"`
	W int `json:"w,omitempty"`
	X int `json:"x,omitempty"`
	Y int `json:"y,omitempty"`
}

type DashboardPanelType string

const (
	DashboardPanelTypeTimeseries DashboardPanelType = "timeseries"
	DashboardPanelTypeRow        DashboardPanelType = "row"
	DashboardPanelTypeGuage      DashboardPanelType = "guage"
	DashboardPanelTypeBarGauge   DashboardPanelType = "bargauge"
	DashboardPanelTypeGraph      DashboardPanelType = "graph"
	DashboardPanelTypeStat       DashboardPanelType = "stat"
	DashboardPanelTypeTable      DashboardPanelType = "table"
	DashboardPanelTypeText       DashboardPanelType = "text"
	DashboardPanelTypeSinglestat DashboardPanelType = "singlestat"
	DashboardPanelTypeDashlist   DashboardPanelType = "dashlist"
	DashboardPanelTypeAlertlist  DashboardPanelType = "alertlist"
	DashboardPanelTypeHeatmap    DashboardPanelType = "heatmap"
	DashboardPanelTypeLogs       DashboardPanelType = "logs"
	DashboardPanelTypeTrace      DashboardPanelType = "trace"
)

type DashboardDataSource struct {
	Type string `json:"type,omitempty"`
	UID  string `json:"uid,omitempty"`
}

func (d *DashboardDataSource) UnmarshalJSON(data []byte) error {
	var uid string
	if err := json.Unmarshal(data, &uid); err == nil {
		d.UID = uid
		return nil
	}
	type Alias DashboardDataSource
	aux := &struct {
		*Alias
	}{
		Alias: (*Alias)(d),
	}
	if err := json.Unmarshal(data, &aux); err != nil {
		return err
	}
	return nil
}

type DashboardPanelTarget struct {
	Datasource     DashboardDataSource `json:"datasource,omitempty"`
	Expr           string              `json:"expr,omitempty"`
	RefId          string              `json:"refId,omitempty"`
	EditorMode     string              `json:"editorMode,omitempty"`
	Exemplar       bool                `json:"exemplar,omitempty"`
	Hide           bool                `json:"hide,omitempty"`
	Format         string              `json:"format,omitempty"`
	Instant        bool                `json:"instant,omitempty"`
	Interval       string              `json:"interval,omitempty"`
	IntervalFactor int                 `json:"intervalFactor,omitempty"`
	LegendFormat   string              `json:"legendFormat,omitempty"`
	Range          bool                `json:"range,omitempty"`
	Step           int                 `json:"step,omitempty"`
}

type DashboardAnnotaions struct {
	Enable bool  `json:"enable,omitempty"`
	List   []any `json:"list,omitempty"`
}

type DashboardInput struct {
	Name        string `json:"name"`
	Type        string `json:"type"`
	Label       string `json:"label"`
	Description string `json:"description"`
	PluginId    string `json:"pluginId"`
	PluginName  string `json:"pluginName"`
}

type StringOrStringArray []string

func (s *StringOrStringArray) UnmarshalJSON(data []byte) error {
	if len(data) > 0 && data[0] == '[' {
		var arr []string
		if err := json.Unmarshal(data, &arr); err != nil {
			return err
		}
		*s = arr
	} else {
		var str string
		if err := json.Unmarshal(data, &str); err != nil {
			return err
		}
		*s = StringOrStringArray{str}
	}
	return nil
}

func (s StringOrStringArray) MarshalJSON() ([]byte, error) {
	if len(s) == 1 {
		return json.Marshal(s[0])
	}
	return json.Marshal([]string(s))
}

func (s StringOrStringArray) String() string {
	if len(s) == 1 {
		return s[0]
	}
	return "[" + strings.Join([]string(s), ",") + "]"
}
