package dashboard

import (
	"io/fs"
	"path/filepath"
	"strings"

	"xiaoshiai.cn/common/errors"
)

func LoadDashboards(fsys fs.FS) (map[string]DashboardConfiguration, error) {
	dashboards := make(map[string]DashboardConfiguration)

	entries, err := fs.ReadDir(fsys, "dashboards")
	if err != nil {
		return nil, err
	}
	var errs []error
	for _, entry := range entries {
		if entry.IsDir() {
			continue
		}
		ext := filepath.Ext(entry.Name())
		if ext != ".json" && ext != ".yaml" {
			continue
		}
		simplename := strings.TrimSuffix(entry.Name(), ext)

		data, err := fs.ReadFile(fsys, filepath.Join("dashboards", entry.Name()))
		if err != nil {
			errs = append(errs, err)
			continue
		}
		dashboardconfig, err := LoadDashboardConfiguration(data)
		if err != nil {
			errs = append(errs, err)
			continue
		}
		dashboards[simplename] = *dashboardconfig
	}
	return dashboards, errors.NewAggregate(errs)
}
