package dashboard

import (
	"context"
	"encoding/json"
	"log"
	"reflect"
	"testing"
	"time"

	"xiaoshiai.cn/rune/pkg/cloud/observability/monitoring/prometheus"
)

func TestPrometheusExprParser_Parse(t *testing.T) {
	tests := []struct {
		expr    string
		want    *PrometheusExprNode
		wantErr bool
	}{
		{
			expr: "label_values(node_uname_info, job)",
			want: &PrometheusExprNode{
				Type:  PrometheusExprNodeTypeFunc,
				Value: "label_values",
				Args: []PrometheusExprNode{
					{
						Type:  PrometheusExprNodeTypeIdentity,
						Value: "node_uname_info",
					},
					{
						Type:  PrometheusExprNodeTypeIdentity,
						Value: "job",
					},
				},
			},
		},
		{
			expr: "query_result(topk(5, sum(rate(http_requests_total[$__range])) by (instance)))",
			want: &PrometheusExprNode{
				Type:  PrometheusExprNodeTypeFunc,
				Value: "query_result",
				Args: []PrometheusExprNode{
					{
						Type:  PrometheusExprNodeTypeFunc,
						Value: "topk",
						Args: []PrometheusExprNode{
							{
								Type:  PrometheusExprNodeTypeIdentity,
								Value: "5",
							},
							{
								Type:  PrometheusExprNodeTypeFunc,
								Value: "sum",
								Args:  []PrometheusExprNode{},
							},
						},
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.expr, func(t *testing.T) {
			p := &PrometheusExprParser{Expr: tt.expr}
			got, err := p.Parse()
			if (err != nil) != tt.wantErr {
				t.Errorf("PrometheusExprParser.Parse() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("PrometheusExprParser.Parse() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestExecutePrometheusExpr(t *testing.T) {
	type args struct {
		expr   string
		params map[string]string
	}
	tests := []struct {
		name    string
		args    args
		want    []string
		wantErr bool
	}{
		{
			name: "label_values",
			args: args{
				expr: "label_values(node_uname_info, 'job')",
			},
		},

		{
			name: "query_result",
			args: args{
				expr: "query_result(topk(5, sum(rate(http_requests_total[$__range])) by (instance)))",
				params: map[string]string{
					"__range": "5m",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			dummy := &DummyPrometheus{}
			got, err := ExecutePrometheusExpr(context.Background(), tt.args.expr, dummy, tt.args.params)
			if (err != nil) != tt.wantErr {
				t.Errorf("ExecutePrometheusExpr() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ExecutePrometheusExpr() = %v, want %v", got, tt.want)
			}
		})
	}
}

type DummyPrometheus struct{}

// Query implements PrometheusDatasource.
func (d *DummyPrometheus) Query(ctx context.Context, query string, at time.Time) (*prometheus.ResponseData, error) {
	log.Printf("Query: %s, at: %s", query, at.Format(time.RFC3339))
	return &prometheus.ResponseData{
		Result:     json.RawMessage(`{"resultType":"vector","result":[{"metric":{"__name__":"up","instance":"localhost:9090","job":"prometheus"},"value":[1697059200,"1"]}]}`),
		ResultType: prometheus.ResultTypeVector,
	}, nil
}

// QueryRange implements PrometheusDatasource.
func (d *DummyPrometheus) QueryRange(ctx context.Context, query string, start time.Time, end time.Time, step time.Duration) (*prometheus.ResponseData, error) {
	log.Printf("QueryRange: %s, start: %s, end: %s, step: %s", query, start.Format(time.RFC3339), end.Format(time.RFC3339), step)
	return &prometheus.ResponseData{
		Result:     json.RawMessage(`{"resultType":"matrix","result":[{"metric":{"__name__":"up","instance":"localhost:9090","job":"prometheus"},"values":[[1697059200,"1"],[1697059260,"1"]]}]}`),
		ResultType: prometheus.ResultTypeMatrix,
	}, nil
}

// Type implements PrometheusDatasource.
func (d *DummyPrometheus) Type() string {
	return "prometheus"
}

var _ PrometheusDatasource = &DummyPrometheus{}
