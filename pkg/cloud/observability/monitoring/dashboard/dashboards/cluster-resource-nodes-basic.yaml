title: Node Overview
templating:
  list:
    - name: node
panels:
  - name: cpu_usage
    title: CPU Usage
    fieldConfig:
      defaults:
        unit: percent
    targets:
      - expr: gems_node_cpu_usage_percent{node="$node"}
        legendFormat: "{{ node }}"
        refId: A
        range: true

  - name: memory_usage
    title: Memory Usage
    fieldConfig:
      defaults:
        unit: percent
    targets:
      - expr: gems_node_memory_usage_percent{node="$node"}
        legendFormat: "{{ node }}"
        refId: A
        range: true

  - name: node_loads
    title: Node Load
    targets:
      - expr: node_load1{node="$node"}
        legendFormat: ""
        refId: A
        range: true
      - expr: node_load5{node="$node"}
        legendFormat: ""
        refId: B
        range: true
      - expr: node_load15{node="$node"}
        legendFormat: ""
        refId: C
        range: true

  - name: network
    title: Network Traffic
    fieldConfig:
      defaults:
        unit: bytes
    targets:
      - expr: gems_node_network_receive_bps{node="$node"}
        legendFormat: rx {{ node }}
        refId: A
        range: true
      - expr: gems_node_network_send_bps{node="$node"}
        legendFormat: tx {{ node }}
        refId: B
        range: true

  - name: disk_io
    title: Disk I/O
    fieldConfig:
      defaults:
        unit: iops
    targets:
      - expr: gems_node_disk_read_iops{node="$node"}
        legendFormat: "read {{ node }}"
        refId: A
        range: true
      - expr: gems_node_disk_write_iops{node="$node"}
        legendFormat: "write {{ node }}"
        refId: B
        range: true

  - name: storage
    title: Storage Available
    fieldConfig:
      defaults:
        unit: bytes
    targets:
      - expr: gems_node_disk_total_bytes{node="$node", device !~ "/dev/rbd.*"} - gems_node_disk_usage_bytes{node="$node", device !~ "/dev/rbd.*"}
        legendFormat: "{{ node }} {{ device }}"
        refId: A
        range: true

  - name: health
    title: Health
    fieldConfig:
      defaults:
        unit: bool_yes_no
    targets:
      - expr: kube_node_status_condition{node="$node", condition="Ready", status="true"}
        legendFormat: "{{ node }}"
        refId: A
        range: true
time:
  from: now-1h
  to: now
  step: 1m
