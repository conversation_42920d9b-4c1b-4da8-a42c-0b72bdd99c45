title: Storage Overview
templating:
  list:
    - name: namespace
    - name: instance
panels:
  - name: used_percent
    title: Used Percent
    fieldConfig:
      defaults:
        unit: percent
    targets:
      - expr: (sum by (persistentvolumeclaim) (ismc_pvc_storage_used_bytes{namespace="$namespace",instance="$instance"}) / sum by (persistentvolumeclaim) (ismc_pvc_storage_total_bytes{namespace="$namespace",instance="$instance"})) * 100
        legendFormat: "{{ persistentvolumeclaim }}"
        refId: A
        range: true
  - name: usage
    title: Used Total
    fieldConfig:
      defaults:
        unit: bytes
    targets:
      - expr: sum by (persistentvolumeclaim) (ismc_pvc_storage_used_bytes{namespace="$namespace",instance="$instance"})
        legendFormat: "{{ persistentvolumeclaim }} used"
        refId: A
        range: true
      - expr: sum by (persistentvolumeclaim) (ismc_pvc_storage_total_bytes{namespace="$namespace",instance="$instance"})
        legendFormat: "{{ persistentvolumeclaim }} total"
        refId: B
        range: true
  

  - name: inodes
    title: Inodes Used and Total
    targets:
      - expr: sum by (persistentvolumeclaim) (ismc_node_inode_used{namespace="$namespace",instance="$instance"})
        legendFormat: "{{ persistentvolumeclaim }} used"
        refId: A
        range: true
      - expr: sum by (persistentvolumeclaim) (ismc_node_inode_total{namespace="$namespace",instance="$instance"})
        legendFormat: "{{ persistentvolumeclaim }} total"
        refId: B
        range: true

  - name: inodes_percent
    title: Inodes Used Percent
    fieldConfig:
      defaults:
        unit: percent
    targets:
      - expr: (sum by (persistentvolumeclaim) (ismc_node_inode_used{namespace="$namespace",instance="$instance"}) / sum by (persistentvolumeclaim) (ismc_node_inode_total{namespace="$namespace",instance="$instance"})) * 100
        legendFormat: "{{ persistentvolumeclaim }}"
        refId: A
        range: true

time:
  from: now-1h
  to: now
  step: 1m
