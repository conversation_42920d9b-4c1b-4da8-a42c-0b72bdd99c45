title: Basic Overview
templating:
  list:
    - name: namespace
    - name: instance
panels:
  - name: cpu_usage
    title: CPU Usage
    fieldConfig:
      defaults:
        unit: percent
    targets:
      - expr: ismc_pod_cpu_usage_percent{namespace="$namespace", instance="$instance"}
        legendFormat: "{{ pod }}"
        refId: A
        range: true

  - name: memory_usage
    title: Memory Usage
    fieldConfig:
      defaults:
        unit: percent
    targets:
      - expr: ismc_pod_memory_usage_percent{namespace="$namespace", instance="$instance"}
        legendFormat: "{{ pod }}"
        refId: A
        range: true

  - name: network_io
    title: Network IO
    targets:
      - expr: ismc_pod_network_receive_bytes_per_5m{namespace="$namespace", instance="$instance"}
        legendFormat: "{{ pod }}"
        range: true
        refId: A
      - expr: ismc_pod_network_send_bytes_per_5m{namespace="$namespace", instance="$instance"}
        legendFormat: "{{ pod }}"
        range: true
        refId: B

  - name: health
    title: Health
    targets:
      - expr: ismc_pod_health{namespace="$namespace", instance="$instance"}
        legendFormat: "{{ pod }}"
        range: true
        refId: A

time:
  from: now-1h
  to: now
  step: 1m
