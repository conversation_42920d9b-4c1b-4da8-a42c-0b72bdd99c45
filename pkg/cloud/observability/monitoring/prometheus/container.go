package prometheus

import (
	"context"
	"time"

	"xiaoshiai.cn/rune/pkg/cloud/cluster"
)

var _ Prometheus = &ContainerOperationPrometheus{}

type ContainerOperationPrometheus struct {
	Operation cluster.ContainerOperation
}

// Query implements Datasource.
func (c ContainerOperationPrometheus) Query(ctx context.Context, expr string, at time.Time) (*ResponseData, error) {
	result := PrometheusResponse{}
	if err := c.Operation.PrometheusQuery(ctx, cluster.PrometheusQueryOptions{Query: expr, Time: at, Timeout: time.Second * 5}, &result); err != nil {
		return nil, err
	}
	return &result.Data, nil
}

// RangeQuery implements Datasource.
func (c ContainerOperationPrometheus) QueryRange(ctx context.Context, expr string, start time.Time, end time.Time, step time.Duration) (*ResponseData, error) {
	result := PrometheusResponse{}
	if err := c.Operation.PrometheusQueryRange(ctx, cluster.PrometheusQueryRangeOptions{Query: expr, Start: start, End: end, Step: step, Timeout: time.Second * 5}, &result); err != nil {
		return nil, err
	}
	return &result.Data, nil
}
