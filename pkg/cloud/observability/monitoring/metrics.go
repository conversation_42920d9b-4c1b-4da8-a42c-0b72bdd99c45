package monitoring

import (
	"context"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	corev1 "k8s.io/api/core/v1"
	"xiaoshiai.cn/common/errors"
	liberrors "xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/cloud/cluster"
	"xiaoshiai.cn/rune/pkg/cloud/observability/monitoring/dashboard"
)

func merge(kvs ...map[string]string) map[string]string {
	m := map[string]string{}
	for _, kv := range kvs {
		for k, v := range kv {
			m[k] = v
		}
	}
	return m
}

// it is ordered, the last one overwrites the previous one
// eg. "health" exists in both PodMetricsMapper and NodeMetricsMapper, the value in [PodMetricsMapper] will be used
var ContainerMetricsMapper = merge(NodeMetricsMapper, ServiceMetricsMapper, PodMetricsMapper, IngressMetricsMapper, OpenTelemetryMetricsMapper)

// performance metrics
var OpenTelemetryMetricsMapper = map[string]string{
	"requests_per_second": `ismc_pod_calls_rate{@}`,
	"requests_error_rate": `ismc_pod_calls_errors_rate{@}`,
	"requests_p99":        `histogram_quantile(0.99, ismc_pod_calls_latency_ms{@})`,
	"requests_p90":        `histogram_quantile(0.90, ismc_pod_calls_latency_ms{@})`,
	"requests_p75":        `histogram_quantile(0.75, ismc_pod_calls_latency_ms{@})`,
	"requests_p50":        `histogram_quantile(0.50, ismc_pod_calls_latency_ms{@})`,
}

var PodMetricsMapper = map[string]string{
	"cpu_used_cores":         `ismc_pod_cpu_used_cores{@}`,
	"cpu_used_percentage":    `ismc_pod_cpu_usage_percent{@}`,
	"memory_used_bytes":      `ismc_pod_memory_used_bytes{@}`,
	"memory_used_percentage": `ismc_pod_memory_usage_percent{@}`,
	"network_in_bps":         `ismc_pod_network_receive_bytes_per_5m{@}`,
	"network_out_bps":        `ismc_pod_network_send_bytes_per_5m{@}`,
	"health":                 `ismc_pod_health{@}`,
}

var ServiceMetricsMapper = map[string]string{
	"network_in_bps":  `round(ismc_pod_network_receive_bytes_per_5m{@}, 0.001)`,
	"network_out_bps": `round(ismc_pod_network_send_bytes_per_5m{@}, 0.001)`,
}

var PersistentVolumeClaimMetricsMapper = map[string]string{
	"storage_total_bytes": `kubelet_volume_stats_capacity_bytes{@}`,
	"storage_used_bytes":  `kubelet_volume_stats_used_bytes{@}`,
}

var IngressMetricsMapper = map[string]string{
	"ingress_qps":         `label_replace(sum(rate(nginx_ingress_controller_requests{exported_namespace=~"$namespace"}[1m])) by (exported_namespace,ingress,le),"namespace", "$1", "exported_namespace", "(.*)") * on (namespace, ingress) group_left group(kube_ingress_labels{label_app_kubernetes_io_instance = "$instance"}) by (label_app_kubernetes_io_instance, ingress, namespace)`,
	"ingress_latency_p99": `histogram_quantile(0.99, label_replace(sum(nginx_ingress_controller_request_duration_seconds_bucket{exported_namespace=~"$namespace"}) by (exported_namespace,ingress,le),"namespace", "$1", "exported_namespace", "(.*)") * on (namespace, ingress) group_left group(kube_ingress_labels{label_app_kubernetes_io_instance = "$instance"}) by (label_app_kubernetes_io_instance, ingress, namespace))`,
	"ingress_latency_p90": `histogram_quantile(0.90, label_replace(sum(nginx_ingress_controller_request_duration_seconds_bucket{exported_namespace=~"$namespace"}) by (exported_namespace,ingress,le),"namespace", "$1", "exported_namespace", "(.*)") * on (namespace, ingress) group_left group(kube_ingress_labels{label_app_kubernetes_io_instance = "$instance"}) by (label_app_kubernetes_io_instance, ingress, namespace))`,
}

var NodeMetricsMapper = map[string]string{
	"node_load1_cores":       `node_load1{@}`,
	"node_load5_cores":       `node_load5{@}`,
	"node_load15_cores":      `node_load15{@}`,
	"network_in_bytes":       `gems_node_network_receive_bps{@}`,
	"network_out_bytes":      `gems_node_network_send_bps{@}`,
	"disk_read_iops":         `gems_node_disk_read_iops{@}`,
	"disk_write_iops":        `gems_node_disk_write_iops{@}`,
	"cpu_used_percentage":    `gems_node_cpu_usage_percent{@}`,
	"memory_used_percentage": `gems_node_memory_usage_percent{@}`,
	"storage_free_bytes":     `gems_node_disk_total_bytes{@, device !~"/dev/rbd.*"} - gems_node_disk_usage_bytes{@, device !~"/dev/rbd.*"}`,
	"health":                 `kube_node_status_condition{condition="Ready", status="true", @}`,
}

var GetMetricsParams = []api.Param{
	api.QueryParam("time", "time point,if time set, use instant query").
		Format("datatime").Optional(),
	api.QueryParam("start", "start time,if time not set, use range query").
		Format("datatime").Optional(),
	api.QueryParam("end", "end time").Format("datatime").Optional(),
	api.QueryParam("step", "step duration").Format("duration").Optional(),

	api.QueryParam("filter", "filter expression,example: foo=bar,pod=abc ").Optional(),
	api.QueryParam("by", "sum/avg by labels").In("pod", "workload", "instance").Optional(),
	api.QueryParam("aggregate", "aggregate function").In("sum", "avg", "max", "min").Optional(),
}

type API struct {
	Store             store.Store
	CloudInfo         cluster.CloudInfoGetter
	BuiltInDashboards map[string]dashboard.DashboardConfiguration
}

func NewAPI(store store.Store, cloudinfo cluster.CloudInfoGetter, dashboards map[string]dashboard.DashboardConfiguration) *API {
	return &API{
		Store:             store,
		CloudInfo:         cloudinfo,
		BuiltInDashboards: dashboards,
	}
}

var MetricsParams = []api.Param{
	api.PathParam("metrics", "metrics name").
		In(getMetricskeys(PodMetricsMapper)...).
		In(getMetricskeys(NodeMetricsMapper)...).
		In(getMetricskeys(PersistentVolumeClaimMetricsMapper)...).
		In(getMetricskeys(IngressMetricsMapper)...).
		In(getMetricskeys(ServiceMetricsMapper)...),
}

func GetResourceMetrics(r *http.Request, op cluster.ContainerOperation, meta cluster.RequestMetadata) (any, error) {
	withNamespace := func(cond string, ns string) string {
		if ns == "" {
			return cond
		}
		return fmt.Sprintf(`namespace="%s", %s`, ns, cond)
	}
	params := map[string]string{
		"name": meta.Name,
	}
	if meta.Namespace != "" {
		params["namespace"] = meta.Namespace
	} else {
		params["namespace"] = ".*"
	}

	switch meta.GroupVersionResource {
	case corev1.SchemeGroupVersion.WithResource("pods"):
		cond := withNamespace(fmt.Sprintf(`pod="%s"`, meta.Name), meta.Namespace)
		return GetMetrics(r, op, PodMetricsMapper, cond)
	case corev1.SchemeGroupVersion.WithResource("services"):
		cond := withNamespace(fmt.Sprintf(`service="%s"`, meta.Name), meta.Namespace)
		params["service"] = meta.Name
		return GetMetrics(r, op, ServiceMetricsMapper, cond)
	case corev1.SchemeGroupVersion.WithResource("nodes"):
		cond := fmt.Sprintf(`node="%s"`, meta.Name)
		return GetMetrics(r, op, NodeMetricsMapper, cond)
	case corev1.SchemeGroupVersion.WithResource("persistentvolumeclaims"):
		cond := withNamespace(fmt.Sprintf(`persistentvolumeclaim="%s"`, meta.Name), meta.Namespace)
		return GetMetrics(r, op, PersistentVolumeClaimMetricsMapper, cond)
	case corev1.SchemeGroupVersion.WithResource("ingresses"):
		cond := withNamespace(fmt.Sprintf(`ingress="%s"`, meta.Name), meta.Namespace)
		params["ingress"] = meta.Name
		return GetMetricsWithParams(r, op, IngressMetricsMapper, cond, params)
	default:
		return nil, liberrors.NewBadRequest("unsupported resource")
	}
}

var EmptyMetrics = map[string]any{
	"status": "success",
	"data": map[string]any{
		"result":     []any{},
		"resultType": "matrix",
		"error":      "empty metrics",
	},
}

var UnknownMetrics = map[string]any{
	"status": "failed",
	"data": map[string]any{
		"result":     []any{},
		"resultType": "matrix",
		"error":      "unknown metrics",
	},
}

func GetApplicationMetrics(r *http.Request, op cluster.ContainerOperation, cond string, params map[string]string) (any, error) {
	return GetMetricsWithParams(r, op, ContainerMetricsMapper, cond, params)
}

var rateMetricPattern = regexp.MustCompile(`.+_(percentage|rate|p9[0-9])$`)

func GetMetrics(r *http.Request, op cluster.ContainerOperation, metricsmap map[string]string, cond string) (any, error) {
	return GetMetricsWithParams(r, op, metricsmap, cond, nil)
}

func GetMetricsWithParams(r *http.Request, op cluster.ContainerOperation, metricsmap map[string]string, cond string, params map[string]string) (any, error) {
	metrics := api.Path(r, "metrics", "")
	query, ok := metricsmap[metrics]
	if !ok {
		return nil, liberrors.NewBadRequest("unsupported metric")
	}
	filter := api.Query(r, "filter", "")
	if filter != "" {
		cond += ", " + filter
	}
	// replace @ with condition
	query = strings.Replace(query, "@", cond, -1)

	// replace $<param> with value
	query = TemplateQuery(query, params)

	// sum/avg or other aggregate functions
	aggregate := api.Query(r, "aggregate", "")
	if aggregate == "" {
		if rateMetricPattern.MatchString(metrics) {
			aggregate = "avg"
		} else {
			aggregate = "sum"
		}
	}
	// aggregate by labels
	if by := api.Query(r, "by", ""); by != "" {
		// ingress will not aggregateed
		if !strings.HasPrefix(metrics, "ingress_") {
			query = fmt.Sprintf(`%s(%s) by(%s)`, aggregate, query, by)
		}
	}

	// if time is specified, use instant query
	if tim := api.Query(r, "time", time.Time{}); !tim.IsZero() {
		options := cluster.PrometheusQueryOptions{
			Query:   query,
			Timeout: api.Query(r, "timeout", time.Duration(0)),
			Time:    tim,
		}
		log.FromContext(r.Context()).Info("prometheus query", "query", options.Query)
		response := map[string]any{}
		if err := op.PrometheusQuery(r.Context(), options, &response); err != nil {
			return nil, err
		}
		return response, nil
	}
	// otherwise, use range query
	options := cluster.PrometheusQueryRangeOptions{
		Query:   query,
		Start:   api.Query(r, "start", time.Now().Add(-5*time.Minute)),
		End:     api.Query(r, "end", time.Now()),
		Step:    api.Query(r, "step", time.Duration(1*time.Minute)),
		Timeout: api.Query(r, "timeout", time.Duration(0)),
	}
	log.FromContext(r.Context()).Info("prometheus range query", "query", options.Query)
	response := map[string]any{}
	if err := op.PrometheusQueryRange(r.Context(), options, &response); err != nil {
		return nil, err
	}
	return response, nil
}

type MetricsDashboard struct {
	store.ObjectMeta `json:",inline"`
	Dashboard        dashboard.DashboardConfiguration `json:"dashboard"`
}

type QueryResponse struct {
	ShowName string `json:"showName"`
	Unit     string `json:"unit"`
	Values   any    `json:"values"`
}

func SetPrometheusMonitorDashboard(ctx context.Context, scopedstorage store.Store, name string,
	dashboard dashboard.DashboardConfiguration,
) (*MetricsDashboard, error) {
	exists := &MetricsDashboard{}
	if err := scopedstorage.Get(ctx, name, exists); err != nil {
		if !errors.IsNotFound(err) {
			return nil, err
		}
		newdashboard := &MetricsDashboard{
			ObjectMeta: store.ObjectMeta{Name: name, Description: "Auto created by system"},
			Dashboard:  dashboard,
		}
		if err := scopedstorage.Create(ctx, newdashboard); err != nil {
			return nil, err
		}
		return newdashboard, nil
	}
	exists.Dashboard = dashboard
	if err := scopedstorage.Update(ctx, exists); err != nil {
		return nil, err
	}
	return exists, nil
}

func TemplateQuery(queryTemplate string, params map[string]string) string {
	for k, v := range params {
		queryTemplate = strings.ReplaceAll(queryTemplate, fmt.Sprintf("$%s", k), v)
	}
	return queryTemplate
}

func (a *API) Group() api.Group {
	return api.
		NewGroup("").
		Tag("Monitoring").
		SubGroup(
			a.InstanceGroup(),
			a.ClusterGroup(),
		)
}
