package alert

import (
	"context"
	"log"
	"net/http"
	"slices"

	v1 "github.com/prometheus/client_golang/api/prometheus/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/base"
	"xiaoshiai.cn/rune/pkg/cloud/cluster/operation"
	"xiaoshiai.cn/rune/pkg/cloud/workspace"
)

func (a *API) ListApplicationAlertRules(w http.ResponseWriter, r *http.Request) {
	base.OnInstance(w, r, func(ctx context.Context, tenant, workspacename, app string) (any, error) {
		info, _, err := workspace.GetWorkspaceCluster(ctx, a.CloudInfo, a.Store, tenant, workspacename)
		if err != nil {
			return nil, err
		}
		op := operation.NewContainerOperation(info)

		storage := a.Store.Scope(base.ScopeTenant(tenant), base.ScopeWorkspace(workspacename), base.ScopeInstance(app))
		storageAlertRules := &store.List[AlertRule]{}
		if err := base.GenericListFromRequest(r, storage, storageAlertRules); err != nil {
			return nil, err
		}
		if err := setAlertRuleStatusFromPrometheus(ctx, op, storageAlertRules); err != nil {
			log.Printf("error get prometheus alert status: %v", err)
		}
		return storageAlertRules, nil
	})
}

type PromeAlertRulesResp struct {
	Status    string         `json:"status"`
	Data      v1.RulesResult `json:"data"`
	ErrorType v1.ErrorType   `json:"errorType"`
	Error     string         `json:"error"`
	Warnings  []string       `json:"warnings,omitempty"`
}

func setAlertRuleStatusFromPrometheus(ctx context.Context, op operation.ContainerOperation, rules *store.List[AlertRule]) error {
	prr := &PromeAlertRulesResp{}
	if err := op.PrometheusRules(ctx, operation.PrometheusRulesOptions{Type: "alert"}, prr); err != nil {
		return err
	}
	alertRuleMap := make(map[string]*AlertRule, len(rules.Items))
	for i := range rules.Items {
		name := rules.Items[i].GetKubernetesResourceName()
		alertRuleMap[name] = &rules.Items[i]
	}
	for _, group := range prr.Data.Groups {
		for _, rule := range group.Rules {
			if alertRule, ok := rule.(v1.AlertingRule); ok {
				if localRule, found := alertRuleMap[alertRule.Name]; found {
					localRule.Status = alertRule.State
				}
			}
		}
	}
	return nil
}

func (a *API) GetApplicationAlertRule(w http.ResponseWriter, r *http.Request) {
	a.OnApplicationAlertRule(w, r, func(ctx context.Context, storage store.Store, rule string) (any, error) {
		return base.GenericGet(r, storage, &AlertRule{}, rule)
	})
}

func (a *API) CreateApplicationAlertRule(w http.ResponseWriter, r *http.Request) {
	base.OnInstance(w, r, func(ctx context.Context, tenant, workspace, app string) (any, error) {
		storage := a.Store.Scope(base.ScopeTenant(tenant), base.ScopeWorkspace(workspace), base.ScopeInstance(app))
		return base.GenericCreate(r, storage, &AlertRule{})
	})
}

func (a *API) UpdateApplicationAlertRule(w http.ResponseWriter, r *http.Request) {
	a.OnApplicationAlertRule(w, r, func(ctx context.Context, storage store.Store, rule string) (any, error) {
		return base.GenericUpdate(r, storage, &AlertRule{}, rule)
	})
}

func (a *API) DeleteApplicationAlertRule(w http.ResponseWriter, r *http.Request) {
	a.OnApplicationAlertRule(w, r, func(ctx context.Context, storage store.Store, rule string) (any, error) {
		return base.GenericDelete(r, storage, &AlertRule{}, rule)
	})
}

func (a *API) OnApplicationAlertRule(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, storage store.Store, rule string) (any, error)) {
	base.OnInstance(w, r, func(ctx context.Context, tenant, workspace, app string) (any, error) {
		rule := api.Path(r, "rule", "")
		if rule == "" {
			return nil, errors.NewBadRequest("rule name is required")
		}
		storage := a.Store.Scope(base.ScopeTenant(tenant), base.ScopeWorkspace(workspace), base.ScopeInstance(app))
		return fn(ctx, storage, rule)
	})
}

func (a *API) instanceAlertRuleGroup() api.Group {
	return api.
		NewGroup("/alertrules").
		Route(
			api.GET("").Operation("list alert rules").
				To(a.ListApplicationAlertRules).
				Param(api.PageParams...).
				Response(store.List[AlertRule]{}),

			api.POST("").Operation("create alert rule").
				To(a.CreateApplicationAlertRule).
				Param(api.BodyParam("rule", AlertRule{})).
				Response(AlertRule{}),

			api.GET("/{rule}").Operation("get alert rule").
				To(a.GetApplicationAlertRule).
				Response(AlertRule{}),

			api.PUT("/{rule}").Operation("update alert rule").
				Param(api.BodyParam("rule", AlertRule{})).
				To(a.UpdateApplicationAlertRule),

			api.DELETE("/{rule}").Operation("delete alert rule").
				To(a.DeleteApplicationAlertRule),
		)
}

func (a *API) instanceRuleTemplateGroup() api.Group {
	return api.
		NewGroup("").
		Route(
			api.GET("/templategroups").
				Doc("List prometheus rule template group under application").
				Param(api.PageParams...).
				Param(
					api.QueryParam("type", "application type").In("application", "database").Optional(),
				).
				To(a.ListApplicationPrometheusRuleTemplateGroup).
				Response(store.List[AlertRuleTemplateGroup]{}),

			api.GET("/templategroups/{templategroup}/resources").
				Doc("List prometheus rule template group resource under application").
				Param(api.PageParams...).
				Param(
					api.QueryParam("type", "application type").In("application", "database").Optional(),
				).
				To(a.ListApplicationPrometheusRuleTemplateGroupResource).
				Response(store.List[AlertRuleTemplateResourceGroup]{}),

			api.GET("/templategroups/{templategroup}/resources/{resource}/ruletemplates").
				Doc("List prometheus rule templates under group under application").
				Param(api.PageParams...).
				To(a.ListApplicationPrometheusRuleTemplate).
				Response(store.List[AlertRuleTemplate]{}),
		)
}

// containers opentelemetry middlewares cloud
func (a *API) ListApplicationPrometheusRuleTemplateGroup(w http.ResponseWriter, r *http.Request) {
	api.On(w, r, func(ctx context.Context) (any, error) {
		list := &store.List[AlertRuleTemplateGroup]{}
		if err := a.Store.List(ctx, list); err != nil {
			return nil, err
		}
		var (
			newList  []AlertRuleTemplateGroup
			includes []string
		)
		tp := api.Query(r, "type", "")
		switch tp {
		case "database":
			includes = append(includes, "Application", "Database")
		default:
			// 普通应用，非数据库应用，不返回Database
			includes = append(includes, "Middleware", "Application", "Opentelemetry")
		}

		for i := range list.Items {
			item := list.Items[i]
			for _, include := range includes {
				if item.Name == include {
					newList = append(newList, item)
				}
			}
		}
		return store.List[AlertRuleTemplateGroup]{
			Items: newList,
			Total: len(newList),
			Page:  list.Page,
			Size:  list.Size,
		}, nil
	})
}

func (a *API) ListApplicationPrometheusRuleTemplateGroupResource(w http.ResponseWriter, r *http.Request) {
	a.OnTemplateGroup(w, r, func(ctx context.Context, storage store.Store) (any, error) {
		list := &store.List[AlertRuleTemplateResourceGroup]{}
		if err := storage.List(ctx, list); err != nil {
			return nil, err
		}
		tp := api.Query(r, "type", "")
		switch tp {
		case "database":
			var newList []AlertRuleTemplateResourceGroup
			for i := range list.Items {
				item := list.Items[i]
				if !slices.Contains([]string{"Huawei", "VMware", "MongoDB"}, item.Name) {
					newList = append(newList, item)
				}
			}
			return store.List[AlertRuleTemplateResourceGroup]{
				Items: newList,
				Total: len(newList),
				Page:  list.Page,
				Size:  list.Size,
			}, nil
		default:
			return list, nil
		}
	})
}

func (a *API) ListApplicationPrometheusRuleTemplate(w http.ResponseWriter, r *http.Request) {
	a.OnTemplateGroupResource(w, r, func(ctx context.Context, storage store.Store) (any, error) {
		return base.GenericListWithWatch(w, r, storage, &store.List[AlertRuleTemplate]{}, store.WithSort("name"))
	})
}

func (a *API) OnTemplateGroupResource(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, storage store.Store) (any, error)) {
	a.OnTemplateGroup(w, r, func(ctx context.Context, storage store.Store) (any, error) {
		resource := api.Path(r, "resource", "")
		if resource == "" {
			return nil, errors.NewBadRequest("resource name is required")
		}
		obj := &store.Unstructured{}
		obj.SetResource("alertruletemplateresourcegroups")
		if err := storage.Get(ctx, resource, obj); err != nil {
			return nil, err
		}
		storage = storage.Scope(store.Scope{Resource: "alertruletemplateresourcegroups", Name: resource})
		return fn(ctx, storage)
	})
}

func (a *API) OnTemplateGroup(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, storage store.Store) (any, error)) {
	api.On(w, r, func(ctx context.Context) (any, error) {
		templateGroup := api.Path(r, "templategroup", "")
		if templateGroup == "" {
			return nil, errors.NewBadRequest("templateGroup name is required")
		}
		obj := &store.Unstructured{}
		obj.SetResource("alertruletemplategroups")
		if err := a.Store.Get(ctx, templateGroup, obj); err != nil {
			return nil, err
		}
		templateGroupStorage := a.Store.Scope(store.Scope{Resource: "alertruletemplategroups", Name: templateGroup})
		return fn(ctx, templateGroupStorage)
	})
}
