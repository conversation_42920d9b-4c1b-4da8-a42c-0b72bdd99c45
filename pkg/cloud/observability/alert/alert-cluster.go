package alert

import (
	"context"
	"net/http"

	"github.com/prometheus/common/model"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/base"
	"xiaoshiai.cn/rune/pkg/cloud/cluster"
	"xiaoshiai.cn/rune/pkg/cloud/cluster/operation"
	"xiaoshiai.cn/rune/pkg/cloud/observability/alert/alertchannel"
)

/*
Cluster 			集群模版
Opentelemetry 		集群模版和应用模版
Middleware 		    应用模版
Application 		应用模版
Database 			应用模版
*/

func (a *API) clusterRuleTemplateGroup() api.Group {
	return api.
		NewGroup("").
		Route(
			api.GET("/templategroups").
				Doc("List prometheus rule template group under cluster").
				Param(api.PageParams...).
				To(a.ListClusterPrometheusRuleTemplateGroup).
				Response(store.List[AlertRuleTemplateGroup]{}),

			api.GET("/templategroups/{templategroup}/resources").
				Doc("List prometheus rule template group resource under cluster").
				Param(api.PageParams...).
				To(a.ListClusterPrometheusRuleTemplateGroupResource).
				Response(store.List[AlertRuleTemplateResourceGroup]{}),

			api.GET("/templategroups/{templategroup}/resources/{resource}/ruletemplates").
				Doc("List prometheus rule templates under group under cluster").
				Param(api.PageParams...).
				To(a.ListPrometheusRuleTemplate).
				Response(store.List[AlertRuleTemplate]{}),
		)
}

func (a *API) ListClusterPrometheusRuleTemplateGroupResource(w http.ResponseWriter, r *http.Request) {
	a.OnTemplateGroup(w, r, func(ctx context.Context, storage store.Store) (any, error) {
		list := &store.List[AlertRuleTemplateResourceGroup]{}
		return base.GenericList(r, storage, list)
	})
}

func (a *API) ListClusterPrometheusRuleTemplateGroup(w http.ResponseWriter, r *http.Request) {
	a.onCluster(w, r, func(ctx context.Context, storage store.Store, clusterref store.ObjectReference) (any, error) {
		list := &store.List[AlertRuleTemplateGroup]{}
		if err := a.Store.List(ctx, list); err != nil {
			return nil, err
		}
		var (
			newList  []AlertRuleTemplateGroup
			includes = []string{"Cluster"}
		)
		for i := range list.Items {
			item := list.Items[i]
			for _, include := range includes {
				if item.Name == include {
					newList = append(newList, item)
				}
			}
		}
		return store.List[AlertRuleTemplateGroup]{
			Items: newList,
			Total: len(newList),
			Page:  list.Page,
			Size:  list.Size,
		}, nil
	})
}

func (a *API) clusterAlertRecordGroup() api.Group {
	return api.
		NewGroup("/alertrecords").
		SubGroup(
			api.NewGroup("graphs").
				Route(
					api.GET("").
						Operation("query cluster alert graph").
						To(a.QueryClusterAlertsGraph).
						Param(api.QueryParam("start", "alert graph start time")).
						Param(api.QueryParam("end", "alert graph end time")).
						Response(model.Matrix{}),
				),
		).
		Route(
			api.GET("").Operation("list cluster alert records").
				To(a.ListClusterAlertRecords).
				Param(api.QueryParam("alert", "alert name").Optional()).
				Param(api.PageParams...).
				Response(store.List[AlertRecord]{}),

			api.GET("/{record}").Operation("get cluster alert record").
				To(a.GetClusterAlertRecord).
				Response(AlertRecord{}),

			api.DELETE("/{record}").Operation("delete cluster alert record").
				To(a.DeleteClusterAlertRecord),
		)
}

func (a *API) QueryClusterAlertsGraph(w http.ResponseWriter, r *http.Request) {
	a.onClusterWithMongoStore(w, r, func(ctx context.Context, _ store.Store, mongoStore store.Store, clusterref store.ObjectReference, rs []store.Requirement) (any, error) {
		return getAlertGraph(ctx, r, mongoStore, rs)
	})
}

func (a *API) ListClusterAlertRecords(w http.ResponseWriter, r *http.Request) {
	a.onClusterWithMongoStore(w, r, func(ctx context.Context, _, mongoStore store.Store, clusterref store.ObjectReference, rs []store.Requirement) (any, error) {
		var labelrequirements []store.Requirement
		if alertName := api.Query(r, "alert", ""); alertName != "" {
			labelrequirements = append(labelrequirements, store.RequirementEqual(alertchannel.AlertOriginNameLabel, alertName))
		}
		var opt []store.ListOption
		if len(labelrequirements) > 0 {
			opt = append(opt, store.WithLabelRequirements(labelrequirements...))
		}
		opt = append(opt, store.WithFieldRequirements(rs...))
		ar := &store.List[AlertRecord]{}
		if err := mongoStore.List(ctx, ar, opt...); err != nil {
			return nil, err
		}
		return ar, nil
	})
}

func (a *API) GetClusterAlertRecord(w http.ResponseWriter, r *http.Request) {
	a.onClusterWithMongoStore(w, r, func(ctx context.Context, _, mongoStore store.Store, clusterref store.ObjectReference, rs []store.Requirement) (any, error) {
		record := api.Path(r, "record", "")
		if record == "" {
			return nil, errors.NewBadRequest("record name is required")
		}
		ar := &AlertRecord{}
		if err := mongoStore.Get(ctx, record, ar, store.WithGetFieldRequirements(rs...)); err != nil {
			return nil, err
		}
		return ar, nil
	})
}

func (a *API) DeleteClusterAlertRecord(w http.ResponseWriter, r *http.Request) {
	a.onClusterWithMongoStore(w, r, func(ctx context.Context, _, mongoStore store.Store, clusterref store.ObjectReference, rs []store.Requirement) (any, error) {
		record := api.Path(r, "record", "")
		if record == "" {
			return nil, errors.NewBadRequest("record name is required")
		}
		return nil, mongoStore.Delete(ctx, &AlertRecord{
			ObjectMeta: store.ObjectMeta{
				Name: record,
			},
		}, store.WithDeleteFieldRequirements(rs...))
	})
}

func (a *API) clusterAlertRuleGroup() api.Group {
	return api.
		NewGroup("/alertrules").
		Route(
			api.GET("").Operation("list alert rules").
				To(a.ListClusterAlertRules).
				Param(api.PageParams...).
				Response(store.List[AlertRule]{}),

			api.POST("").Operation("create alert rule").
				To(a.CreateClusterAlertRule).
				Param(api.BodyParam("rule", AlertRule{})).
				Response(AlertRule{}),

			api.GET("/{rule}").Operation("get alert rule").
				To(a.GetClusterAlertRule).
				Response(AlertRule{}),

			api.PUT("/{rule}").Operation("update alert rule").
				Param(api.BodyParam("rule", AlertRule{})).
				To(a.UpdateClusterAlertRule),

			api.DELETE("/{rule}").Operation("delete alert rule").
				To(a.DeleteClusterAlertRule),
		)
}

func (a *API) ListClusterAlertRules(w http.ResponseWriter, r *http.Request) {
	a.onCluster(w, r, func(ctx context.Context, storage store.Store, clusterref store.ObjectReference) (any, error) {
		info, err := a.CloudInfo.Get(ctx, clusterref)
		if err != nil {
			return nil, err
		}
		kubes, err := info.KubernetesConfig()
		if err != nil {
			return nil, err
		}

		op := operation.NewContainerOperation(kubes)

		storageAlertRules, err := base.GenericListWithWatch(w, r, storage, &store.List[AlertRule]{})
		if err != nil {
			return nil, err
		}
		setAlertRuleStatusFromPrometheus(ctx, op, storageAlertRules.(*store.List[AlertRule]))
		return storageAlertRules, nil
	})
}

func (a *API) CreateClusterAlertRule(w http.ResponseWriter, r *http.Request) {
	a.onCluster(w, r, func(ctx context.Context, storage store.Store, clusterref store.ObjectReference) (any, error) {
		return base.GenericCreate(r, storage, &AlertRule{})
	})
}

func (a *API) GetClusterAlertRule(w http.ResponseWriter, r *http.Request) {
	a.onCluster(w, r, func(ctx context.Context, storage store.Store, clusterref store.ObjectReference) (any, error) {
		rule := api.Path(r, "rule", "")
		if rule == "" {
			return nil, errors.NewBadRequest("rule name is required")
		}
		return base.GenericGet(r, storage, &AlertRule{}, rule)
	})
}

func (a *API) UpdateClusterAlertRule(w http.ResponseWriter, r *http.Request) {
	a.onCluster(w, r, func(ctx context.Context, storage store.Store, clusterref store.ObjectReference) (any, error) {
		rule := api.Path(r, "rule", "")
		if rule == "" {
			return nil, errors.NewBadRequest("rule name is required")
		}
		return base.GenericUpdate(r, storage, &AlertRule{}, rule)
	})
}

func (a *API) DeleteClusterAlertRule(w http.ResponseWriter, r *http.Request) {
	a.onCluster(w, r, func(ctx context.Context, storage store.Store, clusterref store.ObjectReference) (any, error) {
		rule := api.Path(r, "rule", "")
		if rule == "" {
			return nil, errors.NewBadRequest("rule name is required")
		}
		return base.GenericDelete(r, storage, &AlertRule{}, rule)
	})
}

func (a *API) onCluster(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, storage store.Store, clusterref store.ObjectReference) (any, error)) {
	cluster.OnCluster(w, r, func(ctx context.Context, clusterref store.ObjectReference) (any, error) {
		store := a.Store.Scope(clusterref.Scopes...).Scope(base.ScopeCluster(clusterref.Name))
		return fn(ctx, store, clusterref)
	})
}

func (a *API) onClusterWithMongoStore(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, storage store.Store, mongoStore store.Store, clusterref store.ObjectReference, rs []store.Requirement) (any, error)) {
	cluster.OnCluster(w, r, func(ctx context.Context, clusterref store.ObjectReference) (any, error) {
		rss := GetAlertRecordFieldsWithScopes(clusterref.Scopes)
		rss = append(rss, store.RequirementEqual("cluster", clusterref.Name))
		return fn(ctx, a.Store, a.Store, clusterref, rss)
	})
}
