package alert

import (
	"context"
	"net/http"
	"sort"
	"time"

	"github.com/prometheus/common/model"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/base"
	"xiaoshiai.cn/rune/pkg/cloud/cluster"
	"xiaoshiai.cn/rune/pkg/cloud/observability/alert/alertchannel"
)

func GetAlertRecordFieldsWithScopes(scopes []store.Scope) []store.Requirement {
	var sr []store.Requirement
	for _, scope := range scopes {
		switch scope.Resource {
		case "tenants":
			sr = append(sr, store.RequirementEqual("tenant", scope.Name))
		case "clusters":
			sr = append(sr, store.RequirementEqual("cluster", scope.Name))
		case "organizations":
			sr = append(sr, store.RequirementEqual("organization", scope.Name))
		case "applications":
			sr = append(sr, store.RequirementEqual("application", scope.Name))
		}
	}
	return sr
}

func GetAlertRecordFields(scope AlertRecordScope) []store.Requirement {
	var sr []store.Requirement
	if scope.Tenant != "" {
		sr = append(sr, store.RequirementEqual("tenant", scope.Tenant))
	}
	if scope.Cluster != "" {
		sr = append(sr, store.RequirementEqual("cluster", scope.Cluster))
	}
	if scope.Organization != "" {
		sr = append(sr, store.RequirementEqual("organization", scope.Organization))
	}
	if scope.Application != "" {
		sr = append(sr, store.RequirementEqual("application", scope.Application))
	}
	return sr
}

type API struct {
	CloudInfo cluster.CloudInfoGetter
	Store     store.Store
}

func NewAPI(store store.Store, cloudinfo cluster.CloudInfoGetter) *API {
	return &API{
		CloudInfo: cloudinfo,
		Store:     store,
	}
}

func (a *API) ListApplicationAlertRecords(w http.ResponseWriter, r *http.Request) {
	base.OnInstance(w, r, func(ctx context.Context, tenant, workspace, app string) (any, error) {
		rs := ReferenceToFieldRequirements(tenant, workspace, app)

		var labelrequirements []store.Requirement
		if alertName := api.Query(r, "alert", ""); alertName != "" {
			labelrequirements = append(labelrequirements, store.RequirementEqual(alertchannel.AlertOriginNameLabel, alertName))
		}
		var opt []store.ListOption
		if len(labelrequirements) > 0 {
			opt = append(opt, store.WithLabelRequirements(labelrequirements...))
		}
		opt = append(opt, store.WithFieldRequirements(rs...))
		ar := &store.List[AlertRecord]{}
		if err := a.Store.List(ctx, ar, opt...); err != nil {
			return nil, err
		}
		return ar, nil
	})
}

func ReferenceToFieldRequirements(tenant, workspace, app string) []store.Requirement {
	var rs []store.Requirement
	if tenant != "" {
		rs = append(rs, store.RequirementEqual("tenant", tenant))
	}
	if workspace != "" {
		rs = append(rs, store.RequirementEqual("workspace", workspace))
	}
	if app != "" {
		rs = append(rs, store.RequirementEqual("application", app))
	}
	return rs
}

func (a *API) GetApplicationAlertRecord(w http.ResponseWriter, r *http.Request) {
	base.OnInstance(w, r, func(ctx context.Context, tenant, workspace, app string) (any, error) {
		rs := ReferenceToFieldRequirements(tenant, workspace, app)
		record := api.Path(r, "record", "")
		if record == "" {
			return nil, errors.NewBadRequest("record name is required")
		}
		ar := &AlertRecord{}
		if err := a.Store.Get(ctx, record, ar, store.WithGetFieldRequirements(rs...)); err != nil {
			return nil, err
		}
		return ar, nil
	})
}

func (a *API) DeleteApplicationAlertRecord(w http.ResponseWriter, r *http.Request) {
	base.OnInstance(w, r, func(ctx context.Context, tenant, workspace, app string) (any, error) {
		record := api.Path(r, "record", "")
		if record == "" {
			return nil, errors.NewBadRequest("record name is required")
		}
		rs := ReferenceToFieldRequirements(tenant, workspace, app)
		obj := &AlertRecord{
			ObjectMeta: store.ObjectMeta{
				Name: record,
			},
		}
		return nil, a.Store.Delete(ctx, obj, store.WithDeleteFieldRequirements(rs...))
	})
}

// 应用层级的告警历史
func (a *API) instanceAlertRecordGroup() api.Group {
	return api.
		NewGroup("/alertrecords").
		SubGroup(
			api.NewGroup("graphs").
				Route(
					api.GET("").
						Operation("query application alert graph").
						To(a.QueryApplicationAlertsGraph).
						Param(api.QueryParam("start", "alert graph start time")).
						Param(api.QueryParam("end", "alert graph end time")).
						Response(model.Matrix{}),
				),
		).
		Route(
			api.GET("").
				Operation("list alert records").
				To(a.ListApplicationAlertRecords).
				Param(api.QueryParam("alert", "alert name").Optional()).
				Param(api.PageParams...).
				Response(store.List[AlertRecord]{}),

			api.GET("/{record}").
				Operation("get alert record").
				To(a.GetApplicationAlertRecord).
				Response(AlertRecord{}),

			api.DELETE("/{record}").
				Operation("delete alert record").
				To(a.DeleteApplicationAlertRecord),
		)
}

func (a *API) QueryApplicationAlertsGraph(w http.ResponseWriter, r *http.Request) {
	base.OnInstance(w, r, func(ctx context.Context, tenant, workspace, app string) (any, error) {
		return nil, nil
		// return getAlertGraph(ctx, r, mongoStore, rs)
	})
}

func getAlertGraph(ctx context.Context, r *http.Request, mongoStorage store.Store, rs []store.Requirement) (model.Matrix, error) {
	loc := time.Local

	start := api.Query(r, "start", time.Time{})
	if start.IsZero() {
		start = dayStartTime(time.Now(), loc)
	} else {
		start = start.In(loc)
	}
	end := api.Query(r, "end", time.Time{})
	if end.IsZero() {
		end = dayStartTime(time.Now(), loc).Add(24 * time.Hour)
	} else {
		end = end.In(loc)
	}
	alertRecords := &store.List[AlertRecord]{}
	if err := mongoStorage.List(ctx, alertRecords, store.WithFieldRequirements(rs...)); err != nil {
		return nil, err
	}
	mp := make(map[int64]int64)
	for d := start; !d.After(end); d = d.AddDate(0, 0, 1) {
		mp[dayStartTime(d, loc).Unix()] = 0
	}
	for _, item := range alertRecords.Items {
		if item.Record.StartsAt != nil {
			startAt := item.Record.StartsAt.Local()
			createTime := dayStartTime(startAt, loc).Unix()
			if _, exists := mp[createTime]; exists {
				mp[createTime] += item.Count
			}
		}
	}
	var keys []int64
	for k := range mp {
		keys = append(keys, k)
	}
	sort.Slice(keys, func(i, j int) bool {
		return keys[i] < keys[j]
	})
	ret := model.Matrix{}
	values := make([]model.SamplePair, 0)
	for _, k := range keys {
		v := mp[k]
		values = append(values, model.SamplePair{
			Timestamp: model.Time(k * 1000),
			Value:     model.SampleValue(v),
		})
	}
	ret = append(ret, &model.SampleStream{Values: values})
	return ret, nil
}

func dayStartTime(t time.Time, loc *time.Location) time.Time {
	year, month, day := t.Date()
	return time.Date(year, month, day, 0, 0, 0, 0, loc)
}
