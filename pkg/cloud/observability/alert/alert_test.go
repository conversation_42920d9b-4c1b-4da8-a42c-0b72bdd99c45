package alert

import (
	"context"
	"testing"

	v1 "github.com/prometheus/client_golang/api/prometheus/v1"
	"k8s.io/client-go/tools/clientcmd"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/cloud/cluster"
	"xiaoshiai.cn/rune/pkg/cloud/cluster/operation"
	"xiaoshiai.cn/rune/pkg/cloud/observability/alert/alertchannel"
)

const kubeconfigPath = "../tmp/develop.config"

// func addScheme() *runtime.Scheme {
// 	scheme := runtime.NewScheme()
// 	monitoringv1.AddToScheme(scheme)
// 	v1alpha1.AddToScheme(scheme)
// 	return scheme
// }

func setupContainerOperation(ctx context.Context) (*operation.ContainerOperation, error) {
	cfg, err := clientcmd.BuildConfigFromFlags("", kubeconfigPath)
	if err != nil {
		return nil, err
	}
	cli, err := cluster.NewKubernetesClients(ctx, cfg)
	if err != nil {
		return nil, err
	}
	return &operation.ContainerOperation{Info: cli}, nil
}

var testRule = &AlertRule{
	// rule部分
	ObjectMeta: store.ObjectMeta{
		Name: "test-zhengke",
	},
	AlertLevels: []AlertLevel{
		{
			CompareOp:    ">",
			CompareValue: "4",
			Severity:     "critical",
		},
	},
	For:  "10s",
	Expr: `(1 - avg(irate(node_cpu_seconds_total{mode="idle"}[5m]))) * 100`,
	// Message: `cluster-high-cpu-load: [cluster:{{ $externalLabels.cluster }}] Cluster
	//       CPU Usage Ratio (%) trigger alert, value: {{ $value | printf "%.1f" }}`,

	// 告警发送部分
	Receivers: []*AlertReceiver{
		{
			Interval: "1m",
			AlertChannel: &AlertChannel{
				ObjectMeta: store.ObjectMeta{
					Name: "test",
				},
				ChannelType: "email",
				ChannelEmail: &alertchannel.Email{
					SMTPServer:   "smtp.163.com:465",
					RequireTLS:   true,
					From:         "",
					AuthPassword: "",
				},
			},
			Targets: []string{""},
		},
	},
}

func TestCreateAlertRule(t *testing.T) {
	ctx := context.Background()
	op, err := setupContainerOperation(ctx)
	if err != nil {
		t.Fatal(err)
	}
	prr := &PromeAlertRulesResp{}
	if err := op.PrometheusRules(ctx, operation.PrometheusRulesOptions{Type: "alert"}, prr); err != nil {
		t.Fatal(err)
	}
	t.Log("status:", prr.Status)
	for _, data := range prr.Data.Groups {
		t.Log("name", data.Name)
		t.Log("file:", data.File)
		t.Log("interval:", data.Interval)
		for _, rule := range data.Rules {
			switch v := rule.(type) {
			case v1.RecordingRule:
				t.Log("------")
				t.Log(v)
				t.Log("------")
			case v1.AlertingRule:
				t.Log("******")
				t.Log(v)
				t.Log("******")
			default:
				t.Fatalf("unknown rule type %T", v)
			}
		}
	}
	t.Log("finished")
}
