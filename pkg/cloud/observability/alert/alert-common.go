package alert

import (
	"context"
	"net/http"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/base"
)

// 监控告警通用型接口
func (a *API) alertCommonGroup() api.Group {
	return api.
		NewGroup("").
		SubGroup(
			a.prometheusRuleTemplateGroup(),
			a.prometheusRuleTemplateGroupResource(),
			a.prometheusRuleTemplate(),
		).
		Route(
			api.POST("/testchannel").
				To(a.TestChannel).
				Operation("test alert channel connected or not").
				Param(
					api.BodyParam("alertchannel", AlertChannelTest{}),
				),
		)
}

type ChartVerions struct {
	Image    string   `json:"image"`
	Versions []string `json:"versions"`
}

type AlertChannelTest struct {
	AlertChannel `json:",inline"`
	Targets      []string `json:"targets"`
}

// TestChannel - 测试告警渠道
func (a *API) TestChannel(w http.ResponseWriter, r *http.Request) {
	channelWithTargets := &AlertChannelTest{}
	if err := api.Body(r, channelWithTargets); err != nil {
		api.Error(w, err)
		return
	}
	err := channelWithTargets.TestConnected(channelWithTargets.Targets)
	if err != nil {
		val := errors.NewBadRequest(err.Error())
		api.Raw(w, int(val.Code), val, nil)
		return
	}
	w.WriteHeader(http.StatusOK)
}

type AlertRuleTemplate struct {
	store.ObjectMeta `json:",inline"`
	Template         *PromqlGenerator `json:"template"`
}

// 模版组
type AlertRuleTemplateGroup struct {
	store.ObjectMeta `json:",inline"`
	ShowName         string `json:"templateGroupShowName"`
}

// 资源组,包含资源监控大盘
type AlertRuleTemplateResourceGroup struct {
	store.ObjectMeta `json:",inline"`
	ShowName         string `json:"resourceGroupShowName"`
}

func (a *API) prometheusRuleTemplate() api.Group {
	return api.NewGroup("/templategroups/{templategroup}/resources/{resource}/ruletemplates").
		Route(
			api.GET("").
				To(a.ListPrometheusRuleTemplate).
				Operation("list prometheus rule templates").
				Param(api.PageParams...).
				Response(store.List[AlertRuleTemplate]{}),

			api.DELETE("/{ruletemplate}").
				Operation("delete prometheus rule template").
				To(a.DeletePrometheusRuleTemplate),

			api.POST("").Operation("create rule template").
				To(a.CreatePrometheusRuleTemplate).
				Param(api.BodyParam("ruletemplate", AlertRuleTemplate{})).
				Response(AlertRuleTemplate{}),
		)
}

func (a *API) CreatePrometheusRuleTemplate(w http.ResponseWriter, r *http.Request) {
	a.OnTemplateGroupResource(w, r, func(ctx context.Context, storage store.Store) (any, error) {
		return base.GenericCreate(r, storage, &AlertRuleTemplate{})
	})
}

// ListPrometheusRuleTemplate - 获取全局通用告警模版
func (a *API) ListPrometheusRuleTemplate(w http.ResponseWriter, r *http.Request) {
	a.OnTemplateGroupResource(w, r, func(ctx context.Context, storage store.Store) (any, error) {
		return base.GenericListWithWatch(w, r, storage, &store.List[AlertRuleTemplate]{}, store.WithSort("name"))
	})
}

func (a *API) DeletePrometheusRuleTemplate(w http.ResponseWriter, r *http.Request) {
	a.OnTemplateGroupResource(w, r, func(ctx context.Context, storage store.Store) (any, error) {
		templateName := api.Path(r, "ruletemplate", "")
		if templateName == "" {
			return nil, errors.NewBadRequest("ruletemplate name is required")
		}
		if err := storage.Delete(ctx, &AlertRuleTemplate{
			ObjectMeta: store.ObjectMeta{
				Name: templateName,
			},
		}); err != nil {
			return nil, err
		}
		return nil, nil
	})
}

func (a *API) prometheusRuleTemplateGroup() api.Group {
	return api.NewGroup("/templategroups").
		Route(
			api.GET("").
				Doc("List prometheus rule template group").
				Param(api.PageParams...).
				To(a.ListPrometheusRuleTemplateGroup).
				Response(store.List[AlertRuleTemplateGroup]{}),

			api.POST("").
				Doc("Create prometheus rule template group").
				Param(api.BodyParam("templategroup", AlertRuleTemplateGroup{})).
				To(a.CreatePrometheusRuleTemplateGroup).
				Response(AlertRuleTemplateGroup{}),

			api.GET("/{templategroup}").
				Doc("get prometheus rule template group").
				To(a.GetPrometheusRuleTemplateGroup).
				Response(AlertRuleTemplateGroup{}),
		)
}

func (a *API) prometheusRuleTemplateGroupResource() api.Group {
	return api.NewGroup("/templategroups/{templategroup}/resources").
		Route(
			api.GET("").
				Doc("List prometheus rule template group resource").
				Param(api.PageParams...).
				To(a.ListPrometheusRuleTemplateGroupResource).
				Response(store.List[AlertRuleTemplateResourceGroup]{}),

			api.POST("").
				Doc("Create prometheus rule template group resource").
				Param(api.BodyParam("resource", AlertRuleTemplateResourceGroup{})).
				To(a.CreatePrometheusRuleTemplateGroupResource).
				Response(AlertRuleTemplateResourceGroup{}),

			api.GET("/{resource}").
				Doc("get prometheus rule template group resource").
				To(a.GetPrometheusRuleTemplateGroupResource).
				Response(AlertRuleTemplateResourceGroup{}),

			api.DELETE("/{resource}").
				Doc("delete prometheus rule template under group resource and this group resource").
				To(a.DeletePrometheusRuleTemplateGroupResource),
		)
}

func (a *API) ListPrometheusRuleTemplateGroupResource(w http.ResponseWriter, r *http.Request) {
	a.OnTemplateGroup(w, r, func(ctx context.Context, storage store.Store) (any, error) {
		list := &store.List[AlertRuleTemplateResourceGroup]{}
		return base.GenericList(r, storage, list)
	})
}

func (a *API) CreatePrometheusRuleTemplateGroupResource(w http.ResponseWriter, r *http.Request) {
	a.OnTemplateGroup(w, r, func(ctx context.Context, storage store.Store) (any, error) {
		return base.GenericCreate(r, storage, &AlertRuleTemplateResourceGroup{})
	})
}

func (a *API) GetPrometheusRuleTemplateGroupResource(w http.ResponseWriter, r *http.Request) {
	a.OnTemplateGroup(w, r, func(ctx context.Context, storage store.Store) (any, error) {
		resource := api.Path(r, "resource", "")
		if resource == "" {
			return nil, errors.NewBadRequest("resource name is required")
		}
		return base.GenericGet(r, storage, &AlertRuleTemplateResourceGroup{}, resource)
	})
}

func (a *API) DeletePrometheusRuleTemplateGroupResource(w http.ResponseWriter, r *http.Request) {
	a.OnTemplateGroup(w, r, func(ctx context.Context, storage store.Store) (any, error) {
		// 1.遍历该resourcegroup下所有template,并删除
		resource := api.Path(r, "resource", "")
		if resource == "" {
			return nil, errors.NewBadRequest("resource name is required")
		}
		templateStore := storage.Scope(store.Scope{Resource: "alertruletemplateresourcegroups", Name: resource})
		templates := &store.List[AlertRuleTemplate]{}
		if err := templateStore.List(ctx, templates); err != nil {
			return nil, err
		}
		for _, item := range templates.Items {
			if err := templateStore.Delete(ctx, &AlertRuleTemplate{
				ObjectMeta: store.ObjectMeta{
					Name: item.ObjectMeta.Name,
				},
			}); err != nil {
				return nil, err
			}
		}
		// 2.删除resourcegroup
		if err := storage.Delete(ctx, &AlertRuleTemplateResourceGroup{
			ObjectMeta: store.ObjectMeta{
				Name: resource,
			},
		}); err != nil {
			return nil, err
		}
		return nil, nil
	})
}

func (a *API) ListPrometheusRuleTemplateGroup(w http.ResponseWriter, r *http.Request) {
	api.On(w, r, func(ctx context.Context) (any, error) {
		list := &store.List[AlertRuleTemplateGroup]{}
		return base.GenericList(r, a.Store, list)
	})
}

func (a *API) CreatePrometheusRuleTemplateGroup(w http.ResponseWriter, r *http.Request) {
	api.On(w, r, func(ctx context.Context) (any, error) {
		return base.GenericCreate(r, a.Store, &AlertRuleTemplateGroup{})
	})
}

func (a *API) GetPrometheusRuleTemplateGroup(w http.ResponseWriter, r *http.Request) {
	api.On(w, r, func(ctx context.Context) (any, error) {
		group := api.Path(r, "templategroup", "")
		if group == "" {
			return nil, errors.NewBadRequest("resource name is required")
		}
		return base.GenericGet(r, a.Store, &AlertRuleTemplateGroup{}, group)
	})
}
