package alertchannel

import (
	"net/url"
	"testing"
	"time"
)

var teams = &MSTeams{
	URL: "",
}

func TestSendAppMSTeams(t *testing.T) {
	q := url.Values{}
	q.Add("type", string(TypeMSTeams))
	q.Add("url", teams.URL)
	now := time.Now()
	start := now.Add(-3 * time.Hour)
	alertObj := AlertProxyMessage{
		Status: "firing",
		Labels: map[string]string{
			AlertScopeTenant:      "abc",
			AlertOriginNameLabel:  "test-msteams",
			AlertScopeApplication: "mds",
			AlertClusterKey:       "test-cluster",
			"namespace":           "default",
			"pod":                 "1-nginx-7fc6f6f695-blz4d",
		},
		Annotations: map[string]string{
			AlertTemplateGroupName: "Container CPU Usage Ratio",
			AlertResource:          "pod",
			AlertActionKey:         ">=",
			AlertThreshold:         "10",
			AlertUnit:              "%",
			ValueAnnotationKey:     "20",
		},
		StartsAt: &start,
	}
	if err := teams.DoRequest(q, alertObj); err != nil {
		t.Fatal(err)
	}
	t.Log("success")
}

// 发送应用的告警恢复
func TestSendAppRecoverdMSTeams(t *testing.T) {
	q := url.Values{}
	q.Add("type", string(TypeMSTeams))
	q.Add("url", teams.URL)
	now := time.Now()
	start := now.Add(-2 * time.Hour)
	alertObj := AlertProxyMessage{
		Status: "resolved",
		Labels: map[string]string{
			AlertScopeTenant:      "abc",
			AlertOriginNameLabel:  "test-msteams",
			AlertScopeApplication: "mds",
			AlertClusterKey:       "test-cluster",
			"namespace":           "default",
			"pod":                 "1-nginx-7fc6f6f695-blz4d",
		},
		Annotations: map[string]string{
			AlertTemplateGroupName: "Container CPU Usage Ratio",
			AlertResource:          "pod",
			AlertActionKey:         ">=",
			AlertThreshold:         "10",
			AlertUnit:              "%",
			ValueAnnotationKey:     "5",
		},
		StartsAt: &start,
		EndsAt:   &now,
	}
	if err := teams.DoRequest(q, alertObj); err != nil {
		t.Fatal(err)
	}
	t.Log("success")

}

// 发送集群的告警通知
func TestSendClusterAlertMSTeams(t *testing.T) {
	q := url.Values{}
	q.Add("type", string(TypeMSTeams))
	q.Add("url", teams.URL)
	now := time.Now()
	start := now.Add(-3 * time.Hour)
	alertObj := AlertProxyMessage{
		Status: "firing",
		Labels: map[string]string{
			AlertOriginNameLabel: "test-msteams",
			ALertScopeCluster:    "my-cluster",
			AlertClusterKey:      "test-cluster",
			//"namespace":          "default",
			"node": "k8s-node1",
		},
		Annotations: map[string]string{
			AlertTemplateGroupName: "Node CPU Usage Ratio",
			AlertResource:          "node",
			AlertActionKey:         ">=",
			AlertThreshold:         "10",
			AlertUnit:              "%",
			ValueAnnotationKey:     "20",
		},
		StartsAt: &start,
	}
	if err := teams.DoRequest(q, alertObj); err != nil {
		t.Fatal(err)
	}
	t.Log("success")
}

// 发送集群的恢复通知
func TestSendClusterRecoverdMSTeams(t *testing.T) {
	q := url.Values{}
	q.Add("type", string(TypeMSTeams))
	q.Add("url", teams.URL)
	now := time.Now()
	start := now.Add(-2 * time.Hour)
	alertObj := AlertProxyMessage{
		Status: "resolved",
		Labels: map[string]string{
			AlertOriginNameLabel: "test-msteams",
			ALertScopeCluster:    "my-cluster",
			AlertClusterKey:      "test-cluster",
			"node":               "k8s-node1",
		},
		Annotations: map[string]string{
			AlertTemplateGroupName: "Node CPU Usage Ratio",
			AlertResource:          "node",
			AlertActionKey:         ">=",
			AlertThreshold:         "10",
			AlertUnit:              "%",
			ValueAnnotationKey:     "5",
		},
		StartsAt: &start,
		EndsAt:   &now,
	}
	if err := teams.DoRequest(q, alertObj); err != nil {
		t.Fatal(err)
	}
	t.Log("success")

}
