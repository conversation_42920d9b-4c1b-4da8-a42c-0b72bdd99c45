package alertchannel

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"

	"github.com/prometheus-operator/prometheus-operator/pkg/apis/monitoring/v1alpha1"
)

var _ AlertChannel = &Webhook{}

type WebHookKind string

const (
	WebHookKindClusterDefault WebHookKind = "cluster-default"
	WebHookKindUserDefined    WebHookKind = "user-defined"
)

type Webhook struct {
	Kind WebHookKind `json:"kind"`
}

func (w *Webhook) formatURL(target []string) string {
	q := url.Values{}
	q.Add("type", string(TypeWebhook))
	q.Add("kind", string(w.Kind))
	q.Add("to", strings.Join(target, ","))
	return fmt.Sprintf("http://%s?%s", InternalHttpServiceAddress, q.Encode())
}

func (w *Webhook) ToReceiver(name string, useBasicAuth bool, target []string) v1alpha1.Receiver {
	u := w.formatURL(target)
	return v1alpha1.Receiver{
		Name: name,
		WebhookConfigs: []v1alpha1.WebhookConfig{
			{
				URL:          &u,
				SendResolved: BoolPointer(true),
			},
		},
	}
}

func (w *Webhook) Check() error {
	return nil
}

func (w *Webhook) Test(alert AlertProxyWebhookAlert, target []string) error {
	buf := bytes.NewBuffer(nil)
	if err := json.NewEncoder(buf).Encode(alert); err != nil {
		return err
	}
	testCli := &http.Client{}
	for _, t := range target {
		req, err := http.NewRequest(http.MethodPost, t, buf)
		if err != nil {
			return err
		}
		resp, err := testCli.Do(req)
		if err != nil {
			return err
		}
		_, err = io.ReadAll(resp.Body)
		if err != nil {
			return err
		}
		resp.Body.Close()
	}
	return nil
}

var _ AlertProxy = &Webhook{}

// DoRequest implements AlertProxy.
func (w *Webhook) DoRequest(params url.Values, alert AlertProxyMessage) error {
	webhookKind := params.Get("kind")
	switch WebHookKind(webhookKind) {
	case WebHookKindClusterDefault:
		// do nothing,just record history
	case WebHookKindUserDefined:
		sendCli := &http.Client{}
		for _, target := range strings.Split(params.Get("to"), ",") {
			data, err := json.Marshal(alert)
			if err != nil {
				return err
			}
			req, err := http.NewRequest(http.MethodPost, target, bytes.NewBuffer(data))
			if err != nil {
				return err
			}
			resp, err := sendCli.Do(req)
			if err != nil {
				return err
			}
			_, err = io.ReadAll(resp.Body)
			if err != nil {
				return err
			}
			resp.Body.Close()
		}
	default:
		return fmt.Errorf("invalid webhook kind: %s", webhookKind)
	}
	return nil
}

// Template implements AlertProxy.
func (w *Webhook) Template() string {
	return ""
}
