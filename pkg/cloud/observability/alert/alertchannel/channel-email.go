package alertchannel

import (
	"bytes"
	"context"
	"crypto/tls"
	"fmt"
	"net"
	"net/smtp"
	"net/textproto"
	"net/url"
	"strconv"
	"strings"
	"text/template"
	"time"

	"github.com/jordan-wright/email"
	monitoringv1 "github.com/prometheus-operator/prometheus-operator/pkg/apis/monitoring/v1"
	"github.com/prometheus-operator/prometheus-operator/pkg/apis/monitoring/v1alpha1"
	v1 "k8s.io/api/core/v1"
)

var _ AlertChannel = &Email{}

type Email struct {
	SMTPServer   string `json:"smtpServer"`
	RequireTLS   bool   `json:"requireTLS"`
	From         string `json:"from"`
	AuthPassword string `json:"authPassword"`
}

func (e *Email) formatURL(target []string) string {
	q := url.Values{}
	q.Add("type", string(TypeEmail))
	q.Add("smtpServer", e.SMTPServer)
	q.Add("from", e.From)
	q.Add("to", strings.Join(target, ","))
	q.Add("authPassword", e.AuthPassword)
	q.Add("tls", strconv.FormatBool(e.RequireTLS))
	return fmt.Sprintf("http://%s?%s", InternalHttpServiceAddress, q.Encode())
}

func (e *Email) ToReceiver(name string, useBasicAuth bool, target []string) v1alpha1.Receiver {
	u := e.formatURL(target)
	httpConfig := &v1alpha1.HTTPConfig{}
	if useBasicAuth {
		httpConfig.BasicAuth = &monitoringv1.BasicAuth{
			Username: v1.SecretKeySelector{
				LocalObjectReference: v1.LocalObjectReference{
					Name: BasicAuthSecretName,
				},
				Key: BasicAuthUserName,
			},
			Password: v1.SecretKeySelector{
				LocalObjectReference: v1.LocalObjectReference{
					Name: BasicAuthSecretName,
				},
				Key: BasicAuthPassword,
			},
		}
	}
	return v1alpha1.Receiver{
		Name: name,
		WebhookConfigs: []v1alpha1.WebhookConfig{
			{
				URL:          &u,
				SendResolved: BoolPointer(true),
				HTTPConfig:   httpConfig,
			},
		},
	}
}

func (e *Email) Check() error {
	return nil
}

func (e *Email) Test(alert AlertProxyWebhookAlert, target []string) error {
	if len(alert.Alerts) == 0 {
		return fmt.Errorf("no alert")
	}
	tmpl := template.Must(template.New("email").Parse(e.Template()))
	buf := bytes.NewBuffer([]byte{})
	if err := tmpl.Execute(buf, alert.Alerts[0]); err != nil {
		return err
	}

	mail := &email.Email{
		From:    e.From,
		To:      target,
		Subject: "Bob test email",
		HTML:    buf.Bytes(),
		Headers: textproto.MIMEHeader{},
	}
	host, _, err := net.SplitHostPort(e.SMTPServer)
	if err != nil {
		return err
	}
	ctx, cancel := context.WithTimeout(context.Background(), 13*time.Second)
	defer cancel()
	done := make(chan error, 1)
	go func() {
		if e.RequireTLS {
			done <- mail.SendWithTLS(e.SMTPServer, smtp.PlainAuth("", e.From, e.AuthPassword, host),
				&tls.Config{InsecureSkipVerify: false, ServerName: host})
		} else {
			done <- mail.Send(e.SMTPServer, smtp.PlainAuth("", e.From, e.AuthPassword, host))
		}
	}()
	select {
	case err := <-done:
		if err != nil {
			return err
		}
	case <-ctx.Done():
		return fmt.Errorf("your email server is responding slowly. It is recommended to check your email configuration or email service provider status")
	}
	return nil
}

var _ AlertProxy = &Email{}

// DoRequest implements AlertProxy.
func (e *Email) DoRequest(params url.Values, alert AlertProxyMessage) error {
	smtpServer := params.Get("smtpServer")
	from := params.Get("from")
	to := params.Get("to")
	tlsStr := params.Get("tls")
	authPassword := params.Get("authPassword")
	enableTls, err := strconv.ParseBool(tlsStr)
	if err != nil {
		return err
	}
	// 生成html模版
	tmpl := template.Must(template.New("email").Parse(e.Template()))
	buf := bytes.NewBuffer([]byte{})
	if err := tmpl.Execute(buf, alert); err != nil {
		return err
	}

	mail := &email.Email{
		From:    from,
		To:      strings.Split(to, ","),
		Subject: "Bob alert",
		// Text:    data,
		HTML:    buf.Bytes(),
		Headers: textproto.MIMEHeader{},
	}
	host, _, err := net.SplitHostPort(smtpServer)
	if err != nil {
		return err
	}
	if enableTls {
		return mail.SendWithTLS(smtpServer, smtp.PlainAuth("", from, authPassword, host),
			&tls.Config{InsecureSkipVerify: false, ServerName: host})
	}
	return mail.Send(smtpServer, smtp.PlainAuth("", from, authPassword, host))
}

// Template implements AlertProxy.
func (e *Email) Template() string {
	return `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <style>
    body {
      font-family: Arial, sans-serif;
      color: #333;
      padding: 20px;
      line-height: 1.6;
      background-color: #f9f9f9;
    }
    .header, .footer {
      text-align: center;
      margin-bottom: 30px;
    }
    .header img {
      margin-top: 10px;
    }
    .content {
      background-color: #fff;
      padding: 25px;
      border-radius: 8px;
      box-shadow: 0 0 8px rgba(0,0,0,0.05);
      max-width: 600px;
      margin: 0 auto;
    }
    h2 {
      color: #0056b3;
    }
    a {
      color: #007bff;
      text-decoration: none;
    }
    .ticket-info {
      margin-top: 20px;
    }
    .ticket-info strong {
      display: inline-block;
      width: 120px;
    }
    .footer {
      font-size: 12px;
      color: #888;
    }
  </style>
</head>
<body>
  <div class="header">
    <h3>Majnoon ISMC Group</h3>
    <img src="https://kubegems.oss-cn-chengdu.aliyuncs.com/kubegems.io/ismc.png" alt="Casdoor Logo" width="200">
  </div>
  <div class="content">
	<div class="email-container">
        <div class="email-header">
            <h1>Alert Notification</h1>
        </div>
        <div class="email-body">
            <div class="alert">
                <p><strong>Status:</strong> {{ .Status }}</p>
                <p><strong>Labels:</strong></p>
                <ul>
                    {{ range $key, $value := .Labels }}
                    <li>{{ $key }}: {{ $value }}</li>
                    {{ end }}
                </ul>
                <p><strong>Annotations:</strong></p>
                <ul>
                    {{ range $key, $value := .Annotations }}
						{{ if ne $key "value" }}
                    		<li>{{ $key }}: {{ $value }}</li>
						{{ end }}
                    {{ end }}
                </ul>
				<p><strong>CurrentValue:</strong> {{ .Annotations.value }}</p>
                <p><strong>Starts At:</strong> {{ .StartsAt }}</p>
            </div>
        </div>
    	</div>
  </div>

  <div class="footer">
    <p>ISMC is a brand operated by Majnoon organization. For more info, please visit <a href="https://xiaoshiai.cn">xiaoshiai.cn</a></p>
  </div>
</body>
</html>`
}
