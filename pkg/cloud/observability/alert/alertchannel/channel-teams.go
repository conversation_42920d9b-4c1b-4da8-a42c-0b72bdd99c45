package alertchannel

import (
	"bytes"
	"fmt"
	"html/template"
	"io"
	"net/http"
	"net/url"
	"strings"

	"github.com/prometheus-operator/prometheus-operator/pkg/apis/monitoring/v1alpha1"
)

// MSTeams implements the AlertChannel interface for sending alerts via Microsoft Teams.
// It is used to ensure that the MSTeams type satisfies the AlertChannel interface at compile time.
var _ AlertChannel = &MSTeams{}

// MSTeams implements the AlertProxy interface for sending alerts to Microsoft Teams.
// It is used to ensure that the MSTeams type satisfies the AlertProxy interface at compile time.
var _ AlertProxy = &MSTeams{}

type MSTeams struct {
	URL string `json:"url"`
}

func (m *MSTeams) formatURL(target []string) string {
	q := url.Values{}
	q.Add("type", string(TypeMSTeams))
	q.Add("url", m.URL)
	return fmt.Sprintf("http://%s?%s", InternalHttpServiceAddress, q.Encode())
}

// ToReceiver implements AlertChannel.
func (m *MSTeams) ToReceiver(name string, useBasic<PERSON>uth bool, target []string) v1alpha1.Receiver {
	u := m.formatURL(target)
	httpConfig := &v1alpha1.HTTPConfig{}
	return v1alpha1.Receiver{
		Name: name,
		WebhookConfigs: []v1alpha1.WebhookConfig{
			{
				URL:          &u,
				SendResolved: BoolPointer(true),
				HTTPConfig:   httpConfig,
			},
		},
	}
}

// Check implements AlertChannel.
func (m *MSTeams) Check() error {
	if !strings.Contains(m.URL, "webhook.office.com") {
		return fmt.Errorf("teams robot url not valid")
	}
	return nil
}

func (m *MSTeams) DoRequest(params url.Values, alert AlertProxyMessage) error {
	obj := struct {
		Notify NotifyTemplate
	}{
		Notify: alert.GetCommonNotifyTemplate(),
	}
	tmpl := template.Must(template.New("teams").Parse(m.Template()))
	buf := bytes.NewBuffer([]byte{})
	if err := tmpl.Execute(buf, obj); err != nil {
		return err
	}
	req, err := http.NewRequest(http.MethodPost, params.Get("url"), buf)
	if err != nil {
		return err
	}
	req.Header.Set("Content-Type", "application/json")
	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	bodyBytes, _ := io.ReadAll(resp.Body)
	if resp.StatusCode != http.StatusOK || string(bodyBytes) != "1" {
		return fmt.Errorf("http request failed with info:%s", string(bodyBytes))
	}
	return nil
}

func (m *MSTeams) Template() string {
	return `
{
  "type":"message",
  "attachments": [
    {
      "contentType":"application/vnd.microsoft.card.adaptive",
      "contentUrl":null,
      "content": {
        "type": "AdaptiveCard",
        "$schema": "http://adaptivecards.io/schemas/adaptive-card.json",
        "version": "1.6",
        "msteams": {
          "width": "full"
        },
        "body": [
		  {
            "type": "Container",
            "style": "emphasis",
            "items": [
              {
                "type": "TextBlock",
                "size": "Large",
                "weight": "Bolder",
				"text": "[BOB Observability] - {{ .Notify.TemplateShowName }} {{ .Notify.Action }} {{ .Notify.Threshold }}{{ .Notify.Unit }}",
				"color": "{{ if eq .Notify.Status "firing" }}warning{{ else }}good{{ end }}"
              }
            ]
          },
          {
            "type": "Container",
            "items": [
				{
				  "type": "TextBlock",
                  "text": "The alarm for the BOB platform of your account ({{ if ne .Notify.TenantID "" }}Account ID:{{ .Notify.TenantID }}{{ else }}Cluster ID:{{ .Notify.ClusterID }}{{ end }}) has been {{ if eq .Notify.Status "firing" }}triggered{{ else }}recovery{{ end }}",
                  "wrap": true
				},
				{
				  "type": "TextBlock",
                  "text": "\n",
                  "wrap": true
				},
				{
				  "type": "TextBlock",
                  "text": "Current: {{ .Notify.CurrentValue }}{{ .Notify.Unit }}({{ .Notify.TemplateShowName }})",
                  "wrap": true
				},
				{
				  "type": "TextBlock",
                  "text": "Alert Rule: {{ .Notify.AlertName }}",
                  "wrap": true
				},
				{
				  "type": "TextBlock",
                  "text": "Alert Object: {{ .Notify.AlertObject }} | {{ .Notify.Resource }} | {{ .Notify.Cluster }} {{ if ne .Notify.Namespace "" }}| {{ .Notify.Namespace }}{{ end }}",
                  "wrap": true
				},
				{
				  "type": "TextBlock",
                  "text": "\n",
                  "wrap": true
				},
            	{
				  "type": "TextBlock",
                  "text": "Trigger Time: {{ .Notify.StartTime  }}",
                  "wrap": true
				},
				{
				  "type": "TextBlock",
                  "text": "Duration: {{ .Notify.KeepTime }}",
                  "wrap": true
				}{{ if ne .Notify.ResovedTime "" }},
				{
				  "type": "TextBlock",
                  "text": "Recovery Time: {{ .Notify.ResovedTime }}",
                  "wrap": true
				}{{ end }},
				{
				  "type": "TextBlock",
                  "text": "You can login to the BOB Platform  to view the detailed alert",
                  "wrap": true
				}
            ]
          }
        ],
      }
    }
  ]
}
	`
}

// Test implements AlertChannel.
func (m *MSTeams) Test(alert AlertProxyWebhookAlert, target []string) error {
	q := url.Values{}
	q.Add("type", string(TypeMSTeams))
	q.Add("url", m.URL)
	// q.Add("at", strings.Join(target, ","))
	for _, al := range alert.Alerts {
		if err := m.DoRequest(q, al); err != nil {
			return err
		}
	}
	return nil
}
