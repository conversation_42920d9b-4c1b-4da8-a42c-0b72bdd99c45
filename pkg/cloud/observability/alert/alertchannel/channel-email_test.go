package alertchannel

import (
	"encoding/base64"
	"fmt"
	"io"
	"net/http"
	"testing"
	"time"
)

// 网易邮箱AuthPassword使用授权码
func TestSendEmail(t *testing.T) {
	email := &Email{
		SMTPServer:   "smtp.feishu.cn:465",
		RequireTLS:   true,
		From:         "",
		AuthPassword: "",
	}
	now := time.Now()
	alertObj := AlertProxyWebhookAlert{
		Receiver: "abc",
		Status:   "firing",
		Alerts: []AlertProxyMessage{
			{
				Status: "firing",
				Labels: map[string]string{
					AlertNameLabel:      "bob-test-alert",
					SeverityLabel:       SeverityError,
					AlertNamespaceLabel: "bob-test-namespace",
				},
				Annotations: map[string]string{
					MessageAnnotationsKey: "bob test alert message",
					ValueAnnotationKey:    "0",
				},
				StartsAt: &now,
			},
		},
	}
	if err := email.Test(alertObj, nil); err != nil {
		t.Fatal(err)
	}
	t.Log("success")

}

func TestConnectCenter(t *testing.T) {
	client := &http.Client{}
	req, err := http.NewRequest("GET", "http://bob.develop.xiaoshiai.cn:30000/v1/alertcommon/dashboards", nil)
	if err != nil {
		t.Fatal(err)
	}
	username, password := "", ""
	// 设置基本认证的 Authorization 头
	auth := fmt.Sprintf("%s:%s", username, password)
	req.Header.Set("Authorization", "Basic "+base64.StdEncoding.EncodeToString([]byte(auth)))
	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		t.Fatal("Error sending request:", err)
	}
	defer resp.Body.Close()

	// 处理响应
	if resp.StatusCode == http.StatusOK {
		t.Log("Request succeeded!")
	} else {
		t.Log("Request failed with status:", resp.Status)
		return
	}
	b, _ := io.ReadAll(resp.Body)
	t.Log(string(b))
}
