package tracing

import (
	"context"
	"net/http"

	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/base"
	"xiaoshiai.cn/rune/pkg/cloud/cluster"
	"xiaoshiai.cn/rune/pkg/cloud/cluster/resources"
)

type API struct {
	Store     store.Store
	CloudInfo cluster.CloudInfoGetter
}

func NewAPI(store store.Store, cloudinfo cluster.CloudInfoGetter) *API {
	return &API{
		Store:     store,
		CloudInfo: cloudinfo,
	}
}

func (a *API) ApplicationTracing(w http.ResponseWriter, r *http.Request) {
	cluster.OnClusterInfo(w, r, a.CloudInfo, func(ctx context.Context, kubes *cluster.KubernetesClients) (any, error) {
		cliconfig, err := kubes.GetServiceProxyConfig("jaeger", resources.IsWebsocketRequest(r))
		if err != nil {
			return nil, err
		}
		meta := resources.ResourceMetaFromRequest(r)

		return resources.ClusterProxyHandle(w, r, cliconfig, cliconfig.Server.Path, "/"+meta.Subresource)
	})
}

func (a *API) ApplicationOpenTelementry(w http.ResponseWriter, r *http.Request) {
	cluster.OnClusterInfo(w, r, a.CloudInfo, func(ctx context.Context, kubes *cluster.KubernetesClients) (any, error) {
		cliconfig, err := kubes.GetServiceProxyConfig("otel", resources.IsWebsocketRequest(r))
		if err != nil {
			return nil, err
		}
		meta := resources.ResourceMetaFromRequest(r)
		return resources.ClusterProxyHandle(w, r, cliconfig, cliconfig.Server.Path, "/"+meta.Subresource)
	})
}

func (a *API) Group() api.Group {
	return api.
		NewGroup("").
		Tag("Tracing").
		SubGroup(
			base.
				NewInstanceGroup().
				SubGroup(
					api.
						NewGroup("/tracing").
						Route(
							api.GET("").To(resources.Redirect).Doc("Redirect to the tracing manager"),
							api.Any("/{path}*").To(a.ApplicationTracing).Doc("Redirect to the tracing manager"),
						),
					api.
						NewGroup("otel").
						Route(
							api.GET("").To(resources.Redirect).Doc("Redirect to the opentelemetry"),
							api.Any("/{path}*").To(a.ApplicationOpenTelementry).Doc("Redirect to the opentelemetry"),
						),
				),
		)
}
