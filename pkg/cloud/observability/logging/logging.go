package logging

import (
	"context"
	"fmt"
	"net/http"
	"slices"
	"strconv"
	"time"

	"github.com/gorilla/websocket"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/base"
	"xiaoshiai.cn/rune/pkg/cloud/cluster"
	"xiaoshiai.cn/rune/pkg/cloud/cluster/operation"
	"xiaoshiai.cn/rune/pkg/cloud/workspace"
)

type API struct {
	CloudInfo cluster.CloudInfoGetter
	Store     store.Store
}

func NewAPI(store store.Store, cloudinfo cluster.CloudInfoGetter) *API {
	return &API{
		CloudInfo: cloudinfo,
		Store:     store,
	}
}

func (a *API) InstanceLogging(w http.ResponseWriter, r *http.Request) {
	base.OnInstance(w, r, func(ctx context.Context, tenant, workspacename, instance string) (any, error) {
		kubes, namespace, err := workspace.GetWorkspaceCluster(ctx, a.CloudInfo, a.Store, tenant, workspacename)
		if err != nil {
			return nil, err
		}
		op := operation.NewContainerOperation(kubes)

		options := operation.LokiQueryRangeOptions{
			Query:     getLokiQuery(r, namespace, instance),
			Start:     parseNanoTime(api.Query(r, "start", "")),
			End:       parseNanoTime(api.Query(r, "end", "")),
			Since:     api.Query(r, "since", time.Duration(0)),
			Step:      api.Query(r, "step", time.Duration(0)),
			Limit:     api.Query(r, "limit", 0),
			Direction: api.Query(r, "direction", "backward"),
			Interval:  api.Query(r, "interval", time.Duration(0)),
		}
		log.FromContext(ctx).V(5).Info("loki query", "query", options.Query)
		data, err := op.LokiQueryRange(ctx, options)
		if err != nil {
			return nil, err
		}
		return ConvertLokiResponse(data, options.Direction), nil
	})
}

func parseNanoTime(s string) time.Time {
	if len(s) == 0 {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC3339Nano, s)
	if err != nil {
		// try rfc3339
		t, err = time.Parse(time.RFC3339, s)
		if err != nil {
			return time.Time{}
		}
	}
	return t
}

func (a *API) InstanceLoggingStream(w http.ResponseWriter, r *http.Request) {
	base.OnInstance(w, r, func(ctx context.Context, tenant, workspacename, app string) (any, error) {
		kubes, namespace, err := workspace.GetWorkspaceCluster(ctx, a.CloudInfo, a.Store, tenant, workspacename)
		if err != nil {
			return nil, err
		}
		op := operation.NewContainerOperation(kubes)

		log := log.FromContext(ctx)

		options := operation.LokiTailOptions{
			Query:    getLokiQuery(r, namespace, app),
			Start:    parseNanoTime(api.Query(r, "start", "")),
			DelayFor: api.Query(r, "delay_for", time.Duration(0)),
			Limit:    api.Query(r, "limit", 100),
		}
		log.V(5).Info("loki query", "query", options.Query)

		up := websocket.Upgrader{CheckOrigin: func(r *http.Request) bool { return true }}
		conn, err := up.Upgrade(w, r, nil)
		if err != nil {
			return nil, err
		}
		defer conn.Close()

		data, err := op.LokiTail(ctx, options, func(ctx context.Context, msg operation.LokiQueryRangeResult) error {
			for _, m := range ConvertLokiValues(msg) {
				return conn.WriteJSON(m)
			}
			return nil
		})
		if err != nil {
			log.Error(err, "loki tail error")
			return nil, nil
		}
		_ = data
		return nil, nil
	})
}

type LogResponse []LogItem

type LogItem struct {
	Timestamp time.Time         `json:"timestamp"`
	Labels    map[string]string `json:"labels"`
	Log       string            `json:"log"`
}

func ConvertLokiResponse(data *operation.LokiQueryRangeResponse, direction string) LogResponse {
	logs := []LogItem{}
	for i := range data.Data.Result {
		logs = append(logs, ConvertLokiValues(data.Data.Result[i])...)
	}
	switch direction {
	case "forward":
		slices.SortFunc(logs, func(i, j LogItem) int {
			return i.Timestamp.Compare(j.Timestamp)
		})
	case "backward":
		slices.SortFunc(logs, func(i, j LogItem) int {
			return j.Timestamp.Compare(i.Timestamp)
		})
	}
	return LogResponse(logs)
}

func ConvertLokiValues(data operation.LokiQueryRangeResult) []LogItem {
	var logs []LogItem
	for _, values := range data.Values {
		if len(values) != 2 {
			continue
		}
		unixnano, _ := strconv.ParseInt(values[0], 10, 64)
		logs = append(logs, LogItem{Timestamp: time.Unix(0, unixnano), Labels: data.Stream, Log: values[1]})
	}
	return logs
}

func (a *API) InstanceLoggingSeries(w http.ResponseWriter, r *http.Request) {
	base.OnInstance(w, r, func(ctx context.Context, tenant, workspacename, app string) (any, error) {
		kubes, namespace, err := workspace.GetWorkspaceCluster(ctx, a.CloudInfo, a.Store, tenant, workspacename)
		if err != nil {
			return nil, err
		}
		op := operation.NewContainerOperation(kubes)

		log := log.FromContext(ctx)
		options := operation.LokiSeriesOptions{
			Match: []string{getLokiQuery(r, namespace, app)},
			Start: api.Query(r, "start", time.Time{}),
			End:   api.Query(r, "end", time.Time{}),
			Since: api.Query(r, "since", time.Duration(0)),
		}
		log.V(5).Info("loki series", "match", options.Match)
		data, err := op.LokiSeries(ctx, options)
		if err != nil {
			return nil, err
		}
		return data.Data, nil
	})
}

func getLokiQuery(r *http.Request, namespace, instance string) string {
	cond := fmt.Sprintf(`namespace="%s",instance="%s"`, namespace, instance)
	if filter := api.Query(r, "filter", ""); filter != "" {
		cond += ", " + filter
	}
	query := "{" + cond + "}"
	query += api.Query(r, "query", "")
	return query
}

func (a *API) InstanceGroup() api.Group {
	return api.
		NewGroup("/log").
		Param(
			api.QueryParam("filter", "additional filter expression,exmaple: foo=\"bar\", level=\"info\"").Optional(),
		).
		Route(
			api.GET("").
				Doc("Tenant application logging").
				To(a.InstanceLogging).
				Response(LogResponse{}).
				Param(
					api.QueryParam("start", "rfc3339nano timestamp,start time for the query").Optional(),
					api.QueryParam("end", "rfc3339nano timestamp,end time for the query").Optional(),
					api.QueryParam("since", "duration,query start relative to end").Optional(),
					api.QueryParam("step", "duration,query resolution step width").Optional(),
					api.QueryParam("interval", "duration,only return entries at (or greater than) the specified interval").Optional(),
					api.QueryParam("direction", "string,sort order of logs,forward or backward").Optional(),
					api.QueryParam("filter", "filter expression,exmaple: foo=bar, level=info").Optional(),
				),
			api.GET("/stream").
				Doc("Tenant application logging stream").
				To(a.InstanceLoggingStream).
				Param(
					api.QueryParam("start", "rfc3339nano timestamp,start time for the query").Optional(),
					api.QueryParam("delay_for", "duration,delay for the query").Optional(),
					api.QueryParam("limit", "int,limit the number of logs").Optional(),
				),
			api.GET("/series").
				Doc("Tenant application logging series").
				To(a.InstanceLoggingSeries).
				Param(
					api.QueryParam("start", "rfc3339 timestamp,start time for the query").Optional(),
					api.QueryParam("end", "rfc3339 timestamp,end time for the query").Optional(),
					api.QueryParam("since", "duration,query start relative to end").Optional(),
				),
		)
}

func (a *API) Group() api.Group {
	return api.
		NewGroup("").
		Tag("Logging").
		SubGroup(
			base.NewInstanceGroup().
				SubGroup(
					a.InstanceGroup(),
				),
		)
}
