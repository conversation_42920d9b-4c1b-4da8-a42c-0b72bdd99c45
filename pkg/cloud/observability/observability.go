package observability

import (
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/cloud/cluster"
	"xiaoshiai.cn/rune/pkg/cloud/observability/alert"
	"xiaoshiai.cn/rune/pkg/cloud/observability/logging"
	"xiaoshiai.cn/rune/pkg/cloud/observability/monitoring"
	"xiaoshiai.cn/rune/pkg/cloud/observability/monitoring/dashboard"
	"xiaoshiai.cn/rune/pkg/cloud/observability/tracing"
)

func NewAPI(store store.Store, mongoStore store.Store, infos cluster.CloudInfoGetter, dashboards map[string]dashboard.DashboardConfiguration) *API {
	return &API{
		Tracing:    tracing.NewAPI(store, infos),
		Monitoring: monitoring.NewAPI(store, infos, dashboards),
		Logging:    logging.NewAPI(store, infos),
		Alert:      alert.NewAPI(store, infos),
	}
}

type API struct {
	Tracing    *tracing.API
	Monitoring *monitoring.API
	Logging    *logging.API
	Alert      *alert.API
}

func (a *API) Group() api.Group {
	return api.
		NewGroup("").
		Tag("Observability").
		SubGroup(
			a.Logging.Group(),
			a.Monitoring.Group(),
			a.Tracing.Group(),
			a.Alert.Group(),
		)
}
