package cost

import (
	"context"
	"fmt"
	"regexp"
	"slices"
	"strings"
	"time"

	"golang.org/x/sync/errgroup"
	"xiaoshiai.cn/common/collections"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/rand"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/common/store/mongo"
	"xiaoshiai.cn/rune/pkg/base"
	"xiaoshiai.cn/rune/pkg/cloud/cluster"
	"xiaoshiai.cn/rune/pkg/cloud/observability/monitoring/prometheus"
	"xiaoshiai.cn/rune/pkg/iam/tenant"
	"xiaoshiai.cn/rune/pkg/pay/billing"
	"xiaoshiai.cn/rune/pkg/pay/cost"
	"xiaoshiai.cn/rune/pkg/pay/product"
	"xiaoshiai.cn/rune/pkg/pay/types"
	"xiaoshiai.cn/rune/pkg/pay/utils"
)

const ResoucesRound = 6

func init() {
	mongo.GlobalObjectsScheme.Register(&ResourceCountCycle{}, mongo.ObjectDefination{
		Uniques: []mongo.UnionFields{
			{"name"},
		},
		Indexes: []mongo.UnionFields{
			{"from", "to"},
		},
	})
	mongo.GlobalObjectsScheme.Register(&MeasurementPolicy{}, mongo.ObjectDefination{
		Uniques: []mongo.UnionFields{
			{"name"},
		},
		Indexes: []mongo.UnionFields{
			{"period"},
			{"clusterCategory"},
		},
	})
}

type ResourceCountCycle struct {
	store.ObjectMeta `json:",inline"`
	From             time.Time `json:"from,omitempty"`
	To               time.Time `json:"to,omitempty"`
}

const (
	MeasurementContainerCPU          = "cpu"
	MeasurementContainerMemory       = "memory"
	MeasurementContainerStorage      = "storage"
	MeasurementIngressRx             = "ingress_rx"
	MeasurementIngressTx             = "ingress_tx"
	MeasurementPodNetworkRx          = "pod_network_rx"
	MeasurementPodNetworkTx          = "pod_network_tx"
	MeasurementVirtualMachineCPU     = "vcpu"
	MeasurementVirtualMachineMemory  = "vmemory"
	MeasurementVirtualMachineStorage = "disk"
)

type ResourceUnit string

const (
	ResourceUnitCorePerHour ResourceUnit = "Core/h"
	ResourceUnitGBPerHour   ResourceUnit = "GB/h"
	ResourceUnitGB          ResourceUnit = "GB"
)

type MeasurementPolicy struct {
	store.ObjectMeta   `json:",inline"`
	Period             cost.ResourcePeriod `json:"period,omitempty"`
	Unit               ResourceUnit        `json:"unit,omitempty"`
	Query              string              `json:"query,omitempty"`
	MatchClusterLabels map[string]string   `json:"matchClusterLabels,omitempty"`
	Disabled           bool                `json:"disabled,omitempty"`
}

func ResourcePeriodToDuration(period cost.ResourcePeriod) time.Duration {
	switch period {
	case cost.ResourcePeriodHour:
		return time.Hour
	case cost.ResourcePeriodDay:
		return time.Hour * 24
	case cost.ResourcePeriodWeek:
		return time.Hour * 24 * 7
	case cost.ResourcePeriodMonth:
		return time.Hour * 24 * 30
	default:
		return 0
	}
}

func InitMeasurementPolicies(ctx context.Context, mongo store.Store) error {
	resources := []MeasurementPolicy{
		{
			ObjectMeta: store.ObjectMeta{
				Name:        MeasurementContainerCPU,
				Description: "CPU",
			},
			Period: cost.ResourcePeriodHour,
			Unit:   ResourceUnitCorePerHour,
			Query:  `sum(sum_over_time((kube_pod_container_resource_limits{resource="cpu"} * on (container, pod, namespace) group_left () kube_pod_container_status_running* on (namespace, pod) group_left (label_app_kubernetes_io_instance) kube_pod_labels{label_app_kubernetes_io_instance!=""})[1h:1m])/61) by (pod,label_app_kubernetes_io_instance, namespace)`,
		},
		{
			ObjectMeta: store.ObjectMeta{
				Name:        MeasurementContainerMemory,
				Description: "Memory",
			},
			Period: cost.ResourcePeriodHour,
			Unit:   ResourceUnitGBPerHour,
			Query:  `sum(sum_over_time((kube_pod_container_resource_limits{resource="memory"} * on (container, pod, namespace) group_left () kube_pod_container_status_running* on (namespace, pod) group_left (label_app_kubernetes_io_instance) kube_pod_labels{label_app_kubernetes_io_instance!=""})[1h:1m])/61) by (pod,label_app_kubernetes_io_instance, namespace)/ 1073741824`,
		},
		{
			ObjectMeta: store.ObjectMeta{
				Name:        MeasurementContainerStorage,
				Description: "Storage",
			},
			Period: cost.ResourcePeriodHour,
			Query:  `sum_over_time((kube_persistentvolumeclaim_resource_requests_storage_bytes{}* on (namespace, persistentvolumeclaim) group_left (label_app_kubernetes_io_instance) kube_persistentvolumeclaim_labels{label_app_kubernetes_io_instance != ""} * on (namespace, persistentvolumeclaim) group_left(storageclass) kube_persistentvolumeclaim_info)[1h:1m]) / 61  / 1073741824`,
			Unit:   ResourceUnitGBPerHour,
		},
		{
			ObjectMeta: store.ObjectMeta{
				Name:        MeasurementIngressRx,
				Description: "Ingress receive traffic",
			},
			Period: cost.ResourcePeriodHour,
			Query:  `label_replace(sum(idelta(nginx_ingress_controller_request_size_count{}[1h]) / 1073741824) by (exported_namespace, ingress, controller_class), "namespace", "$1", "exported_namespace", "(.*)") * on (ingress, namespace) group_left(label_app_kubernetes_io_instance) kube_ingress_labels{label_app_kubernetes_io_instance != ""}`,
			Unit:   ResourceUnitGB,
		},
		{
			ObjectMeta: store.ObjectMeta{
				Name:        MeasurementIngressTx,
				Description: "Ingress send traffic",
			},
			Period: cost.ResourcePeriodHour,
			Query:  `label_replace(sum(idelta(nginx_ingress_controller_response_size_count{}[1h]) / 1073741824) by (exported_namespace, ingress, controller_class), "namespace", "$1", "exported_namespace", "(.*)") * on (ingress, namespace) group_left(label_app_kubernetes_io_instance) kube_ingress_labels{label_app_kubernetes_io_instance != ""}`,
			Unit:   ResourceUnitGB,
		},
		{
			ObjectMeta: store.ObjectMeta{
				Name:        MeasurementVirtualMachineCPU,
				Description: "Virtual Machine CPU",
			},
			Period: cost.ResourcePeriodHour,
			Query:  `sum(sum_over_time((ismc_vm_cpu_total_cores{resourcegroup!=""} * on(resourcegroup,vm) group_left() ismc_vm_up{})[1h:1m])/61) by (name, vm, resourcegroup, app)`,
			Unit:   ResourceUnitCorePerHour,
		},
		{
			ObjectMeta: store.ObjectMeta{
				Name:        MeasurementVirtualMachineMemory,
				Description: "Virtual Machine Memory",
			},
			Period: cost.ResourcePeriodHour,
			Query:  `sum(sum_over_time((ismc_vm_memory_total_bytes{resourcegroup!=""} * on(resourcegroup,vm) group_left() ismc_vm_up{})[1h:1m])/61) by (name, vm, resourcegroup, app) / 1073741824`,
			Unit:   ResourceUnitGBPerHour,
		},
		{
			ObjectMeta: store.ObjectMeta{
				Name:        MeasurementVirtualMachineStorage,
				Description: "Virtual Machine Disk",
			},
			Period: cost.ResourcePeriodHour,
			Query:  `sum(sum_over_time(ismc_disk_total_bytes{resourcegroup!=""}[1h:1m])/61) by (name, disk, resourcegroup, app) / 1073741824`,
			Unit:   ResourceUnitGBPerHour,
		},
	}
	var errs []error
	for _, resource := range resources {
		if err := store.CreateIfNotExists(ctx, mongo, &resource); err != nil {
			errs = append(errs, err)
		}
	}
	return errors.NewAggregate(errs)
}

type ClustersResourceUsageItem struct {
	MeasurementName string
	Cluster         store.ObjectReference
	ClusterLabels   map[string]string
	Items           []UsageItem
}

type UsageItem struct {
	Labels map[string]string `json:"labels,omitempty"`
	Value  float64           `json:"value,omitempty"`
}

type ResourceCostCounter struct {
	Cloudinfo      cluster.CloudInfoGetter
	Storage        store.Store
	Options        *Options
	Mongo          store.Store
	BillingService billing.BillingService
}

type Options struct {
	MaxRecoveryDuration time.Duration `json:"maxRecoveryDuration,omitempty"`
}

func NewDefaultOptions() *Options {
	return &Options{
		MaxRecoveryDuration: time.Hour,
	}
}

func NewResourceCostCounter(storage store.Store, mongo store.Store, infos cluster.CloudInfoGetter, billlingsvc billing.BillingService, options *Options) *ResourceCostCounter {
	return &ResourceCostCounter{
		Storage:        storage,
		Mongo:          mongo,
		Cloudinfo:      infos,
		Options:        options,
		BillingService: billlingsvc,
	}
}

func (s *ResourceCostCounter) Name() string {
	return "ResourceCostCounter"
}

func (s *ResourceCostCounter) Run(ctx context.Context) error {
	log := log.FromContext(ctx)

	go s.recoveryIfNeeded(ctx)

	runner := utils.ScheduleRunner{
		RunEveryHourFunc: func(ctx context.Context) error {
			log.Info("running resource cost calculation every hour")
			return s.DoNow(ctx, cost.ResourcePeriodHour)
		},
		RunEveryDayFunc: func(ctx context.Context) error {
			log.Info("running resource cost calculation every day")
			return s.DoNow(ctx, cost.ResourcePeriodDay)
		},
		RunEveryWeekFunc: func(ctx context.Context) error {
			log.Info("running resource cost calculation every week")
			return s.DoNow(ctx, cost.ResourcePeriodWeek)
		},
		RunEveryMonthFunc: func(ctx context.Context) error {
			log.Info("running resource cost calculation every month")
			return s.DoNow(ctx, cost.ResourcePeriodMonth)
		},
	}
	return runner.Run(ctx)
}

func (s *ResourceCostCounter) recoveryIfNeeded(ctx context.Context) error {
	eg := errgroup.Group{}
	eg.Go(func() error {
		return s.recoveryIfNeededPeriod(ctx, cost.ResourcePeriodHour)
	})
	eg.Go(func() error {
		return s.recoveryIfNeededPeriod(ctx, cost.ResourcePeriodDay)
	})
	eg.Go(func() error {
		return s.recoveryIfNeededPeriod(ctx, cost.ResourcePeriodWeek)
	})
	eg.Go(func() error {
		return s.recoveryIfNeededPeriod(ctx, cost.ResourcePeriodMonth)
	})
	return eg.Wait()
}

func (s *ResourceCostCounter) recoveryIfNeededPeriod(ctx context.Context, period cost.ResourcePeriod) error {
	log := log.FromContext(ctx)
	log.Info("recovery resource cost calculation", "period", period)

	for _, cycle := range utils.CalcHistoryCyclesFromNow(s.Options.MaxRecoveryDuration, ResourcePeriodToDuration(period)) {
		// check if already calculated
		if err := s.Mongo.Get(ctx, cycle.Key(), &ResourceCountCycle{}); err != nil {
			if !errors.IsNotFound(err) {
				log.Error(err, "failed to get resource count cycle", "cycle", cycle)
				continue
			}
			// recovery
		} else {
			continue
		}
		log.Info("recovery resource cost calculation", "cycle", cycle)
		if err := s.DoCycel(ctx, cycle, period); err != nil {
			return err
		}
	}
	return nil
}

func (s *ResourceCostCounter) DoNow(ctx context.Context, period cost.ResourcePeriod) error {
	resolution := ResourcePeriodToDuration(period)
	now := time.Now().Truncate(resolution)
	cycel := utils.Cycle{From: now.Add(-resolution), To: now}
	return s.DoCycel(ctx, cycel, period)
}

func (s *ResourceCostCounter) DoCycel(ctx context.Context, cycel utils.Cycle, period cost.ResourcePeriod) error {
	log := log.FromContext(ctx)
	log.Info("running resource cost calculation", "cycel", cycel)

	from, to := cycel.From, cycel.To

	usages, err := s.CollectAllCluster(ctx, cycel)
	if err != nil {
		return err
	}
	log.Info("collected resource usage", "from", from, "to", to, "count", len(usages.Items))
	countCycle := &ResourceCountCycle{
		ObjectMeta: store.ObjectMeta{Name: utils.Cycle{From: from, To: to}.Key()},
		From:       from,
		To:         to,
	}
	if err := s.Mongo.Create(ctx, countCycle); err != nil {
		if errors.IsAlreadyExists(err) {
			log.Info("resource count cycle already exists", "countCycle", countCycle)
			return nil
		}
		log.Error(err, "failed to create resource count cycle", "countCycle", countCycle)
		return err
	}
	classified, err := s.ClassifyCost(ctx, usages)
	if err != nil {
		return err
	}
	return s.GenerateCostBilling(ctx, classified)
}

type ClustersResourceUsage struct {
	From  time.Time
	To    time.Time
	Items []ClustersResourceUsageItem
}

func (s *ResourceCostCounter) CollectAllCluster(ctx context.Context, cycel utils.Cycle) (ClustersResourceUsage, error) {
	log := log.FromContext(ctx)

	from, to := cycel.From, cycel.To

	clusterlist := &store.List[cluster.Cluster]{}
	// only list global clusters
	if err := s.Storage.List(ctx, clusterlist); err != nil {
		return ClustersResourceUsage{}, err
	}
	allcosts := collections.SafeSlice[ClustersResourceUsageItem]{}
	eg := errgroup.Group{}
	// every cluster
	for _, cluster := range clusterlist.Items {
		ref := store.ObjectReferenceFrom(&cluster)
		usages, err := s.CollectAllMeasurements(ctx, ref, from, to)
		if err != nil {
			log.Error(err, "failed to list supported resources", "cluster", ref.String())
			continue
		}
		allcosts.Append(usages...)
	}
	eg.Wait()
	items := allcosts.Get()

	return ClustersResourceUsage{From: from, To: to, Items: items}, nil
}

func (r *ResourceCostCounter) CollectAllMeasurements(ctx context.Context, ref store.ObjectReference, from, to time.Time) ([]ClustersResourceUsageItem, error) {
	clusteritem := &cluster.Cluster{}
	if err := r.Storage.Scope(ref.Scopes...).Get(ctx, ref.Name, clusteritem); err != nil {
		return nil, err
	}

	info, err := r.Cloudinfo.Get(ctx, ref)
	if err != nil {
		return nil, err
	}

	policiylist := store.List[MeasurementPolicy]{}
	if err := r.Mongo.List(ctx, &policiylist); err != nil {
		return nil, err
	}
	allcosts := collections.SafeSlice[ClustersResourceUsageItem]{}
	eg := errgroup.Group{}
	for _, policy := range policiylist.Items {

		// cluster labels not match
		if !matchLabels(policy.MatchClusterLabels, clusteritem.Labels) {
			continue
		}
		config := policy
		eg.Go(func() error {
			log.Info("getting resource usage", "measurement policy", policy.Name, "from", from, "to", to)
			items, err := r.getUsage(ctx, info, config.Query, from, to)
			if err != nil {
				log.Error(err, "failed to get resource usage", "measurement policy", policy.Name, "from", from, "to", to)
				return nil
			}
			clusterusage := ClustersResourceUsageItem{
				Cluster:         store.ObjectReferenceFrom(clusteritem),
				ClusterLabels:   clusteritem.Labels,
				MeasurementName: config.Name,
				Items:           items,
			}
			allcosts.Append(clusterusage)
			return nil
		})
	}
	eg.Wait()
	return allcosts.Get(), nil
}

// GetUsage implements MeasurementService.
func (r *ResourceCostCounter) getUsage(ctx context.Context, info cluster.CloudInfo, query string, from, to time.Time) ([]UsageItem, error) {
	_ = from // from is not used currently

	kubes, err := info.KubernetesConfig()
	if err != nil {
		return nil, err
	}
	op := cluster.NewContainerOperation(kubes)

	prom := prometheus.ContainerOperationPrometheus{Operation: op}
	vectors, err := prometheus.QueryVector(ctx, prom, query, to)
	if err != nil {
		return nil, err
	}
	items := make([]UsageItem, 0, len(vectors.Result))
	for _, vector := range vectors.Result {
		usage := UsageItem{
			Labels: vector.Metric,
			Value:  vector.Value.FloatValue(),
		}
		items = append(items, usage)
	}
	return items, nil
}

type CostMetadata struct {
	Tenant       string
	Organization string
	Application  string // which application it belong to

	Cluster   store.ObjectReference
	Namespace string
	Name      string // pod name,pvc name and etc.
	Alias     string // vm name, disk name and etc.
}

type CostItem struct {
	ResourceName        string                `json:"resourceName,omitempty"`
	ResourceDescription string                `json:"resourceDescription,omitempty"`
	UnitPrice           types.Price           `json:"unitPrice,omitempty"`
	Unit                ResourceUnit          `json:"unit,omitempty"`
	Price               types.Price           `json:"price,omitempty"`
	Cluster             store.ObjectReference `json:"cluster,omitempty"`
	Instance            string                `json:"instance,omitempty"`
	Namespace           string                `json:"namespace,omitempty"`
	Labels              map[string]string     `json:"labels,omitempty"`
	Value               float64               `json:"value,omitempty"`
}

type TenantOrganizationApplication struct {
	Tenant       string
	Organization string
	Application  string
}

func (t TenantOrganizationApplication) Reference() store.ResourcedObjectReference {
	scopes := []store.Scope{}
	if t.Tenant != "" {
		scopes = append(scopes, base.ScopeTenant(t.Tenant))
	}
	if t.Organization != "" {
		scopes = append(scopes, base.ScopeOrganization(t.Organization))
	}
	return store.ResourcedObjectReference{Scopes: scopes, Resource: "applications", Name: t.Application}
}

type ClassifiedCost struct {
	CostMetadata
	Quantity     float64
	SKUReference product.SKUReference
}

type empty = struct{}

type ClassifyCosts struct {
	From      time.Time
	To        time.Time
	Instances map[TenantOrganizationApplication][]ClassifiedCost
}

func (s *ResourceCostCounter) ClassifyCost(ctx context.Context, usage ClustersResourceUsage) (*ClassifyCosts, error) {
	pricingpolicies := &store.List[PricingPolicy]{}
	if err := s.Mongo.List(ctx, pricingpolicies); err != nil {
		return nil, err
	}
	alltenants := &store.List[tenant.Tenant]{}
	if err := s.Storage.List(ctx, alltenants); err != nil {
		return nil, err
	}
	tenantsmap := map[string]empty{}
	for _, tenant := range alltenants.Items {
		tenantsmap[tenant.Name] = empty{}
	}
	byapp := map[TenantOrganizationApplication][]ClassifiedCost{}
	for _, item := range usage.Items {
		for _, usageitem := range item.Items {
			metadata := s.getCostMetadata(ctx, item, usageitem)
			if metadata.Tenant == "" {
				continue
			}
			if _, ok := tenantsmap[metadata.Tenant]; !ok {
				continue
			}
			policy := findMatchPolicy(pricingpolicies.Items, item.MeasurementName, usageitem)
			if policy == nil {
				continue
			}
			classifiedCost := ClassifiedCost{
				CostMetadata: metadata,
				SKUReference: SystemResourceSKU(item.MeasurementName, policy.Name),
				// floor to 3 decimal places
				Quantity: float64(int(usageitem.Value*1000)) / 1000,
			}
			key := TenantOrganizationApplication{
				Tenant:       classifiedCost.Tenant,
				Organization: classifiedCost.Organization,
				Application:  classifiedCost.Application,
			}
			byapp[key] = append(byapp[key], classifiedCost)
		}
	}
	ret := &ClassifyCosts{
		From:      usage.From,
		To:        usage.To,
		Instances: byapp,
	}
	return ret, nil
}

func findMatchPolicy(policies []PricingPolicy, measermantName string, item UsageItem) *PricingPolicy {
	matches := []PricingPolicy{}
	for _, policy := range policies {
		if policy.MeasurementName != measermantName {
			continue
		}
		if !matchLabelValueRegexp(policy.MatchLabels, item.Labels) {
			continue
		}
		matches = append(matches, policy)
	}
	if len(matches) == 0 {
		return nil
	}
	// find the highest priority
	slices.SortFunc(matches, func(a, b PricingPolicy) int {
		return b.Priority - a.Priority
	})
	return &matches[0]
}

func matchLabelValueRegexp(match map[string]string, labels map[string]string) bool {
	for k, v := range match {
		labelv, ok := labels[k]
		if !ok {
			return false
		}
		// is regexp
		if strings.Contains(v, ".") {
			if matched, _ := regexp.MatchString(v, labelv); !matched {
				return false
			}
			continue
		}
		if labelv != v {
			return false
		}
	}
	return true
}

func (s *ResourceCostCounter) GenerateCostBilling(ctx context.Context, costs *ClassifyCosts) error {
	log := log.FromContext(ctx)

	for metadata, resources := range costs.Instances {
		account := metadata.Tenant
		if account == "" {
			continue
		}
		billingitems := make([]billing.CreateBillingItem, 0, len(resources))
		for _, costitem := range resources {
			if costitem.Quantity <= 0 {
				continue
			}
			billingitems = append(billingitems, billing.CreateBillingItem{
				SKU:          costitem.SKUReference,
				Quantity:     costitem.Quantity,
				Cluster:      costitem.Cluster,
				Instance:     costitem.Name,
				InstanceName: costitem.Alias,
				Namespace:    costitem.Namespace,
			})
		}
		if len(billingitems) == 0 {
			log.Info("no billing items", "metadata", metadata)
			continue
		}
		// create billing
		newbilling := billing.CreateBillingOptions{
			Tenant:         metadata.Tenant,
			Organization:   metadata.Organization,
			Reference:      metadata.Reference(),
			Description:    fmt.Sprintf("application %s compute resource", metadata.Application),
			Operator:       "system",
			From:           costs.From,
			To:             costs.To,
			Items:          billingitems,
			PaymentTimeout: costs.To.Sub(costs.From), // timeout must not longger than next billing generated
			Round:          ResoucesRound,
		}
		created, err := s.BillingService.CreateBilling(ctx, account, newbilling)
		if err != nil {
			log.Error(err, "failed to create billing", "billing", newbilling)
			continue
		}
		log.Info("created billing", "billing", created.Name)
	}
	return nil
}

func (s *ResourceCostCounter) getCostMetadata(_ context.Context, measurement ClustersResourceUsageItem, item UsageItem) CostMetadata {
	measurementname := measurement.MeasurementName
	labels := item.Labels
	if labels == nil {
		labels = map[string]string{}
	}
	metadata := CostMetadata{
		Tenant:       getmapvalue(labels, "tenant"),
		Organization: getmapvalue(labels, "organization"),
		Application:  getmapvalue(labels, "label_app_kubernetes_io_instance", "instance", "app"),
		Namespace:    getmapvalue(labels, "namespace", "resourcegroup"),
		Cluster:      measurement.Cluster,
	}
	if (metadata.Tenant == "" || metadata.Organization == "") && metadata.Namespace != "" {
		// get tenant and organization from namespace
		if splites := strings.SplitN(metadata.Namespace, "-", 2); len(splites) == 2 {
			metadata.Tenant, metadata.Organization = splites[0], splites[1]
		} else {
			// TODO: may find out this namespace belong to which organization
		}
	}
	switch {
	case strings.Contains(measurementname, "cpu") || strings.Contains(measurementname, "memory") || strings.Contains(measurementname, "network"):
		metadata.Name = getmapvalue(labels, "pod", "vm")
		metadata.Alias = getmapvalue(labels, "name")
	case strings.Contains(measurementname, "storage"):
		metadata.Name = getmapvalue(labels, "persistentvolumeclaim")
	case strings.Contains(measurementname, "disk"):
		metadata.Name = getmapvalue(labels, "disk")
		metadata.Alias = getmapvalue(labels, "name")
	case strings.Contains(measurementname, "ingress"):
		metadata.Name = getmapvalue(labels, "ingressclass")
	default:
		// if cpu_request-xxx, memory_request-xxx, use cpu
		prefix := strings.Split(string(measurementname), "_")[0]
		metadata.Name = getmapvalue(labels, prefix)
	}
	return metadata
}

func NewRandID() string {
	return time.Now().Format("20060102150405") + rand.RandomNumeric(6)
}

func getmapvalue(m map[string]string, key ...string) string {
	for _, k := range key {
		if v, ok := m[k]; ok {
			return v
		}
	}
	return ""
}

func matchLabels(match map[string]string, labels map[string]string) bool {
	for k, v := range match {
		if labels[k] != v {
			return false
		}
	}
	return true
}

func SystemResourceSKU(measermantName, resource string) product.SKUReference {
	return product.SKUReference{Category: product.CategoryCompute, Product: measermantName, Name: resource}
}
