package cost_test

import (
	"testing"

	libconfig "xiaoshiai.cn/common/config"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/rune/pkg/cloud/cost"
	"xiaoshiai.cn/rune/pkg/iam/events"
	"xiaoshiai.cn/rune/pkg/pay"
	paycost "xiaoshiai.cn/rune/pkg/pay/cost"
	"xiaoshiai.cn/rune/test/e2e/setup"
)

func TestResourceCostCounter_Run(t *testing.T) {
	ctx := libconfig.SetupSignalContext()
	ctx = log.NewContext(ctx, log.DefaultLogger)

	mongostore, err := setup.SetupMongoStore(ctx)
	if err != nil {
		t.Fatalf("Failed to create mongo store: %v", err)
		return
	}

	etcdstore, err1 := setup.SetupEtcdStore(ctx)
	if err1 != nil {
		t.Fatalf("Failed to create etcd store: %v", err1)
		return
	}

	cloudinfos, close, err := setup.SetupTestCloudInfo(ctx, etcdstore)
	if err != nil {
		t.Fatalf("Failed to setup etcd store: %v", err)
		return
	}
	defer close()

	paysystem, err := pay.New(ctx, etcdstore, mongostore, events.NoopRecorder{}, pay.NewDefaultOptions())
	if err != nil {
		t.Fatalf("Failed to create pay system: %v", err)
		return
	}

	options := cost.NewDefaultOptions()
	svc := cost.NewResourceCostCounter(etcdstore, mongostore, cloudinfos, paysystem.BillingService, options)
	if err := svc.DoNow(ctx, paycost.ResourcePeriodHour); err != nil {
		t.Fatalf("Failed to run resource cost counter: %v", err)
	}
}
