package cost

import (
	"context"
	"net/http"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/base"
	"xiaoshiai.cn/rune/pkg/pay/product"
	"xiaoshiai.cn/rune/pkg/pay/types"
)

type PolicyAPI struct {
	Store      store.Store
	SKUService product.SKUService
}

func NewPolicyAPI(mongo store.Store, sku product.SKUService) *PolicyAPI {
	return &PolicyAPI{Store: mongo, SKUService: sku}
}

func (a *PolicyAPI) ListPolicy(w http.ResponseWriter, r *http.Request) {
	api.On(w, r, func(ctx context.Context) (any, error) {
		return base.GenericList(r, a.Store, &store.List[PricingPolicy]{})
	})
}

func (a *PolicyAPI) CreatePolicy(w http.ResponseWriter, r *http.Request) {
	api.On(w, r, func(ctx context.Context) (any, error) {
		policy := &PricingPolicy{}
		if err := api.Body(r, policy); err != nil {
			return nil, err
		}
		if err := createPolicy(ctx, a.Store, a.SKUService, policy); err != nil {
			return nil, err
		}
		return policy, nil
	})
}

func createPolicy(ctx context.Context, store store.Store, sku product.SKUService, policy *PricingPolicy) error {
	if err := store.Create(ctx, policy); err != nil {
		return err
	}
	if err := ensurePolicySKU(ctx, sku, policy); err != nil {
		return err
	}
	return nil
}

func updatePolicy(ctx context.Context, store store.Store, sku product.SKUService, policy *PricingPolicy) error {
	if err := store.Update(ctx, policy); err != nil {
		return err
	}
	if err := ensurePolicySKU(ctx, sku, policy); err != nil {
		return err
	}
	return nil
}

func (a *PolicyAPI) deletePolicy(ctx context.Context, policyname string) (*PricingPolicy, error) {
	policy := &PricingPolicy{}
	policy.Name = policyname
	if err := a.Store.Delete(ctx, policy); err != nil {
		return nil, err
	}
	return policy, nil
}

func ensurePolicySKU(ctx context.Context, skusvc product.SKUService, policy *PricingPolicy) error {
	sku := &product.SKUData{
		Name:         policy.Name,
		Alias:        policy.Name,
		Category:     product.CategoryCompute,
		Product:      policy.MeasurementName,
		Description:  policy.Description,
		Price:        policy.UnitPrice,
		Enabled:      true,
		Stock:        0,
		Unit:         string(policy.Unit),
		StockPolicy:  product.StockPolicyNone,
		RefundPolicy: product.RefundPolicyNotSupport,
	}
	if _, err := skusvc.GetSKU(ctx, sku.Category, sku.Product, sku.Name); err != nil {
		if !errors.IsNotFound(err) {
			return err
		}
		if _, err := skusvc.CreateSKU(ctx, sku.Category, sku.Product, sku); err != nil {
			return err
		}
	}
	return skusvc.UpdateSKU(ctx, sku.Category, sku.Product, sku.Name, sku)
}

func (a *PolicyAPI) GetPolicy(w http.ResponseWriter, r *http.Request) {
	a.onPolicy(w, r, func(ctx context.Context, storage store.Store, policy string) (any, error) {
		return base.GenericGet(r, storage, &PricingPolicy{}, policy)
	})
}

type PricingPolicyUnitPrice struct {
	UnitPrice types.Price `json:"unitPrice"`
}

func (a *PolicyAPI) UpdatePolicyUnitPrice(w http.ResponseWriter, r *http.Request) {
	a.onPolicy(w, r, func(ctx context.Context, storage store.Store, policy string) (any, error) {
		p := &PricingPolicyUnitPrice{}
		if err := api.Body(r, p); err != nil {
			return nil, err
		}
		exists := &PricingPolicy{}
		if err := storage.Get(ctx, policy, exists); err != nil {
			return nil, err
		}
		exists.UnitPrice = p.UnitPrice
		if err := updatePolicy(ctx, a.Store, a.SKUService, exists); err != nil {
			return nil, err
		}
		return exists, nil
	})
}

func (a *PolicyAPI) UpdatePolicy(w http.ResponseWriter, r *http.Request) {
	a.onPolicy(w, r, func(ctx context.Context, storage store.Store, policy string) (any, error) {
		exists := &PricingPolicy{}
		if err := storage.Get(ctx, policy, exists); err != nil {
			return nil, err
		}
		if err := api.Body(r, exists); err != nil {
			return nil, err
		}
		if err := updatePolicy(ctx, a.Store, a.SKUService, exists); err != nil {
			return nil, err
		}
		return exists, nil
	})
}

func (a *PolicyAPI) DeletePolicy(w http.ResponseWriter, r *http.Request) {
	a.onPolicy(w, r, func(ctx context.Context, storage store.Store, policy string) (any, error) {
		return a.deletePolicy(ctx, policy)
	})
}

func (a *PolicyAPI) onPolicy(w http.ResponseWriter, r *http.Request, fn func(context.Context, store.Store, string) (any, error)) {
	api.On(w, r, func(ctx context.Context) (any, error) {
		policy := api.Path(r, "policy", "")
		if policy == "" {
			return nil, errors.NewBadRequest("policy is required")
		}
		return fn(ctx, a.Store, policy)
	})
}

func (a *PolicyAPI) Group() api.Group {
	return api.NewGroup("").SubGroup(
		api.
			NewGroup("/pricingpolicies").
			Route(
				api.GET("").
					Operation("list policies").
					To(a.ListPolicy).
					Response(store.List[PricingPolicy]{}).
					Param(api.PageParams...),

				api.POST("").
					Operation("create policy").
					Param(api.BodyParam("body", &PricingPolicy{})).
					To(a.CreatePolicy),

				api.PUT("/{policy}").
					Operation("update policy").
					Param(api.BodyParam("body", &PricingPolicy{})).
					To(a.UpdatePolicy),

				api.PUT("/{policy}/unitprice").
					Operation("update policy price").
					Param(api.BodyParam("body", &PricingPolicyUnitPrice{})).
					To(a.UpdatePolicyUnitPrice),

				api.DELETE("/{policy}").
					Param(api.BodyParam("body", &PricingPolicy{})).
					Operation("delete policy").
					To(a.DeletePolicy),

				api.GET("/{policy}").
					Operation("get policy").
					Response(PricingPolicy{}).
					To(a.GetPolicy),
			),
	)
}

type Pricing struct {
	Name        string      `json:"name"`
	Description string      `json:"description"`
	UnitPrice   types.Price `json:"unitPrice"`
	Unit        string      `json:"unit"`
}

func (a *PolicyAPI) ListPricing(w http.ResponseWriter, r *http.Request) {
	api.On(w, r, func(ctx context.Context) (any, error) {
		reqs := store.Requirements{
			store.RequirementEqual("disabled", false),
		}
		opts := base.ListOptionsToStoreListOptions(api.GetListOptions(r))
		opts = append(opts, store.WithFieldRequirements(reqs...))

		policylist := &store.List[PricingPolicy]{}
		if err := a.Store.List(ctx, policylist, opts...); err != nil {
			return nil, err
		}
		pricings := make([]Pricing, 0, len(policylist.Items))
		for _, policy := range policylist.Items {
			if policy.Disabled {
				continue
			}
			pricings = append(pricings, Pricing{
				Name:        policy.Name,
				Description: policy.Description,
				UnitPrice:   policy.UnitPrice,
				Unit:        string(policy.Unit),
			})
		}
		return pricings, nil
	})
}

func (a *PolicyAPI) PublicGroup() api.Group {
	return api.NewGroup("").SubGroup(
		api.
			NewGroup("/pricing").
			Route(
				api.GET("").
					Operation("list pricing").
					To(a.ListPricing).
					Response([]Pricing{}),
			),
	)
}
