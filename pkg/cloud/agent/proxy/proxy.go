package proxy

import (
	"context"
	"crypto/tls"
	"fmt"
	"net"
	"net/http"
	"net/http/httputil"
	"net/url"
	"path"
	"strings"
	"time"

	utilnet "k8s.io/apimachinery/pkg/util/net"
	"k8s.io/apimachinery/pkg/util/proxy"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/transport"
	"xiaoshiai.cn/common/rest/api"
	libproxy "xiaoshiai.cn/common/rest/proxy"
)

const KubernetesProxyPath = "/proxy/kubernetes"

type API struct {
	kubeTransports *KubeTransportCache
}

func NewAPI(restconfig *rest.Config) (*API, error) {
	api := &API{}
	if restconfig != nil {
		tpcache, err := NewTransportCache(restconfig)
		if err != nil {
			return nil, err
		}
		api.kubeTransports = tpcache
	}
	return api, nil
}

const useGoHTTPProxy = true

func (a *API) ProxyKubernets(w http.ResponseWriter, r *http.Request) {
	reqpath := api.Path(r, "path", "")

	authedtp, target := a.kubeTransports.Get(isSPDY(r))

	var reverseProxy http.Handler
	if useGoHTTPProxy {
		reverseProxy = &httputil.ReverseProxy{
			Transport: authedtp,
			Rewrite: func(pr *httputil.ProxyRequest) {
				pr.Out.URL.Path = reqpath
				pr.SetURL(&target)
			},
			FlushInterval: -1,
			ErrorHandler:  libproxy.ErrorResponser{}.Error,
		}
	} else {
		target.Path = path.Join(target.Path, reqpath)
		target.RawQuery += r.URL.RawQuery
		reverseProxy = proxy.NewUpgradeAwareHandler(&target, authedtp, true, false, libproxy.ErrorResponser{})
	}
	reverseProxy.ServeHTTP(w, r)
}

type KubeTransportCache struct {
	http2    http.RoundTripper
	http1    http.RoundTripper
	basehost url.URL
}

func NewTransportCache(config *rest.Config) (*KubeTransportCache, error) {
	target, err := url.Parse(config.Host)
	if err != nil {
		return nil, fmt.Errorf("failed to parse k8s server url")
	}
	http1, err := newKubTansportDisableHTTP2(config)
	if err != nil {
		return nil, err
	}
	http2, err := rest.TransportFor(config)
	if err != nil {
		return nil, err
	}
	return &KubeTransportCache{basehost: *target, http2: http2, http1: http1}, nil
}

func (c *KubeTransportCache) Get(disablehttp2 bool) (http.RoundTripper, url.URL) {
	if disablehttp2 {
		return c.http1, c.basehost
	}
	return c.http2, c.basehost
}

func newKubTansportDisableHTTP2(config *rest.Config) (http.RoundTripper, error) {
	cfg, err := config.TransportConfig()
	if err != nil {
		return nil, err
	}
	tlsConfig, err := transport.TLSConfigFor(cfg)
	if err != nil {
		return nil, err
	}
	var dial func(ctx context.Context, network, address string) (net.Conn, error)
	if cfg.DialHolder != nil {
		dial = cfg.DialHolder.Dial
	} else {
		dial = (&net.Dialer{Timeout: 30 * time.Second, KeepAlive: 30 * time.Second}).DialContext
	}
	proxy := http.ProxyFromEnvironment
	if cfg.Proxy != nil {
		proxy = cfg.Proxy
	}
	basictp := &http.Transport{
		Proxy:               proxy,
		TLSHandshakeTimeout: 10 * time.Second,
		TLSClientConfig:     tlsConfig,
		DialContext:         dial,
		DisableCompression:  config.DisableCompression,
		TLSNextProto:        map[string]func(authority string, c *tls.Conn) http.RoundTripper{},
	}
	return transport.HTTPWrappersForConfig(cfg, basictp)
}

func isSPDY(r *http.Request) bool {
	return strings.HasPrefix(strings.ToLower(r.Header.Get("Upgrade")), "spdy/")
}

func (a *API) ProxyHTTP(w http.ResponseWriter, r *http.Request) {
	proxyhost := api.Path(r, "host", "")
	proxypath := api.Path(r, "path", "")
	svcScheme, svcName, portStr, valid := utilnet.SplitSchemeNamePort(proxyhost)
	if !valid {
		http.Error(w, fmt.Sprintf("invalid service request %s", proxyhost), http.StatusBadRequest)
		return
	}
	dest := &url.URL{
		Scheme: svcScheme,
		Host: func() string {
			if portStr != "" {
				return net.JoinHostPort(svcName, portStr)
			}
			return svcName
		}(),
		Path:     proxypath,
		RawQuery: r.URL.RawQuery,
	}
	// It fix the websocket query parameters can't pass kube apiserver
	if query := r.Header.Get("x-query"); query != "" {
		dest.RawQuery = query
	}

	// use a "custom" transport to avoid the wrapping of the transport
	transport := http.DefaultTransport
	handler := proxy.NewUpgradeAwareHandler(dest, transport, false, false, libproxy.ErrorResponser{})
	handler.UseLocationHost = true
	handler.ServeHTTP(w, r)
}

// kubernetsProxyGroup use in cluster config to proxy kubernets request
func (a *API) kubernetsProxyGroup() api.Group {
	return api.
		NewGroup(KubernetesProxyPath).
		Route(
			api.Any("{path}*").To(a.ProxyKubernets),
		)
}

func (a *API) httpProxyGroup() api.Group {
	return api.
		NewGroup("/proxy").
		Route(
			api.Any("/{host}/{path}*").To(a.ProxyHTTP),
		)
}

func (a *API) Group() api.Group {
	optionalGroups := []api.Group{}
	if a.kubeTransports != nil {
		optionalGroups = append(optionalGroups, a.kubernetsProxyGroup())
	}
	return api.
		NewGroup("").
		SubGroup(
			a.httpProxyGroup(),
		).
		SubGroup(optionalGroups...)
}
