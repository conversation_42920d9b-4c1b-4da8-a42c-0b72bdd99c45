package terminal

import (
	"context"
	"fmt"
	"io"
	"os"
	"os/exec"
	"time"

	"github.com/creack/pty"
	"k8s.io/client-go/tools/remotecommand"
)

type Config struct {
	StreamIdleTimeout               time.Duration
	StreamCreationTimeout           time.Duration
	SupportedRemoteCommandProtocols []string
	SupportedPortForwardProtocols   []string
}

type TerminalSize = remotecommand.TerminalSize

func Exec(ctx context.Context, cmd []string, in io.Reader, out io.WriteCloser, err io.WriteCloser, tty bool, resize <-chan *TerminalSize) error {
	if len(cmd) == 0 {
		return fmt.Errorf("exec command required")
	}
	execCmd := exec.CommandContext(ctx, cmd[0], cmd[1:]...)
	if tty {
		execCmd.WaitDelay = 30 * time.Second
		return ttyCmd(execCmd, in, out, resize)
	} else {
		if in != nil {
			// Use an os.Pipe here as it returns true *os.File objects.
			// This way, if you run 'exec -i bash' (no tty) and type 'exit',
			// the call below to execCmd.Run() can unblock because its Stdin is the read half
			// of the pipe.
			r, w, err := os.Pipe()
			if err != nil {
				return err
			}
			defer r.Close()
			execCmd.Stdin = r
			go func() {
				_, _ = io.Copy(w, in)
				w.Close()
			}()
		}
		execCmd.Stdout = out
		execCmd.Stderr = err
		if err := execCmd.Start(); err != nil {
			return err
		}
		return execCmd.Wait()
	}
}

func ttyCmd(execCmd *exec.Cmd, stdin io.Reader, stdout io.WriteCloser, resizeChan <-chan *TerminalSize) error {
	p, err := pty.Start(execCmd)
	if err != nil {
		return err
	}
	defer p.Close()
	defer stdout.Close()
	HandleResizing(resizeChan, func(size *TerminalSize) {
		if size == nil {
			return
		}
		pty.Setsize(p, &pty.Winsize{Rows: size.Height, Cols: size.Width})
	})
	if stdin != nil {
		go func() { _, _ = io.Copy(p, stdin) }()
	}
	if stdout != nil {
		go func() { _, _ = io.Copy(stdout, p) }()
	}
	return execCmd.Wait()
}

func HandleResizing(resize <-chan *TerminalSize, resizeFunc func(size *TerminalSize)) {
	if resize == nil {
		return
	}
	go func() {
		for {
			size, ok := <-resize
			if !ok {
				return
			}
			if size.Height < 1 || size.Width < 1 {
				continue
			}
			resizeFunc(size)
		}
	}()
}
