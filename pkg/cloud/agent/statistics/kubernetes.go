package statistics

import (
	"context"
	"net/http"

	"k8s.io/apimachinery/pkg/api/meta"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/conversion"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"xiaoshiai.cn/common/rest/api"
)

type API struct {
	cli client.Client
}

func NewAPI(cli client.Client) *API {
	return &API{
		cli: cli,
	}
}

type ContainerClusterStatistics struct {
	ConfigMapCount   int `json:"configMapCount"`
	SecretCount      int `json:"secretCount"`
	ServiceCount     int `json:"serviceCount"`
	DeploymentCount  int `json:"deploymentCount"`
	StatefulSetCount int `json:"statefulSetCount"`
	DaemonSetCount   int `json:"daemonSetCount"`
	PodCount         int `json:"podCount"`
	JobsCount        int `json:"jobsCount"`
	NodeCount        int `json:"nodeCount"`
	CronJobsCount    int `json:"cronJobsCount"`
}

func (a *API) GetStatistics(w http.ResponseWriter, r *http.Request) {
	statistics, err := CalcK8sClusterStatistics(r.Context(), a.cli)
	if err != nil {
		api.Error(w, err)
		return
	}
	api.Success(w, statistics)
}

func CalcK8sClusterStatistics(ctx context.Context, cli client.Client) (*ContainerClusterStatistics, error) {
	countResource := func(ctx context.Context, gvk schema.GroupVersionKind) int {
		partialist := &metav1.PartialObjectMetadataList{}
		partialist.SetGroupVersionKind(gvk)
		_ = cli.List(ctx, partialist)
		itemsPtr, err := meta.GetItemsPtr(partialist)
		if err != nil {
			return 0
		}
		items, err := conversion.EnforcePtr(itemsPtr)
		if err != nil {
			return 0
		}
		return items.Len()
	}
	statistics := &ContainerClusterStatistics{
		ConfigMapCount:   countResource(ctx, schema.GroupVersionKind{Group: "", Version: "v1", Kind: "ConfigMap"}),
		SecretCount:      countResource(ctx, schema.GroupVersionKind{Group: "", Version: "v1", Kind: "Secret"}),
		ServiceCount:     countResource(ctx, schema.GroupVersionKind{Group: "", Version: "v1", Kind: "Service"}),
		DeploymentCount:  countResource(ctx, schema.GroupVersionKind{Group: "apps", Version: "v1", Kind: "Deployment"}),
		StatefulSetCount: countResource(ctx, schema.GroupVersionKind{Group: "apps", Version: "v1", Kind: "StatefulSet"}),
		DaemonSetCount:   countResource(ctx, schema.GroupVersionKind{Group: "apps", Version: "v1", Kind: "DaemonSet"}),
		PodCount:         countResource(ctx, schema.GroupVersionKind{Group: "", Version: "v1", Kind: "Pod"}),
		JobsCount:        countResource(ctx, schema.GroupVersionKind{Group: "batch", Version: "v1", Kind: "Job"}),
		CronJobsCount:    countResource(ctx, schema.GroupVersionKind{Group: "batch", Version: "v1", Kind: "CronJob"}),
		NodeCount:        countResource(ctx, schema.GroupVersionKind{Group: "", Version: "v1", Kind: "Node"}),
	}
	return statistics, nil
}

func (a *API) Group() api.Group {
	return api.
		NewGroup("/statistics").
		Route(
			api.GET("").
				To(a.GetStatistics),
		)
}
