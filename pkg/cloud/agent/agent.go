package agent

import (
	"context"
	"fmt"

	"golang.org/x/sync/errgroup"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/cluster"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/pprof"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/version"
	"xiaoshiai.cn/rune/pkg/cloud/agent/proxy"
	"xiaoshiai.cn/rune/pkg/cloud/agent/statistics"
)

type Options struct {
	// Listen local address to listen
	Listen string `json:"listen,omitempty"`
}

func (o *Options) Validate() error {
	errs := []error{}

	return errors.NewAggregate(errs)
}

func DefaultOptions() *Options {
	return &Options{
		Listen: ":8080",
	}
}

func Run(ctx context.Context, options *Options) error {
	deps, err := BuildDependencies(ctx, options)
	if err != nil {
		return err
	}
	localapi, err := BuildLocalAPI(ctx, deps)
	if err != nil {
		return err
	}
	eg, ctx := errgroup.WithContext(ctx)
	eg.Go(func() error {
		return localapi.Serve(ctx, options.Listen)
	})
	eg.Go(func() error {
		return pprof.Run(ctx)
	})
	return eg.Wait()
}

type Dependencies struct {
	Options    *Options
	RestConfig *rest.Config
	Client     client.Client
}

func BuildDependencies(ctx context.Context, options *Options) (*Dependencies, error) {
	restconfig, err := DefaultClientConfig().ClientConfig()
	if err != nil {
		return nil, fmt.Errorf("failed to get kubernetes rest config: %w", err)
	}
	// set default QPS and Burst
	restconfig.QPS = 1000
	restconfig.Burst = 2000

	cluster, err := cluster.New(restconfig)
	if err != nil {
		return nil, err
	}
	go cluster.Start(ctx)

	deps := &Dependencies{
		Options:    options,
		RestConfig: restconfig,
		Client:     cluster.GetClient(),
	}
	return deps, nil
}

func DefaultClientConfig() clientcmd.ClientConfig {
	return clientcmd.NewNonInteractiveDeferredLoadingClientConfig(
		clientcmd.NewDefaultClientConfigLoadingRules(), nil)
}
func BuildLocalAPI(ctx context.Context, deps *Dependencies) (*api.API, error) {
	proxyapi, err := proxy.NewAPI(deps.RestConfig)
	if err != nil {
		return nil, err
	}
	statisticsapi := statistics.NewAPI(deps.Client)
	return api.
		New().
		Plugin(
			api.VersionPlugin{Version: version.Get()},
			api.HealthCheckPlugin{},
			api.NewAPIDocPlugin("/docs", nil),
		).
		Group(
			api.NewGroup("").
				Filter(
					api.LoggingFilter(log.FromContext(ctx)),
					api.NewCORSFilter(),
				).
				SubGroup(
					statisticsapi.Group(),
					proxyapi.Group(),
				),
		), nil
}
