package client

import (
	"context"
	"path"

	"k8s.io/client-go/rest"
	"xiaoshiai.cn/common/httpclient"
	"xiaoshiai.cn/common/store"
)

// CloudClient is client of cloud service.
// other systems can use this interface to access cloud service.
type CloudClient interface {
	// GetKubeConfig returns the rest.Config for accessing the Kubernetes API server.
	// it not the original rest.Config
	GetKubeConfig(ctx context.Context, cluster store.ObjectReference) (*rest.Config, error)
}

type CloudServiceOptions struct {
	Address string `json:"address" description:"Address of the cloud service"`
}

func NewDefaultCloudServiceOptions() *CloudServiceOptions {
	return &CloudServiceOptions{
		Address: "http://rune-cloud",
	}
}

func NewRemoteClient(options *CloudServiceOptions) (*RemoteCloudClient, error) {
	cli, err := httpclient.NewClientFromConfig(context.Background(), &httpclient.Config{Server: options.Address})
	if err != nil {
		return nil, err
	}
	return &RemoteCloudClient{cli: cli}, nil
}

var _ CloudClient = &RemoteCloudClient{}

type RemoteCloudClient struct {
	cli *httpclient.Client
}

func (c *RemoteCloudClient) GetKubeConfig(ctx context.Context, cluster store.ObjectReference) (*rest.Config, error) {
	proxypath := "/internal/clusters/" + cluster.Name + "/kubernetes"
	target := *c.cli.Server
	target.Path = path.Join(target.Path, proxypath)
	config := &rest.Config{
		Host:      target.String(),
		Transport: c.cli.RoundTripper,
	}
	return config, nil
}
