package workspace

import (
	"context"

	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/base"
	"xiaoshiai.cn/rune/pkg/cloud/cluster"
)

type Workspace struct {
	store.ObjectMeta `json:",inline"`
	Cluster          ClusterNamespaceReference `json:"cluster"`
}

type ClusterNamespaceReference struct {
	store.ObjectReference `json:",inline"`
	Namespace             string `json:"namespace"`
}

func GetWorkspaceCluster(ctx context.Context, infos cluster.CloudInfoGetter, storage store.Store, tenant, workspacename string) (*cluster.KubernetesClients, string, error) {
	workspaceobj := &Workspace{}
	if err := storage.Scope(base.ScopeTenant(tenant)).Get(ctx, workspacename, workspaceobj); err != nil {
		return nil, "", err
	}
	info, err := infos.Get(ctx, workspaceobj.Cluster.ObjectReference)
	if err != nil {
		return nil, "", err
	}
	kubes, err := info.KubernetesConfig()
	if err != nil {
		return nil, "", err
	}
	return kubes, workspaceobj.Cluster.Namespace, nil
}
