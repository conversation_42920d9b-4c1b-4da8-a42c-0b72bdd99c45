package workspace

import (
	"xiaoshiai.cn/common/rest/api"
)

func (s *API) ResourceExtensionGroup() api.Group {
	return api.
		NewGroup("/apis").
		Route(
			api.GET("/core/v1/events").
				Doc("ListWatch events").
				To(s.Resources.ListWatchEvents).
				Param(
					api.QueryParam("namespace", "namespace").Optional(),
					api.QueryParam("kind", "pod,deployment etc").Optional(),
					api.QueryParam("name", "resource name").Optional(),
					api.QueryParam("watch", "watch events").Optional(),
					api.QueryParam("type", "event type").In("Normal", "Warning").Optional(),
					api.QueryParam("size", "Size").Optional(),
				),
			api.GET("/core/v1/{resource}/{name}:pods").
				Doc("List resource pods").
				To(s.Resources.ResourcePods),
			api.GET("/core/v1/{resource}/{name}:children").
				Doc("List resource pods").
				To(s.Resources.ResourceChildren),
			api.GET("/core/v1/pods/{name}:log").
				Doc("Get pod log").
				To(s.Resources.PodLog).
				Param(
					api.QueryParam("container", "").Optional(),
					api.QueryParam("follow", "").Optional(),
					api.QueryParam("previous", "").Optional(),
					api.QueryParam("sinceSeconds", "").Optional(),
					api.QueryParam("sinceTime", "").Optional(),
					api.QueryParam("timestamps", "").Optional(),
					api.QueryParam("tailLines", "").Optional(),
					api.QueryParam("limitBytes", "").Optional(),
					api.QueryParam("insecureSkipTLSVerifyBackend", "").Optional(),
				),
			api.GET("/core/v1/pods/{name}:exec").
				Doc("Exec in pod").
				To(s.Resources.PodExec).
				Param(
					api.QueryParam("stdin", "").Optional(),
					api.QueryParam("stdout", "").Optional(),
					api.QueryParam("stderr", "").Optional(),
					api.QueryParam("tty", "").Optional(),
					api.QueryParam("container", "").Optional(),
					api.QueryParam("command", "").Optional(),
				),
		)
}

func (s *API) ResourcesGroup() api.Group {
	return api.
		NewGroup("/resources").
		SubGroup(
			s.Resources.ResourcesGroup(false),
			s.ResourceExtensionGroup(),
		)
}
