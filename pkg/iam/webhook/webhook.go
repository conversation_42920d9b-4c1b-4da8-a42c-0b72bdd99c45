package webhook

import (
	"context"
	"net/http"

	"xiaoshiai.cn/common/rest/api"
)

type WebhookAPI struct {
	Authorizer         api.Authorizer
	TokenAuthenticator api.TokenAuthenticator
	BasicAuthenticator api.BasicAuthenticator
}

func NewWebhookAPI(tokenAuthenticator api.TokenAuthenticator, basicAuthenticator api.BasicAuthenticator, authorizer api.Authorizer) *WebhookAPI {
	return &WebhookAPI{
		Authorizer:         authorizer,
		TokenAuthenticator: tokenAuthenticator,
		BasicAuthenticator: basicAuthenticator,
	}
}

func (a *WebhookAPI) Authenticate(w http.ResponseWriter, r *http.Request) {
	api.On(w, r, func(ctx context.Context) (any, error) {
		req := &api.TokenAuthenticationRequest{}
		if err := api.Body(r, req); err != nil {
			return nil, err
		}
		var info *api.AuthenticateInfo
		var err error
		if req.Username != "" && req.Password != "" {
			// If username and password are provided, use basic authentication
			info, err = a.BasicAuthenticator.Authenticate(ctx, req.Username, req.Password)
		} else {
			info, err = a.TokenAuthenticator.Authenticate(ctx, req.Token)
		}
		if err != nil {
			resp := &api.TokenAuthenticationResponse{Error: err.Error()}
			return resp, nil
		}
		resp := &api.TokenAuthenticationResponse{
			Authenticated: true,
			UserInfo:      info.User,
			Audiences:     info.Audiences,
		}
		return resp, nil
	})
}

func (a *WebhookAPI) Authorize(w http.ResponseWriter, r *http.Request) {
	api.On(w, r, func(ctx context.Context) (any, error) {
		req := &api.AuthorizationRequest{}
		if err := api.Body(r, req); err != nil {
			return nil, err
		}
		decision, reason, err := a.Authorizer.Authorize(ctx, req.UserInfo, req.Attributes)
		if err != nil {
			return nil, err
		}
		resp := &api.AuthorizationResponse{
			Decision: decision,
			Reason:   reason,
		}
		return resp, nil
	})
}

func (a *WebhookAPI) PublicGroup() api.Group {
	return api.NewGroup("/webhook").
		Route(
			api.POST("/authenticate").To(a.Authenticate).
				Doc("Authenticate request via webhook").
				Param(
					api.BodyParam("request", api.TokenAuthenticationRequest{}).Desc("Request to authenticate"),
				).
				Response(api.TokenAuthenticationResponse{}, "authentication response"),
			api.POST("/authorize").To(a.Authorize).
				Doc("Authorize request via webhook").
				Param(
					api.BodyParam("request", api.AuthorizationRequest{}).Desc("Request to authorize"),
				).
				Response(api.AuthorizationResponse{}, "authorization response"),
		)
}
