package tenant

import (
	"context"

	"xiaoshiai.cn/common/controller"
	"xiaoshiai.cn/common/store"
)

func NewTenantController(storage store.Store) (*controller.Controller, error) {
	rec := &TenantController{
		Client: storage,
	}
	br := controller.NewBetterReconciler(rec, storage,
		controller.WithFinalizer("tenants-controller"),
		controller.WithAutosetStatus(),
	)
	c := controller.NewController("tenants", br).Watch(
		controller.NewStoreSource(storage, &Tenant{}),
	)
	return c, nil
}

var _ controller.Reconciler[*Tenant] = &TenantController{}

type TenantController struct {
	Client store.Store
}

// Remove implements Reconciler.
func (t *TenantController) Remove(ctx context.Context, obj *Tenant) (controller.Result, error) {
	return controller.Result{}, nil
}

// Sync implements Reconciler.
func (t *TenantController) Sync(ctx context.Context, obj *Tenant) (controller.Result, error) {
	obj.Status.Phase = TenantPhaseReady
	return controller.Result{}, nil
}
