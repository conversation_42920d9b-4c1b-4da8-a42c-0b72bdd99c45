package avatar

import (
	"context"
	"io"
	"net/http"
	"strconv"
	"time"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/base"
)

type SetAvatarOptions struct {
	Location      string
	ContentType   string
	ContentLength int64
	Modified      time.Time
	Expires       time.Time
	ETag          string
	Content       io.ReadCloser
}

type GetAvatarOptions struct {
	IfNoneMatch     string
	IfModifiedSince time.Time
	IfMatch         string
	CacheControl    string
}

type GetAvatarResponse struct {
	HttpCode      int
	Location      string
	LastModified  time.Time
	Expires       time.Time
	ETag          string
	ContentType   string
	ContentLength int64
	CacheControl  string
	Body          io.ReadCloser
}

type AvatarService interface {
	SetAvatar(ctx context.Context, kind string, name string, data SetAvatarOptions) error
	GetAvatar(ctx context.Context, kind string, name string, options GetAvatarOptions) (*GetAvatarResponse, error)
}

func NewAPI(storage store.Store, avatarService AvatarService) *API {
	return &API{
		Storage:       storage,
		AvatarService: avatarService,
		DefaultAvatarSVG: map[string][]byte{
			"tenant": DefaultTenantAvatarSVG,
			"user":   DefaultAvatarSVG,
		},
	}
}

type API struct {
	Storage          store.Store
	AvatarService    AvatarService
	DefaultAvatarSVG map[string][]byte
}

func (a *API) SetTenantAvatar(w http.ResponseWriter, r *http.Request) {
	base.OnTenant(w, r, func(ctx context.Context, tenantname string) (any, error) {
		if err := a.SetAvatarFromRequest(w, r, "tenant", tenantname); err != nil {
			return nil, err
		}
		return api.Empty, nil
	})
}

func (a *API) CurrentUserSetAvatar(w http.ResponseWriter, r *http.Request) {
	api.OnCurrentUser(w, r, func(ctx context.Context, username string) (any, error) {
		if err := a.SetAvatarFromRequest(w, r, "user", username); err != nil {
			return nil, err
		}
		return api.Empty, nil
	})
}

func (a *API) Group() api.Group {
	return api.
		NewGroup("").
		Tag("Avatar").
		SubGroup(
			api.NewGroup("/current/avatar").Route(
				api.POST("").To(a.CurrentUserSetAvatar).
					Doc("Set avatar").
					Param(
						api.BodyParam("avatar", []byte{}).Desc("image content"),
					),
			),
			api.NewGroup("/tenant/{tenant}/avatar").Route(
				api.POST("").
					To(a.SetTenantAvatar).
					Doc("Set avatar").
					Param(
						api.BodyParam("avatar", []byte{}).Desc("image content"),
					),
			),
		)
}

var (
	DefaultAvatarSVG       = []byte(`<svg xmlns="http://www.w3.org/2000/svg" width="128" height="128" viewBox="0 0 24 24"><path fill="#7F7F7F" d="M6.25 7a5.75 5.75 0 1 1 11.5 0a5.75 5.75 0 0 1-11.5 0m5.548 7.261a1 1 0 0 1 .13-.011h.144q.066 0 .13.011l7.295 1.283l.038.008c1.344.31 2.788 1.163 3.069 2.82l.004.029l.114.877v.002c.264 2.009-1.329 3.47-3.21 3.47a1 1 0 0 1-.124-.01h-14.9c-1.881 0-3.475-1.462-3.21-3.472l.114-.869l.005-.03c.28-1.627 1.736-2.528 3.077-2.819l.029-.006z"/></svg>`)
	DefaultTenantAvatarSVG = []byte(`<svg xmlns="http://www.w3.org/2000/svg" width="128" height="128" viewBox="0 0 256 256"><path fill="#7F7F7F" d="M239.73 208H224V96a16 16 0 0 0-16-16h-44a4 4 0 0 0-4 4v124h-16V32.41a16.43 16.43 0 0 0-6.16-13a16 16 0 0 0-18.72-.69L39.12 72A16 16 0 0 0 32 85.34V208H16.27A8.18 8.18 0 0 0 8 215.47a8 8 0 0 0 8 8.53h224a8 8 0 0 0 8-8.53a8.18 8.18 0 0 0-8.27-7.47M76 184a8 8 0 0 1-8.53 8a8.18 8.18 0 0 1-7.47-8.28v-15.45a8.19 8.19 0 0 1 7.47-8.27a8 8 0 0 1 8.53 8Zm0-56a8 8 0 0 1-8.53 8a8.19 8.19 0 0 1-7.47-8.28v-15.45a8.19 8.19 0 0 1 7.47-8.27a8 8 0 0 1 8.53 8Zm40 56a8 8 0 0 1-8.53 8a8.18 8.18 0 0 1-7.47-8.26v-15.47a8.19 8.19 0 0 1 7.47-8.26a8 8 0 0 1 8.53 8Zm0-56a8 8 0 0 1-8.53 8a8.19 8.19 0 0 1-7.47-8.26v-15.47a8.19 8.19 0 0 1 7.47-8.26a8 8 0 0 1 8.53 8Z"/></svg>`)
)

func (h *API) GetAvatar(w http.ResponseWriter, r *http.Request) {
	api.On(w, r, func(ctx context.Context) (any, error) {
		h.GetAvatarFromRequest(w, r, api.Path(r, "kind", ""), api.Path(r, "name", ""))
		return nil, nil
	})
}

func (h *API) GetAvatarFromRequest(w http.ResponseWriter, r *http.Request, kind, name string) {
	ctx := r.Context()
	getopt := GetAvatarOptions{
		IfNoneMatch:  r.Header.Get("If-None-Match"),
		IfMatch:      r.Header.Get("If-Match"),
		CacheControl: r.Header.Get("Cache-Control"),
	}
	if ifmodsince := r.Header.Get("If-Modified-Since"); ifmodsince != "" {
		ifmodsince, err := http.ParseTime(ifmodsince)
		if err != nil {
			http.Error(w, "Invalid If-Modified-Since header", http.StatusBadRequest)
			return
		}
		getopt.IfModifiedSince = ifmodsince
	}
	resp, err := h.AvatarService.GetAvatar(ctx, kind, name, getopt)
	if err != nil {
		if errors.IsNotFound(err) {
			if def, ok := h.DefaultAvatarSVG[kind]; ok {
				w.Header().Set("Content-Type", "image/svg+xml")
				w.Header().Set("Content-Length", strconv.Itoa(len(def)))
				w.Write(def)
				return
			}
			w.WriteHeader(http.StatusNotFound)
			return
		}
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}
	if resp.ContentType != "" {
		w.Header().Set("Content-Type", resp.ContentType)
	}
	if resp.ContentLength > 0 {
		w.Header().Set("Content-Length", strconv.FormatInt(resp.ContentLength, 10))
	}
	if resp.ETag != "" {
		w.Header().Set("ETag", resp.ETag)
	}
	if !resp.Expires.IsZero() {
		w.Header().Set("Expires", resp.Expires.Format(http.TimeFormat))
	}
	if !resp.LastModified.IsZero() {
		w.Header().Set("Last-Modified", resp.LastModified.Format(http.TimeFormat))
	}
	if resp.CacheControl != "" {
		w.Header().Set("Cache-Control", resp.CacheControl)
	}
	if resp.Location != "" {
		w.Header().Set("Location", resp.Location)
		if resp.HttpCode == 0 {
			resp.HttpCode = http.StatusFound
		}
	}
	if resp.HttpCode != 0 {
		w.WriteHeader(resp.HttpCode)
	}
	if resp.Body != nil {
		defer resp.Body.Close()
		io.Copy(w, resp.Body)
	}
}

func (h *API) SetAvatarFromRequest(w http.ResponseWriter, r *http.Request, kind, name string) error {
	mf, mfheader, err := r.FormFile("avatar")
	if err != nil {
		return err
	}
	setopts := SetAvatarOptions{
		ContentType:   mfheader.Header.Get("Content-Type"),
		ContentLength: int64(mfheader.Size),
		Modified:      time.Now(),
		Expires:       time.Now().Add(24 * time.Hour),
		Content:       mf,
	}
	return h.AvatarService.SetAvatar(r.Context(), kind, name, setopts)
}

func (l *API) PublicGroup() api.Group {
	return api.
		NewGroup("/avatars").
		Tag("Avatar").
		Route(
			api.GET("/{kind}/{name}").To(l.GetAvatar).Doc("Get avatar"),
		)
}
