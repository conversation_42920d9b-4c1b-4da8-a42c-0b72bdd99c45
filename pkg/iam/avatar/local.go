package avatar

import (
	"bytes"
	"context"
	"encoding/base64"
	"io"
	"net/http"
	"time"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/store"
)

const (
	MaxAvatarSize      = 1024 * 1024 * 5 // 5M
	AvatarCacheSeconds = 300             // 5 minutes
)

type Avatar struct {
	store.ObjectMeta `json:",inline"`
	Modified         time.Time `json:"modified,omitempty"`
	Location         string    `json:"location,omitempty"`
	ContentType      string    `json:"content_type,omitempty"`
	Expires          time.Time `json:"expires,omitempty"`
	ETag             string    `json:"etag,omitempty"`
	Content          string    `json:"content,omitempty"`
}

var _ AvatarService = &LocalAvatarService{}

func NewLocalAvatarService(store store.Store) *LocalAvatarService {
	return &LocalAvatarService{Store: store}
}

type LocalAvatarService struct {
	Store store.Store
}

// GetAvatar implements Service.
func (l *LocalAvatarService) GetAvatar(ctx context.Context, kind string, name string, options GetAvatarOptions) (*GetAvatarResponse, error) {
	avatar := &Avatar{}
	if err := l.Store.Scope(store.Scope{Resource: "kind", Name: kind}).Get(ctx, name, avatar); err != nil {
		if errors.IsNotFound(err) {
			return nil, errors.NewNotFound("avatar", name)
		}
		return nil, err
	}
	if ifnonematch := options.IfNoneMatch; ifnonematch != "" && ifnonematch == avatar.ETag {
		return &GetAvatarResponse{HttpCode: http.StatusNotModified}, nil
	}
	if ifmodsince := options.IfModifiedSince; !ifmodsince.IsZero() && !avatar.Modified.Truncate(time.Second).After(ifmodsince) {
		return &GetAvatarResponse{HttpCode: http.StatusNotModified}, nil
	}
	decodedContent, _ := base64.StdEncoding.DecodeString(avatar.Content)
	return &GetAvatarResponse{
		HttpCode:      http.StatusOK,
		LastModified:  avatar.Modified,
		ContentType:   avatar.ContentType,
		ContentLength: int64(len(decodedContent)),
		Expires:       avatar.Expires,
		Body:          io.NopCloser(bytes.NewReader(decodedContent)),
		ETag:          avatar.ETag,
	}, nil
}

// SetAvatar implements Service.
func (l *LocalAvatarService) SetAvatar(ctx context.Context, kind string, name string, opt SetAvatarOptions) error {
	if opt.Content == nil {
		return errors.NewBadRequest("empty avatar")
	}
	if opt.ContentLength > MaxAvatarSize {
		return errors.NewRequestEntityTooLarge("avatar too large")
	}
	storage := l.Store.Scope(store.Scope{Resource: "kind", Name: kind})
	avatr := &Avatar{ObjectMeta: store.ObjectMeta{Name: name}}

	return store.CreateOrUpdate(ctx, storage, avatr, func() error {
		if opt.Content != nil {
			data := make([]byte, opt.ContentLength)
			if _, err := io.ReadFull(opt.Content, data); err != nil {
				return errors.NewBadRequest("read avatar failed")
			}
			avatr.Content = base64.StdEncoding.EncodeToString(data)
			avatr.ContentType = opt.ContentType
		} else {
			avatr.Content = ""
			if opt.Location == "" {
				return errors.NewBadRequest("empty avatar location")
			}
			avatr.Location = opt.Location
		}
		avatr.Modified = time.Now()
		avatr.Expires = opt.Expires
		avatr.ETag = opt.ETag
		return nil
	})
}
