package rbac

import (
	"xiaoshiai.cn/common/store"
)

var DefaultSystemRoles = []Role{
	{
		ObjectMeta: store.ObjectMeta{
			Name:        RoleAdmin,
			Alias:       "systemadmin",
			Description: "Admin has full access to all resources",
		},
		Authorities: []Authority{{Actions: []string{"*"}, Resources: []string{"**"}}},
	},
	{
		ObjectMeta: store.ObjectMeta{
			Name:        RoleMember,
			Alias:       "systemviewer",
			Description: "Viewer can read all resources",
		},
		Authorities: []Authority{
			{
				Actions:   []string{"get", "list"},
				Resources: []string{"**"},
			},
			{
				Actions: []string{"*"},
				Resources: []string{
					"statistics",
					"tickets", "tickets:**",
					"ticket-modules", "ticket-modules:**",
					"users", "users:**",
					"tenants", "tenants:*", // allow manage tenant but tenant operations.
					"tenants:*:users", "tenants:*:users:*",
					"tenants:*:resourcequotas", "tenants:*:resourcequotas:*",
					"admin-products", "admin-products:**",
					"templategroups", "templategroups:**",
				},
			},
		},
	},
}

var DefaultTenantRoles = []Role{
	{
		ObjectMeta: store.ObjectMeta{
			Name:        RoleAdmin,
			Alias:       "tenantadmin",
			Description: "Admin has full access to all tenant resources",
		},
		Authorities: []Authority{{Actions: []string{"*"}, Resources: []string{"**"}}},
	},
	{
		ObjectMeta: store.ObjectMeta{
			Name:        RoleMember,
			Description: "Member can read organizations",
		},
		Authorities: []Authority{
			{
				Actions: []string{"get", "list"},
				Resources: []string{
					"statistics",
					"organizations", "organizations:*",
					"alertchannels", "alertchannels:*", // organization use tenant's resources
					"users", "users:*", // allow organization add tenant's users to organization
					"wallet", "wallet:*", // organization can see how much money tenant has
					"resourcepools", "resourcepools:*",
					"resourcequotas", "resourcequotas:*",

					// allow see tenant's quota
					"clusters:*:resourcequotas", "clusters:*:resourcequotas:*",
					// application deployment load metadata
					"clusters:*:metadata", "clusters:*:metadata:*",
					"system-clustermetadata:**",
					"local-clustermetadata:**",
				},
			},
			{
				Actions: []string{"*"},
				Resources: []string{
					"tickets", "tickets:**",
				},
			},
		},
	},
}

var DefaultOrganizationRoles = []Role{
	{
		ObjectMeta: store.ObjectMeta{
			Name:        RoleAdmin,
			Alias:       "organizationadmin",
			Description: "Admin has full access to all resources under the organization",
		},
		Authorities: []Authority{
			{
				Actions: []string{"*"}, Resources: []string{"**"},
			},
		},
	},
	{
		ObjectMeta: store.ObjectMeta{
			Name:        RoleMember,
			Alias:       "organizationmember",
			Description: "Member can operation basic organization resources",
		},
		Authorities: []Authority{
			{
				Actions: []string{"*"},
				Resources: []string{
					"applications:**",
					"licenses:**",
					"objectstorages:**",
					"orders:**",
					"databases:**",
				},
			},
			{
				Actions: []string{"get", "list"},
				Resources: []string{
					"statistics",
					"resourcepools:**",
					"resourcequotas:**",
					"alertchannels:**",
					"users:**",
					"gateways:**",
					"system-clustermetadata:**",
					"local-clustermetadata:**",
					"credentials:**",
					"bills:**",
					"orderreports:**",
				},
			},
		},
	},
}
