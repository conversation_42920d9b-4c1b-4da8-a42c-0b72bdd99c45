package rbac

import (
	"context"
	"fmt"
	"net/http"
	"slices"

	"xiaoshiai.cn/common/base"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/events"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/iam/authn"
)

const (
	RoleAdmin  = "admin"
	RoleMember = "member"
)

const (
	LastAdminCheck = false
)

type UserInfoGetter interface {
	GetUserProfile(ctx context.Context, username string) (*authn.UserProfile, error)
}

type AuthProviderUserInfoGetter struct {
	Provider authn.UserProvider
}

func NewAPI(storage store.Store, users UserInfoGetter, events events.Recorder) *API {
	return &API{Storage: storage, UserProvider: users, Recorder: events}
}

type API struct {
	Storage      store.Store
	UserProvider UserInfoGetter
	Recorder     events.Recorder
}

func NewRBACAPI(storage store.Store, users UserInfoGetter, recorder events.Recorder, scopePathVarNames ...api.ScopeVar) RbacAPI {
	return RbacAPI{
		Storage:           storage,
		UserProvider:      users,
		Recorder:          recorder,
		ScopePathVarNames: scopePathVarNames,
	}
}

type RbacAPI struct {
	Storage           store.Store
	ScopePathVarNames []api.ScopeVar
	UserProvider      UserInfoGetter
	Recorder          events.Recorder
}

func (a RbacAPI) Group() api.Group {
	prefix := ""
	for _, val := range a.ScopePathVarNames {
		prefix += "/" + val.Resource + "/{" + val.PathVarName + "}"
	}
	return api.
		NewGroup(prefix).
		SubGroup(
			a.rolesGroup(),
			a.userGroupsGroup(),
			a.MembersGroup(),
		)
}

type Member struct {
	User              string             `json:"user"`
	Role              string             `json:"role"`
	Roles             []string           `json:"roles,omitempty"`
	CreationTimestamp store.Time         `json:"creationTimestamp"`
	DeleteTimestamp   *store.Time        `json:"deleteTimestamp"`
	UserInfo          *authn.UserProfile `json:"userInfo"`
}

func (a *RbacAPI) ListMember(w http.ResponseWriter, r *http.Request) {
	a.OnScope(w, r, func(ctx context.Context, scopes []store.Scope) (any, error) {
		userRoleList, err := base.GenericList(r, a.Storage.Scope(scopes...), &store.List[UserRole]{})
		if err != nil {
			return nil, err
		}
		userroles := make([]Member, 0, len(userRoleList.Items))
		for _, rb := range userRoleList.Items {
			tuser := Member{
				User:              rb.Name,
				Role:              FirstOrEmpty(rb.Roles),
				Roles:             rb.Roles,
				CreationTimestamp: rb.CreationTimestamp,
				DeleteTimestamp:   rb.DeletionTimestamp,
			}
			if a.UserProvider != nil {
				userInfo, err := a.UserProvider.GetUserProfile(ctx, rb.Name)
				if err != nil {
					log.FromContext(ctx).Error(err, "Failed to get user profile")
				} else {
					tuser.UserInfo = &authn.UserProfile{User: userInfo.User}
				}
			}
			userroles = append(userroles, tuser)
		}
		return store.List[Member]{
			Items: userroles,
			Total: userRoleList.Total,
			Page:  userRoleList.Page,
			Size:  userRoleList.Size,
		}, nil
	})
}

func FirstOrEmpty(s []string) string {
	if len(s) > 0 {
		return s[0]
	}
	return ""
}

func (a *RbacAPI) SetMember(w http.ResponseWriter, r *http.Request) {
	a.OnUser(w, r, func(ctx context.Context, storage store.Store, user string) (any, error) {
		setRole := &Member{}
		if err := api.Body(r, setRole); err != nil {
			return nil, err
		}
		if setRole.Role == "" {
			return nil, errors.NewBadRequest("Role is required")
		}
		hasExists := true
		exists := &UserRole{}
		if err := storage.Get(ctx, user, exists); err != nil {
			if !errors.IsNotFound(err) {
				return nil, err
			}
			hasExists = false
		}
		// from 'admin' to other role
		if LastAdminCheck && slices.Contains(exists.Roles, RoleAdmin) && setRole.Role != RoleAdmin {
			// check must have at least one 'admin' role user
			roleList := &store.List[UserRole]{}
			if err := storage.List(ctx, roleList); err != nil {
				return nil, err
			}
			adminCount := 0
			for _, role := range roleList.Items {
				if role.Name == user || !slices.Contains(role.Roles, RoleAdmin) {
					continue
				}
				adminCount++
			}
			if adminCount == 0 {
				return nil, errors.NewBadRequest(fmt.Sprintf("user %s is the last admin user", user))
			}
		}
		if err := AddUserToScope(ctx, storage, nil, user, setRole.Role); err != nil {
			return nil, err
		}
		if !hasExists {
			scopes := make([]store.Scope, 0, len(a.ScopePathVarNames))
			for _, val := range a.ScopePathVarNames {
				value := api.Path(r, val.PathVarName, "")
				if value == "" {
					return nil, errors.NewBadRequest(val.PathVarName + " is required")
				}
				scopes = append(scopes, store.Scope{Resource: val.Resource, Name: value})
			}
			exists = &UserRole{
				ObjectMeta: store.ObjectMeta{
					Name:     user,
					Scopes:   scopes,
					Resource: "userroles",
				},
			}
		}
		a.Recorder.EventNoAggregate(ctx, exists, events.UserPermissionsRefresh, "Role Changed")
		return nil, nil
	})
}

func SetUserRole(ctx context.Context, storage store.Store, user, role string) error {
	roleObj := &UserRole{ObjectMeta: store.ObjectMeta{Name: user}}
	updatefunc := func() error {
		roleObj.Roles = []string{role}
		return nil
	}
	return store.CreateOrUpdate(ctx, storage, roleObj, updatefunc)
}

func AddUserToScope(ctx context.Context, storage store.Store, scopes []store.Scope, user, role string) error {
	return SetUserRole(ctx, storage.Scope(scopes...), user, role)
}

func (a *RbacAPI) RemoveMember(w http.ResponseWriter, r *http.Request) {
	a.OnUser(w, r, func(ctx context.Context, storage store.Store, user string) (any, error) {
		exists := &UserRole{}
		if err := storage.Get(ctx, user, exists); err != nil {
			if !errors.IsNotFound(err) {
				return nil, err
			}
		}
		// check the scope must have at least one 'admin' role user
		if LastAdminCheck && slices.Contains(exists.Roles, RoleAdmin) {
			roleList := &store.List[UserRole]{}
			if err := storage.List(ctx, roleList); err != nil {
				return nil, err
			}
			adminCount := 0
			for _, role := range roleList.Items {
				if role.Name == user || !slices.Contains(role.Roles, RoleAdmin) {
					continue
				}
				adminCount++
			}
			if adminCount == 0 {
				return nil, errors.NewBadRequest(fmt.Sprintf("user %s is the last admin user", user))
			}
		}
		role := &UserRole{}
		if err := storage.Get(ctx, user, role); err != nil {
			if !errors.IsNotFound(err) {
				return nil, err
			}
		}
		if err := store.IgnoreNotFound(storage.Delete(ctx, role)); err != nil {
			return nil, err
		}
		a.Recorder.EventNoAggregate(ctx, role, events.UserPermissionsRefresh, "User Role Changed")
		return nil, nil
	})
}

func (a *RbacAPI) OnUser(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, storage store.Store, user string) (any, error)) {
	a.OnScope(w, r, func(ctx context.Context, scopes []store.Scope) (any, error) {
		user := api.Path(r, "user", "")
		if user == "" {
			return nil, errors.NewBadRequest("User name is required")
		}
		return fn(ctx, a.Storage.Scope(scopes...), user)
	})
}

func (a *RbacAPI) MembersGroup() api.Group {
	return api.
		NewGroup("/members").
		Route(
			api.GET("").
				Operation("list members").
				To(a.ListMember),
			api.PUT("/{member}").
				Operation("add member").
				To(a.SetMember).
				Param(
					api.BodyParam("role", Member{}),
				),
			api.DELETE("/{member}").
				Operation("delete member").
				To(a.RemoveMember),
		)
}

type ScopeVar struct {
	Resource    string
	PathVarName string
}

func (a RbacAPI) OnScope(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, scopes []store.Scope) (any, error)) {
	api.On(w, r, func(ctx context.Context) (any, error) {
		scopes := make([]store.Scope, 0, len(a.ScopePathVarNames))
		for _, val := range a.ScopePathVarNames {
			value := api.Path(r, val.PathVarName, "")
			if value == "" {
				return nil, errors.NewBadRequest(val.PathVarName + " is required")
			}
			scopes = append(scopes, store.Scope{Resource: val.Resource, Name: value})
		}
		return fn(ctx, scopes)
	})
}

type UserGroup struct {
	store.ObjectMeta `json:",inline"`
	Tenant           string   `json:"tenant,omitempty"`
	Users            []string `json:"users,omitempty"`
}

func (a *RbacAPI) ListUserGroups(w http.ResponseWriter, r *http.Request) {
	a.OnScope(w, r, func(ctx context.Context, scopes []store.Scope) (any, error) {
		return base.GenericListWithWatch(w, r, a.Storage.Scope(scopes...), &store.List[UserGroup]{})
	})
}

func (a *RbacAPI) GetUserGroup(w http.ResponseWriter, r *http.Request) {
	a.onUserGroup(w, r, func(ctx context.Context, storage store.Store, name string) (any, error) {
		return base.GenericGet(r, storage, &UserGroup{}, name)
	})
}

func (a *RbacAPI) CreateUserGroup(w http.ResponseWriter, r *http.Request) {
	a.onUserGroup(w, r, func(ctx context.Context, storage store.Store, tenant string) (any, error) {
		return base.GenericCreate(r, storage, &UserGroup{Tenant: tenant})
	})
}

func (a *RbacAPI) UpdateUserGroup(w http.ResponseWriter, r *http.Request) {
	a.onUserGroup(w, r, func(ctx context.Context, storage store.Store, name string) (any, error) {
		return base.GenericUpdate(r, storage, &UserGroup{}, name)
	})
}

func (a *RbacAPI) PatchUserGroup(w http.ResponseWriter, r *http.Request) {
	a.onUserGroup(w, r, func(ctx context.Context, storage store.Store, name string) (any, error) {
		return base.GenericPatch(r, storage, &UserGroup{}, name)
	})
}

func (a *RbacAPI) DeleteUserGroup(w http.ResponseWriter, r *http.Request) {
	a.onUserGroup(w, r, func(ctx context.Context, storage store.Store, name string) (any, error) {
		return base.GenericDelete(r, storage, &UserGroup{}, name)
	})
}

func (a *RbacAPI) AddUserToGroup(w http.ResponseWriter, r *http.Request) {
	a.onUserGroupUser(w, r, func(ctx context.Context, storage store.Store, tenant, name string) (any, error) {
		return nil, errors.NewNotImplemented("Not implemented")
	})
}

func (a *RbacAPI) RemoveUserFromGroup(w http.ResponseWriter, r *http.Request) {
	a.onUserGroupUser(w, r, func(ctx context.Context, storage store.Store, tenant, name string) (any, error) {
		return nil, errors.NewNotImplemented("Not implemented")
	})
}

func (a *RbacAPI) onUserGroupUser(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, storage store.Store, usergroup, user string) (any, error)) {
	a.onUserGroup(w, r, func(ctx context.Context, storage store.Store, usergroup string) (any, error) {
		user := api.Path(r, "user", "")
		if user == "" {
			return nil, errors.NewBadRequest("User name is required")
		}
		return fn(ctx, storage, usergroup, user)
	})
}

func (a *RbacAPI) onUserGroup(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, storage store.Store, usergroup string) (any, error)) {
	a.OnScope(w, r, func(ctx context.Context, scopes []store.Scope) (any, error) {
		userGroup := api.Path(r, "usergroup", "")
		if userGroup == "" {
			return nil, errors.NewBadRequest("UserGroup name is required")
		}
		return fn(ctx, a.Storage.Scope(scopes...), userGroup)
	})
}

type Role struct {
	store.ObjectMeta `json:",inline"`
	Hidden           bool        `json:"hidden,omitempty"` // hidden role will not be listed
	Authorities      []Authority `json:"authorities,omitempty"`
}

type Authority = api.Authority

type UserRole struct {
	store.ObjectMeta `json:",inline"`
	Roles            []string `json:"roles,omitempty"`
}

func (a *RbacAPI) ListRoles(w http.ResponseWriter, r *http.Request) {
	a.OnScope(w, r, func(ctx context.Context, scopes []store.Scope) (any, error) {
		return base.GenericListWithWatch(w, r, a.Storage.Scope(scopes...), &store.List[Role]{})
	})
}

func (a *RbacAPI) CreateRole(w http.ResponseWriter, r *http.Request) {
	a.OnScope(w, r, func(ctx context.Context, scopes []store.Scope) (any, error) {
		return base.GenericCreate(r, a.Storage.Scope(scopes...), &Role{})
	})
}

func (a *RbacAPI) GetRole(w http.ResponseWriter, r *http.Request) {
	a.OnRole(w, r, func(ctx context.Context, storage store.Store, name string) (any, error) {
		return base.GenericGet(r, storage, &Role{}, name)
	})
}

func (a *RbacAPI) UpdateRole(w http.ResponseWriter, r *http.Request) {
	a.OnRole(w, r, func(ctx context.Context, storage store.Store, name string) (any, error) {
		return base.GenericUpdate(r, storage, &Role{}, name)
	})
}

func (a *RbacAPI) PatchRole(w http.ResponseWriter, r *http.Request) {
	a.OnRole(w, r, func(ctx context.Context, storage store.Store, name string) (any, error) {
		return base.GenericPatch(r, storage, &Role{}, name)
	})
}

func (a *RbacAPI) DeleteRole(w http.ResponseWriter, r *http.Request) {
	a.OnRole(w, r, func(ctx context.Context, storage store.Store, name string) (any, error) {
		return base.GenericDelete(r, storage, &Role{}, name)
	})
}

func (a *RbacAPI) OnRole(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, storage store.Store, role string) (any, error)) {
	a.OnScope(w, r, func(ctx context.Context, scopes []store.Scope) (any, error) {
		role := api.Path(r, "role", "")
		if role == "" {
			return nil, errors.NewBadRequest("Role name is required")
		}
		return fn(ctx, a.Storage.Scope(scopes...), role)
	})
}

func (a *RbacAPI) rolesGroup() api.Group {
	return api.
		NewGroup("/roles").
		Route(
			api.GET("").To(a.ListRoles).Doc("List roles").
				Param(api.PageParams...).Response(store.List[Role]{}),
			api.POST("").To(a.CreateRole).Doc("Create role").
				Param(api.BodyParam("role", Role{})).Response(Role{}),
			api.GET("/{role}").Doc("Get role").
				To(a.GetRole).Response(Role{}),
			api.PUT("/{role}").Doc("Update role").
				To(a.UpdateRole).Param(api.BodyParam("role", Role{})).Response(Role{}),
			api.PATCH("/{role}").Doc("Patch role").
				To(a.PatchRole).Param(api.BodyParam("role", Role{})).Response(Role{}),
			api.DELETE("/{role}").Doc("Delete role").
				To(a.DeleteRole),
		)
}

func (a *RbacAPI) userGroupsGroup() api.Group {
	return api.NewGroup("/usergroups").
		Route(
			api.GET("").
				Doc("List usergroups").
				To(a.ListUserGroups).Param(api.PageParams...).Response(store.List[UserGroup]{}),
			api.POST("").
				Doc("Create usergroup").
				To(a.CreateUserGroup).Param(api.BodyParam("usergroup", UserGroup{})).Response(UserGroup{}),
			api.GET("/{usergroup}").
				Doc("Get usergroup").
				To(a.GetUserGroup).Response(UserGroup{}),
			api.PUT("/{usergroup}").
				Doc("Update usergroup").
				To(a.UpdateUserGroup).Param(api.BodyParam("usergroup", UserGroup{})).Response(UserGroup{}),
			api.PATCH("/{usergroup}").
				Doc("Patch usergroup").
				To(a.PatchUserGroup).Param(api.BodyParam("usergroup", UserGroup{})).Response(UserGroup{}),
			api.DELETE("/{usergroup}").
				Doc("Delete usergroup").
				To(a.DeleteUserGroup),
			// usergroup users
			api.POST("/{usergroup}/users/{user}").
				Doc("Add user to usergroup").
				To(a.AddUserToGroup),
			api.DELETE("/{usergroup}/users/{user}").
				Doc("Remove user from usergroup").
				To(a.RemoveUserFromGroup),
		)
}

func (a *API) Group() api.Group {
	globalAPI := NewRBACAPI(a.Storage, a.UserProvider, a.Recorder)
	tenantAPI := NewRBACAPI(a.Storage, a.UserProvider, a.Recorder,
		api.ScopeVar{Resource: "tenants", PathVarName: "tenant"})
	organizationAPI := NewRBACAPI(a.Storage, a.UserProvider, a.Recorder,
		api.ScopeVar{Resource: "tenants", PathVarName: "tenant"},
		api.ScopeVar{Resource: "organizations", PathVarName: "organization"})
	workspaceAPI := NewRBACAPI(a.Storage, a.UserProvider, a.Recorder,
		api.ScopeVar{Resource: "tenants", PathVarName: "tenant"},
		api.ScopeVar{Resource: "workspaces", PathVarName: "workspace"})
	return api.
		NewGroup("").
		Tag("RBAC").
		SubGroup(
			a.CurrentGroup(),
			globalAPI.Group(),
			tenantAPI.Group(),
			organizationAPI.Group(),
			workspaceAPI.Group(),
		)
}
