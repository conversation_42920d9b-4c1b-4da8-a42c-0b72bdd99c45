package authz

import (
	"context"
	"strings"
	"time"

	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/iam/authz/custom"
	"xiaoshiai.cn/rune/pkg/iam/authz/rbac"
)

const (
	SystemAdminGroup = "system:admin"
	SystemBanedGroup = "system:baned"
)

const (
	AuthorizationModeNone = "none"
	AuthorizationModeRBAC = "rbac"
)

type AuthorizationOptions struct {
	Mode       string        `json:"mode,omitempty" description:"authorization mode,support 'rbac' and 'none'"`
	CacaheSize int           `json:"cacheSize,omitempty" description:"cache size for authorizer"`
	CacheTime  time.Duration `json:"cacheTime,omitempty" description:"cache time for authorizer"`
}

func DefaultAuthorizationOptions() *AuthorizationOptions {
	return &AuthorizationOptions{
		Mode: AuthorizationModeRBAC,
	}
}

func BuildAuthz(ctx context.Context, storage store.Store, options *AuthorizationOptions, others ...api.Authorizer) (api.Authorizer, error) {
	authzchain := api.AuthorizerChain{}
	authzchain = append(authzchain, others...)
	authzchain = append(authzchain, api.NewGroupAuthorizer([]string{SystemAdminGroup}, []string{SystemBanedGroup}))
	authzchain = append(authzchain, custom.NewCustomAuthorizer(storage))

	switch strings.ToLower(options.Mode) {
	case AuthorizationModeNone:
		authzchain = append(authzchain, api.NewAlwaysAllowAuthorizer())
	case AuthorizationModeRBAC:
		authzchain = append(authzchain, rbac.NewRBACAuthorizer(storage))
	}
	finalauthz := api.Authorizer(authzchain)
	if options.CacheTime > 0 {
		size := options.CacaheSize
		if size <= 0 {
			size = 128
		}
		finalauthz = api.NewCacheAuthorizer(finalauthz, size, options.CacheTime)
	}
	return finalauthz, nil
}
