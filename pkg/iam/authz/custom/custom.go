package custom

import (
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
)

// NewCustomAuthorizer defines the allowed actions and resources for every authenticated user.
// if API is role-based, update [rbac.DefaultTenantRoles] or [rbac.DefaultOrganizationRoles] instead.
func NewCustomAuthorizer(storage store.Store) api.Authorizer {
	return api.NewCustomAuthorizer(
		[]api.CustomAuthorizeNode{
			{
				Actions: []string{"get", "list"},
				Resource: []string{
					"avatars", "avatars:*", // user avatars
					"user-search", // tenant search add user
				},
			},
			{
				Actions: []string{"*"},
				Resource: []string{
					"current:**", // user self operations
				},
			},
		},
	)
}
