package events

import (
	"context"
	"time"

	"xiaoshiai.cn/common/store"
)

type Reason string

// Week = 7 days
const (
	Week      = 7 * 24 * time.Hour
	Day       = 24 * time.Hour
	ThreeDays = 3 * Day
)

type Event struct {
	// UID is the unique identifier of every event
	UID string `json:"uid,omitempty"`
	// Identifier is the identifier of the event, it is used to aggregate the event
	Identifier        string                         `json:"identifier,omitempty"`
	Annotations       map[string]string              `json:"annotations,omitempty"`
	Object            store.ResourcedObjectReference `json:"object,omitempty"`
	Reason            Reason                         `json:"reason,omitempty"`
	Messsge           string                         `json:"messsge,omitempty"`
	CreationTimestamp store.Time                     `json:"creationTimestamp,omitempty"`
}

type Recorder interface {
	// Event records an event
	// it aggregate the event with same reason and object
	// the same event will be recorded only once
	Event(ctx context.Context, obj store.Object, reason Reason, message string) error
	EventAnnotations(ctx context.Context, obj store.Object, reason Reason, message string, annotations map[string]string) error

	// EventNoAggregate records an event without aggregate
	EventNoAggregate(ctx context.Context, obj store.Object, reason Reason, message string) error
	EventNoAggregateAnnotations(ctx context.Context, obj store.Object, reason Reason, message string, annotations map[string]string) error
}

type NoopRecorder struct{}

func (NoopRecorder) Event(ctx context.Context, obj store.Object, reason Reason, message string) error {
	return nil
}

func (NoopRecorder) EventAnnotations(ctx context.Context, obj store.Object, reason Reason, message string, annotations map[string]string) error {
	return nil
}

func (NoopRecorder) EventNoAggregate(ctx context.Context, obj store.Object, reason Reason, message string) error {
	return nil
}

func (NoopRecorder) EventNoAggregateAnnotations(ctx context.Context, obj store.Object, reason Reason, message string, annotations map[string]string) error {
	return nil
}
