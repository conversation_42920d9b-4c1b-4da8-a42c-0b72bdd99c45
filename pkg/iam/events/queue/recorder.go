package queue

import (
	"context"
	"encoding/json"
	"maps"
	"time"

	"github.com/google/uuid"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/queue"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/iam/events"
)

var _ events.Recorder = (*QueueRecorder)(nil)

type QueueRecorder struct {
	Cache chan events.Event
	Queue queue.Queue
}

func NewQueueRecorder(ctx context.Context, q queue.Queue, cachesize int64) events.Recorder {
	r := &QueueRecorder{
		Queue: q,
		Cache: make(chan events.Event, cachesize),
	}
	go r.run(ctx)
	return r
}

func (q *QueueRecorder) Event(ctx context.Context, obj store.Object, reason events.Reason, message string) error {
	return q.EventAnnotations(ctx, obj, reason, message, nil)
}

func (q *QueueRecorder) EventAnnotations(ctx context.Context, obj store.Object, reason events.Reason, message string, annotations map[string]string) error {
	return q.eventAggregate(ctx, obj, reason, message, annotations)
}

// EventNoAggregate records an event without aggregate
func (q *QueueRecorder) EventNoAggregate(ctx context.Context, obj store.Object, reason events.Reason, message string) error {
	return q.EventNoAggregateAnnotations(ctx, obj, reason, message, nil)
}

// EventNoAggregate records an event without aggregate
func (q *QueueRecorder) EventNoAggregateAnnotations(ctx context.Context, obj store.Object, reason events.Reason, message string, annotations map[string]string) error {
	return q.eventNoAggregate(ctx, obj, reason, message, annotations)
}

func (q *QueueRecorder) eventAggregate(ctx context.Context, obj store.Object, reason events.Reason, message string, annotations map[string]string) error {
	identifier := obj.GetUID() + "-" + string(reason) + "-" + message
	return q.event(ctx, identifier, obj, reason, message, annotations)
}

func (q *QueueRecorder) eventNoAggregate(ctx context.Context, obj store.Object, reason events.Reason, message string, annotations map[string]string) error {
	identifier := obj.GetUID() + "-" + string(reason) + "-" + uuid.NewString()
	return q.event(ctx, identifier, obj, reason, message, annotations)
}

func (q *QueueRecorder) event(ctx context.Context, id string, obj store.Object, reason events.Reason, message string, annotations map[string]string) error {
	merged := mergeMaps(obj.GetAnnotations(), annotations)

	e := events.Event{
		UID:               uuid.NewString(),
		Identifier:        id,
		Object:            store.ResourcedObjectReferenceFrom(obj),
		Annotations:       merged,
		Reason:            reason,
		Messsge:           message,
		CreationTimestamp: store.Time{Time: time.Now()},
	}
	select {
	case q.Cache <- e:
		return nil
	case <-ctx.Done():
		return ctx.Err()
	}
}

func mergeMaps[M ~map[K]V, K comparable, V any](kvs ...M) M {
	ret := make(M)
	for _, kv := range kvs {
		maps.Copy(ret, kv)
	}
	return ret
}

func (q *QueueRecorder) run(ctx context.Context) error {
	log := log.FromContext(ctx)
	for {
		select {
		case <-ctx.Done():
			return nil
		case e := <-q.Cache:
			data, err := json.Marshal(e)
			if err != nil {
				return err
			}
			id := e.Identifier
			if id == "" {
				id = e.UID
			}
			if err := q.Queue.Enqueue(ctx, id, data, queue.EnqueueOptions{}); err != nil {
				if errors.IsAlreadyExists(err) {
					continue
				}
				log.Error(err, "enqueue event", "event", e)
			}
		}
	}
}
