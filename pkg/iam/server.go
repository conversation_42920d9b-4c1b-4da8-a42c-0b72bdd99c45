package iam

import (
	"context"
	"fmt"

	"golang.org/x/sync/errgroup"
	"xiaoshiai.cn/common/config"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/pprof"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/common/store/etcdcache"
	"xiaoshiai.cn/common/store/mongo"
	"xiaoshiai.cn/common/version"
	"xiaoshiai.cn/rune/pkg/iam/audit"
	"xiaoshiai.cn/rune/pkg/iam/authn"
	"xiaoshiai.cn/rune/pkg/iam/authn/provider/casdoor"
	"xiaoshiai.cn/rune/pkg/iam/authz"
	"xiaoshiai.cn/rune/pkg/iam/authz/rbac"
	"xiaoshiai.cn/rune/pkg/iam/avatar"
	"xiaoshiai.cn/rune/pkg/iam/events"
	"xiaoshiai.cn/rune/pkg/iam/tenant"
	"xiaoshiai.cn/rune/pkg/iam/webhook"
)

type Options struct {
	Listen         string                                 `json:"listen" description:"Listen address for the OIDC CAS Shim service" default:":8080"`
	API            *APIOptions                            `json:"api,omitempty" description:"API options for the server"`
	Config         *config.DynamicConfigOptions           `json:"config,omitempty" description:"Dynamic configuration options for the server"`
	Etcd           *etcdcache.Options                     `json:"etcd,omitempty"`
	Casdoor        *casdoor.Options                       `json:"casdoor,omitempty"`
	Mongodb        *mongo.MongoDBOptions                  `json:"mongodb,omitempty"`
	RequestHeader  *api.RequestHeaderAuthenticatorOptions `json:"requestHeader,omitempty"`
	Authentication *authn.AuthenticationOptions           `json:"authentication,omitempty"`
	Authorization  *authz.AuthorizationOptions            `json:"authorization,omitempty"`
	Audit          *audit.AuditOptions                    `json:"audit,omitempty"`
}

type APIOptions struct {
	Prefix string `json:"prefix,omitempty" description:"API prefix for the server, default is '/v1'"`
}

func NewDefaultOptions() *Options {
	casdooropt := casdoor.NewDefaultOptions()
	mongodb := mongo.NewDefaultMongoOptions("rune")
	return &Options{
		Listen:         ":8080",
		Config:         config.NewDefaultDynamicConfigOptions("rune"),
		API:            &APIOptions{Prefix: "/v1"},
		Etcd:           etcdcache.NewDefaultOptions(),
		Mongodb:        mongodb,
		Authentication: authn.DefaultAuthenticationOptions(),
		Authorization:  authz.DefaultAuthorizationOptions(),
		Audit:          audit.NewDefaultAuditOptions(),
		Casdoor:        casdooropt,
		RequestHeader:  api.NewDefaultRequestHeaderAuthenticatorOptions(),
	}
}

func Run(ctx context.Context, options *Options) error {
	deps, err := BuildDependencies(ctx, options)
	if err != nil {
		return err
	}
	eg, ctx := errgroup.WithContext(ctx)
	eg.Go(func() error {
		return RunServer(ctx, deps)
	})
	eg.Go(func() error {
		return pprof.Run(ctx)
	})
	return eg.Wait()
}

type Dependencies struct {
	Options       *Options
	Storage       store.Store
	Authn         api.Authenticator
	TokenAuthn    api.TokenAuthenticator
	BasicAuthn    api.BasicAuthenticator
	AuthProvider  authn.Provider
	Authz         api.Authorizer
	Recorder      events.Recorder
	AvatarService avatar.AvatarService
	Config        config.DynamicConfig
}

func BuildDependencies(ctx context.Context, options *Options) (*Dependencies, error) {
	etcdstore, err := etcdcache.NewEtcdCacher(options.Etcd, etcdcache.ResourceFieldsMap{})
	if err != nil {
		return nil, fmt.Errorf("failed to create etcd store: %w", err)
	}
	mongostore, err := mongo.NewMongoStorage(ctx, mongo.GlobalObjectsScheme, options.Mongodb)
	if err != nil {
		return nil, fmt.Errorf("failed to create mongodb store: %w", err)
	}
	_ = mongostore
	authp, err := casdoor.NewProvider(ctx, options.Casdoor)
	if err != nil {
		return nil, err
	}
	// authntoken, selfauthn, sessionauthn
	tokenauthn, basicauthn, err := authn.BuildAuthn(ctx, etcdstore, options.Authentication,
		[]api.TokenAuthenticator{authn.NewTokenAuthenticator(authp)},
		[]api.BasicAuthenticator{authn.NewAPIKeyAuthenticator(authp)},
	)
	if err != nil {
		return nil, err
	}
	authz, err := authz.BuildAuthz(ctx, etcdstore, options.Authorization)
	if err != nil {
		return nil, err
	}

	avatarService := avatar.NewLocalAvatarService(etcdstore)
	requestHeaderAuth := api.NewRequestHeaderAuthenticator(options.RequestHeader)
	return &Dependencies{
		Options:       options,
		Storage:       etcdstore,
		TokenAuthn:    tokenauthn,
		BasicAuthn:    basicauthn,
		AuthProvider:  authp,
		Authn:         requestHeaderAuth,
		Authz:         authz,
		AvatarService: avatarService,
	}, nil
}

func RunServer(ctx context.Context, deps *Dependencies) error {
	avatarapi := avatar.NewAPI(deps.Storage, deps.AvatarService)
	rbacapi := rbac.NewAPI(deps.Storage, deps.AuthProvider, nil)
	authnapi := authn.NewAPI(deps.AuthProvider, deps.Options.Authentication)
	tenantapi := tenant.NewAPI(deps.Storage, deps.Recorder)
	webhookapi := webhook.NewWebhookAPI(deps.TokenAuthn, deps.BasicAuthn, deps.Authz)

	return api.New().
		Plugin(
			api.VersionPlugin{Version: version.Get()},
			api.HealthCheckPlugin{},
			api.NewAPIDocPlugin("/docs", nil),
		).
		Filter(
			api.LoggingFilter(log.FromContext(ctx)),
			api.NewCORSFilter(),
		).
		Group(
			api.NewGroup(deps.Options.API.Prefix).
				SubGroup(
					// public group
					api.NewGroup("").
						SubGroup(
							authnapi.PublicGroup().
								Filter(
									authn.NewSessionFilter(deps.AuthProvider, deps.Options.Authentication),
								),
							avatarapi.PublicGroup(),
							webhookapi.PublicGroup(),
						),
					// auth group
					api.NewGroup("").
						Filter(
							api.NewAuthenticateFilter(deps.Authn, nil),
						).
						SubGroup(
							avatarapi.Group(),
							rbacapi.Group(),
							authnapi.Group(),
							tenantapi.Group(),
						),
				),
		).
		Serve(ctx, deps.Options.Listen)
}
