package audit

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"xiaoshiai.cn/common/base"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/apiserver"
)

type AuditOptions struct {
	Enabled bool `json:"enabled,omitempty"`
}

type Audit = apiserver.Audit

func NewDefaultAuditOptions() *AuditOptions {
	return &AuditOptions{
		Enabled: true,
	}
}

type ListAuditOptions struct {
	api.ListOptions `json:",inline"`
	Action          string    `json:"action"`
	Tenant          string    `json:"tenant"`
	ResourceType    string    `json:"resource"`
	ResourceName    string    `json:"resourceName"`
	Username        string    `json:"username"`
	StartTime       time.Time `json:"startTime"`
	EndTime         time.Time `json:"endTime"`
}

func (o ListAuditOptions) ToRequirements() store.Requirements {
	var conditions store.Requirements
	if o.Action != "" {
		conditions = append(conditions, store.RequirementEqual("action", o.Action))
	}
	if o.Tenant != "" {
		conditions = append(conditions, store.RequirementEqual("tenant", o.Tenant))
	}
	if o.ResourceType != "" {
		conditions = append(conditions, store.RequirementEqual("resourceType", o.ResourceType))
	}
	if o.ResourceName != "" {
		conditions = append(conditions, store.RequirementEqual("resourceName", o.ResourceName))
	}
	if o.Username != "" {
		conditions = append(conditions, store.RequirementEqual("subject", o.Username))
	}
	if !o.StartTime.IsZero() {
		conditions = append(conditions, store.Requirement{
			Key:      "startTime",
			Operator: store.GreaterThanOrEqual,
			Values:   []any{o.StartTime},
		})
	}
	if !o.EndTime.IsZero() {
		conditions = append(conditions, store.Requirement{
			Key:      "startTime",
			Operator: store.LessThanOrEqual,
			Values:   []any{o.EndTime},
		})
	}
	return conditions
}

func ListAudits(r *http.Request, storage store.Store, tenant string) (*store.List[AuditOverview], error) {
	start, end := api.Query(r, "startTime", time.Time{}), api.Query(r, "endTime", time.Time{})
	if start.IsZero() && end.IsZero() {
		start = time.Now().Add(-24 * time.Hour)
	}
	if end.IsZero() {
		end = time.Now()
	}
	if start.After(end) {
		return nil, errors.NewBadRequest(
			fmt.Sprintf("start time %s is after end time %s",
				start.Format(time.RFC3339), end.Format(time.RFC3339)))
	}
	// not allowed to query more than 7 days
	maxrange := 7 * 24 * time.Hour
	if end.Sub(start) > maxrange {
		return nil, errors.NewBadRequest(fmt.Sprintf("query range should be in %s", maxrange))
	}

	opts := ListAuditOptions{
		ListOptions:  api.GetListOptions(r),
		Action:       api.Query(r, "action", ""),
		Tenant:       tenant,
		ResourceType: api.Query(r, "resourceType", ""),
		ResourceName: api.Query(r, "resourceName", ""),
		Username:     api.Query(r, "username", ""),
		StartTime:    start,
		EndTime:      end,
	}
	options := base.ListOptionsToStoreListOptions(opts.ListOptions)
	options = append(options, store.WithFieldRequirements(opts.ToRequirements()...))
	auditlist := &store.List[AuditOverview]{}
	auditlist.SetResource("audits")
	if err := storage.List(r.Context(), auditlist, options...); err != nil {
		return nil, err
	}
	return auditlist, nil
}

type AuditOverview struct {
	Request      RequestOverView        `json:"request"`
	Response     ResponseOverview       `json:"response"`
	RequestID    string                 `json:"requestID,omitempty"` // request id
	Tenant       string                 `json:"tenant"`
	Organization string                 `json:"organization,omitempty"`
	Action       string                 `json:"action"`
	Parents      []api.AttrbuteResource `json:"parents,omitempty"` // parent resources, e.g. "zoos/{zoo_id}",
	ResourceType string                 `json:"resourceType"`
	ResourceName string                 `json:"resourceName"`
	Subject      string                 `json:"subject"`
	StartTime    time.Time              `json:"startTime"`
}

type RequestOverView struct {
	Method   string `json:"method"`
	URL      string `json:"url,omitempty"`      // full url
	ClientIP string `json:"clientIP,omitempty"` // client ip
}

type ResponseOverview struct {
	StatusCode int `json:"statusCode"`
}

type AuditAPI struct {
	Store store.Store
}

func NewAPI(store store.Store) *AuditAPI {
	return &AuditAPI{Store: store}
}

func (a *AuditAPI) ListAudits(w http.ResponseWriter, r *http.Request) {
	api.On(w, r, func(ctx context.Context) (any, error) {
		return ListAudits(r, a.Store, api.Query(r, "tenant", ""))
	})
}

func (a *AuditAPI) ListTenantAudits(w http.ResponseWriter, r *http.Request) {
	api.On(w, r, func(ctx context.Context) (any, error) {
		return ListAudits(r, a.Store, api.Path(r, "tenant", ""))
	})
}

var AuditParams = append([]api.Param{
	api.QueryParam("tenant", "tenant name").Optional(),
	api.QueryParam("action", "action type").Optional().In(
		"create", "update", "delete", "get", "list", "exec", "log",
	),
	api.QueryParam("resourceType", "resource type").Optional(),
	api.QueryParam("resourceName", "resource name").Optional(),
	api.QueryParam("username", "username").Optional(),
	api.QueryParam("startTime", "start time").Optional(),
	api.QueryParam("endTime", "end time").Optional(),
}, api.PageParams...)

func (a *AuditAPI) Group() api.Group {
	return api.
		NewGroup("").
		Tag("Audit").
		SubGroup(
			api.NewGroup("/tenants/{tenant}/audits").
				Route(
					api.GET("").
						Doc("List audit logs").
						To(a.ListTenantAudits).
						Param(AuditParams...).
						Response(store.List[Audit]{}),
				),
			api.NewGroup("/audits").
				Route(
					api.GET("").
						To(a.ListAudits).
						Doc("List audit logs").
						Param(AuditParams...).
						Response(store.List[Audit]{}),
				),
		)
}
