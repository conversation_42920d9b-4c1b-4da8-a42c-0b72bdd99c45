package casdoor

import (
	"context"
	"testing"

	"xiaoshiai.cn/common/rand"
)

func setupTestClient(t *testing.T) *SessionClient {
	options := &Options{
		Address:             "http://127.0.0.1:8000",
		DefaultOrganization: "default",
	}
	cli, err := NewUserClient(options)
	if err != nil {
		t.Error(err)
		return nil
	}
	return cli
}

func TestClient(t *testing.T) {
	client := setupTestClient(t)
	if client == nil {
		t.Error("client is nil")
	}
	ctx := context.Background()

	session := rand.RandomHex(32)

	account, err := client.getAccount(ctx, session)
	if err != nil {
		t.Error(err)
	}
	_ = account
}
