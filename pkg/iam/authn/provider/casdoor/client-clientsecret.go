package casdoor

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/httpclient"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	auth "xiaoshiai.cn/rune/pkg/iam/authn"
)

func NewAdminClient(options *Options) (*AdminClient, error) {
	return &AdminClient{options: options}, nil
}

type AdminClient struct {
	options *Options
}

func (c *AdminClient) setPassword(ctx context.Context, username, newPassword string) error {
	reqdata := map[string]string{
		"userOwner":   c.options.DefaultOrganization,
		"userName":    username,
		"newPassword": newPassword,
	}
	req := httpclient.Post("/api/set-password").FormURLEncoded(reqdata)
	return c.send(ctx, req)
}

type User struct {
	ID                 string            `json:"id"`
	Name               string            `json:"name"`
	Owner              string            `json:"owner"`
	CreatedTime        TimeOrEmpty       `json:"createdTime"`
	UpdatedTime        TimeOrEmpty       `json:"updatedTime"`
	ExternalID         string            `json:"externalId"`
	Type               string            `json:"type"` // normal-user,admin
	Password           string            `json:"password"`
	PasswordSalt       string            `json:"passwordSalt"`
	PasswordType       string            `json:"passwordType"`
	DisplayName        string            `json:"displayName"`
	FirstName          string            `json:"firstName"`
	LastName           string            `json:"lastName"`
	Avatar             string            `json:"avatar"`
	AvatarType         string            `json:"avatarType"`
	PermanentAvatar    string            `json:"permanentAvatar"`
	Email              string            `json:"email"`
	EmailVerified      bool              `json:"emailVerified"`
	Phone              string            `json:"phone"`
	CountryCode        string            `json:"countryCode"`
	Region             string            `json:"region"`
	Location           string            `json:"location"` // city
	Address            []string          `json:"address"`
	Affiliation        string            `json:"affiliation"`
	Title              string            `json:"title"` // job title
	IDCardType         string            `json:"idCardType"`
	IDCard             string            `json:"idCard"`
	Homepage           string            `json:"homepage"`
	Bio                string            `json:"bio"`
	Tag                string            `json:"tag"` // staff
	Language           string            `json:"language"`
	Gender             string            `json:"gender"`
	Birthday           string            `json:"birthday"`
	Education          string            `json:"education"`
	Currency           string            `json:"currency"`
	IsOnline           bool              `json:"isOnline"`
	IsForbidden        bool              `json:"isForbidden"` // banned
	AccessToken        string            `json:"accessToken"`
	LastSigninTime     string            `json:"lastSigninTime"`
	LastSigninIP       string            `json:"lastSigninIp"`
	MultiFactorAuths   []MultiFactorAuth `json:"multiFactorAuths"`
	Properties         map[string]string `json:"properties"`
	MFAAccounts        []MFAAccount      `json:"mfaAccounts"`
	NeedUpdatePassword bool              `json:"needUpdatePassword"`
	Groups             []string          `json:"groups"`
	AccessKey          string            `json:"accessKey"`
	AccessSecret       string            `json:"accessSecret"`
}

type MultiFactorAuth struct {
	Enabled     bool   `json:"enabled"`
	IsPreferred bool   `json:"isPreferred"`
	MFAType     string `json:"mfaType"`
}

type MFAAccount struct{}

func ConvertToUser(u *User) *auth.UserProfile {
	if u == nil {
		return nil
	}
	return &auth.UserProfile{
		User: auth.User{
			ObjectMeta: store.ObjectMeta{
				Name:              u.Name,
				UID:               u.ID,
				CreationTimestamp: store.Time{Time: u.CreatedTime.Time},
				Description:       u.Bio,
			},
			Subject:       u.ID,
			DisplayName:   u.DisplayName,
			Email:         u.Email,
			EmailVerified: u.EmailVerified,
			Phone:         u.Phone,
			Groups:        u.Groups,
		},
		FirstName:  u.FirstName,
		MiddleName: "",
		LastName:   u.LastName,
		Picture:    u.Avatar,
		Website:    u.Homepage,
		Gender:     u.Gender,
		Country:    u.CountryCode,
		State:      u.Region,
		City:       u.Location,
		Street:     strings.Join(u.Address, ","),
		PostalCode: "",
		Languages:  []string{u.Language},
		MFA: func(list []MultiFactorAuth) auth.MFAConfig {
			for _, mfa := range u.MultiFactorAuths {
				if mfa.Enabled && mfa.MFAType == "app" {
					return auth.MFAConfig{
						Enabled:  mfa.Enabled,
						Provider: auth.MFAProvider(mfa.MFAType),
					}
				}
			}
			return auth.MFAConfig{}
		}(u.MultiFactorAuths),
	}
}

func UpdateFromUser(u *User, profile auth.UserProfile, owner string) {
	if u.ID == "" {
		u.ID = profile.Subject
	}
	if u.Name == "" {
		u.Name = profile.Name
	}
	if u.Owner == "" {
		u.Owner = owner
	}
	u.Bio = profile.Description
	u.DisplayName = profile.DisplayName
	if u.DisplayName == "" {
		u.DisplayName = profile.Name
	}
	u.Email = profile.Email
	u.Phone = profile.Phone
	u.Groups = profile.Groups
	u.FirstName = profile.FirstName
	u.LastName = profile.LastName
	u.Avatar = profile.Picture
	u.Homepage = profile.Website
	u.Affiliation = profile.Company
	u.Location = profile.City
	if u.Properties == nil {
		u.Properties = make(map[string]string)
	}
	u.Properties["languages"] = strings.Join(profile.Languages, ",")
}

func (c *AdminClient) getUser(ctx context.Context, username string) (*User, error) {
	user := &User{}
	req := httpclient.Get("/api/get-user").Query("id", c.options.DefaultOrganization+"/"+username)
	_, respdata, err := c.do(ctx, req)
	if err != nil {
		return nil, err
	}
	if err := respdata.Error(); err != nil {
		return nil, err
	}
	if respdata.IsNullData() {
		return nil, errors.NewNotFound("users", username)
	}
	if err := respdata.Decode(user); err != nil {
		return nil, err
	}
	return user, nil
}

func (c *AdminClient) addUser(ctx context.Context, user User) error {
	user.CreatedTime = TimeOrEmpty{Time: time.Now()}
	req := httpclient.Post("/api/add-user").JSON(user)
	return c.send(ctx, req)
}

func (c *AdminClient) deleteUser(ctx context.Context, user User) error {
	req := httpclient.Post("/api/delete-user").JSON(user)
	return c.send(ctx, req)
}

func (c *AdminClient) updateUser(ctx context.Context, user User) error {
	id := c.options.DefaultOrganization + "/" + user.Name
	req := httpclient.Post("/api/update-user").Query("id", id).JSON(user)
	return c.send(ctx, req)
}

func (c *AdminClient) listUsers(ctx context.Context, owner string, options api.ListOptions) (api.Page[User], error) {
	req := httpclient.Get("/api/get-users").Query("owner", owner)

	sortfield, sortorder := api.ParseSort(options.Sort)
	if sortfield != "" {
		sortorder += "end"
	}
	if sortfield != "" {
		req = req.Query("sortField", sortfield).Query("sortOrder", sortorder)
	}
	if options.Search != "" {
		req = req.Query("field", "email").Query("value", options.Search)
	}
	if options.Size > 0 {
		req = req.Query("pageSize", strconv.Itoa(options.Size))
	}
	// p must be set if pageSize is set, otherwise it will return all users
	if options.Page > 0 || options.Size != 0 {
		req = req.Query("p", strconv.Itoa(options.Page))
	}
	list := api.Page[User]{}
	_, respdata, err := c.do(ctx, req)
	if err != nil {
		return list, err
	}
	if err := respdata.Error(); err != nil {
		return list, err
	}
	data := []User{}
	if err := json.Unmarshal(respdata.Data, &data); err != nil {
		return list, err
	}
	total := int64(0)
	_ = json.Unmarshal(respdata.Data2, &total)

	list.Items = data
	list.Page = int64(options.Page)
	list.Size = int64(options.Size)
	list.Total = total
	return list, nil
}

func (c *AdminClient) listGlobalUsers(ctx context.Context, options api.ListOptions) (api.Page[User], error) {
	req := httpclient.Get("/api/get-global-users")

	sortfield, sortorder := api.ParseSort(options.Sort)
	if sortfield != "" {
		sortorder += "end"
	}
	if sortfield != "" {
		req = req.Query("sortField", sortfield).Query("sortOrder", sortorder)
	}
	if options.Search != "" {
		req = req.Query("field", "email").Query("value", options.Search)
	}
	if options.Size > 0 {
		req = req.Query("pageSize", strconv.Itoa(options.Size))
	}
	if options.Page > 0 {
		req = req.Query("p", strconv.Itoa(options.Page))
	}
	list := api.Page[User]{}
	_, respdata, err := c.do(ctx, req)
	if err != nil {
		return list, err
	}
	if err := respdata.Error(); err != nil {
		return list, err
	}
	data := []User{}
	if err := json.Unmarshal(respdata.Data, &data); err != nil {
		return list, err
	}
	total := int64(0)
	_ = json.Unmarshal(respdata.Data2, &total)

	list.Items = data
	list.Page = int64(options.Page)
	list.Size = int64(options.Size)
	list.Total = total
	return list, nil
}

// /get-user-count
func (c *AdminClient) getUserCount(ctx context.Context, isonline *bool) (int64, error) {
	req := httpclient.Get("/api/get-user-count")
	if isonline != nil {
		req = req.Query("isOnline", strconv.FormatBool(*isonline))
	}
	_, respdata, err := c.do(ctx, req)
	if err != nil {
		return 0, err
	}
	if err := respdata.Error(); err != nil {
		return 0, err
	}
	total := int64(0)
	_ = json.Unmarshal(respdata.Data, &total)
	return total, nil
}

func (c *AdminClient) addUserKeys(ctx context.Context, user *User) error {
	req := httpclient.Post("/api/add-user-keys").JSON(user)
	return c.send(ctx, req)
}

func (r AdminClient) send(ctx context.Context, req *httpclient.Builder) error {
	into := req.R.DecodeInto
	_, respdata, err := r.do(ctx, req)
	if err != nil {
		return err
	}
	if err := respdata.Error(); err != nil {
		return err
	}
	if into != nil {
		return respdata.Decode(into)
	}
	return nil
}

func (r AdminClient) do(ctx context.Context, req *httpclient.Builder) (*http.Response, *ResponseWrapper, error) {
	respdata := &ResponseWrapper{}
	// admin client use client id and client secret to authenticate
	req = req.OnRequest(func(req *http.Request) error {
		req.SetBasicAuth(r.options.ClientID, r.options.ClientSecret)
		return nil
	})
	baseu, err := url.Parse(r.options.Address)
	if err != nil {
		return nil, nil, fmt.Errorf("parse casdoor address %s error: %w", r.options.Address, err)
	}
	resp, err := req.BaseAddr(baseu).Return(respdata).Do(ctx)
	if err != nil {
		return nil, nil, err
	}
	return resp, respdata, nil
}
