package authn

import (
	"context"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/rest/api"
)

var _ api.TokenAuthenticator = &TokenAuthenticator{}

func NewTokenAuthenticator(provider AuthProvider) *TokenAuthenticator {
	return &TokenAuthenticator{Provider: provider}
}

type TokenAuthenticator struct {
	Provider AuthProvider
}

// Authenticate implements api.TokenAuthenticator.
func (s *TokenAuthenticator) Authenticate(ctx context.Context, token string) (*api.AuthenticateInfo, error) {
	userinfo, err := s.Provider.UserInfo(ctx, token)
	if err != nil {
		return nil, err
	}
	if userinfo.Name == "" {
		return nil, errors.NewUnauthorized("username not found in session")
	}
	info := &api.AuthenticateInfo{
		User: api.UserInfo{
			ID:            userinfo.Subject,
			Name:          userinfo.Name,
			Email:         userinfo.Email,
			EmailVerified: userinfo.EmailVerified,
			Groups:        userinfo.Groups,
		},
	}
	return info, nil
}

type ApikeyAuthAuthenticator struct {
	Provider AuthProvider
}

func NewAPIKeyAuthenticator(provider AuthProvider) *ApikeyAuthAuthenticator {
	return &ApikeyAuthAuthenticator{Provider: provider}
}

func (a *ApikeyAuthAuthenticator) Authenticate(ctx context.Context, username, password string) (*api.AuthenticateInfo, error) {
	user, err := a.Provider.CheckAPIKey(ctx, APIKey{AccessKey: username, SecretKey: password})
	if err != nil {
		return nil, err
	}
	info := &api.AuthenticateInfo{
		User: api.UserInfo{
			ID:            user.Subject,
			Name:          user.Name,
			Email:         user.Email,
			EmailVerified: user.EmailVerified,
			Groups:        user.Groups,
		},
	}
	return info, nil
}
