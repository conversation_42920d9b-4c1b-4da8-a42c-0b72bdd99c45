package authn

import (
	"context"
	"log"
	"net"
	"net/http"
	"strings"

	libnet "xiaoshiai.cn/common/net"
	"xiaoshiai.cn/common/rest/api"
)

const SessionCookieKey = "user_session"

var _ api.Filter = SessionFilter{}

func NewSessionFilter(provider AuthProvider, options *AuthenticationOptions) SessionFilter {
	return SessionFilter{Provider: provider, Options: options}
}

type SessionFilter struct {
	Options  *AuthenticationOptions
	Provider AuthProvider
}

// Process implements api.Filter.
func (s SessionFilter) Process(w http.ResponseWriter, r *http.Request, next http.Handler) {
	session := api.GetCookie(r, s.Options.Session.CookieName)
	newsession, err := s.Provider.CheckSession(r.Context(), session)
	if err != nil {
		log.Printf("check session error: %v", err)
		next.ServeHTTP(w, r)
		return
	}
	// refresh the session
	if newsession != nil {
		domain := s.Options.Session.Domain
		if domain == "" {
			domain = ToShareCookieDomain(r.Host)
		}
		api.SetCookieWithDomain(w, s.Options.Session.CookieName, newsession.Value, newsession.Expires, domain, s.Options.Session.Insecure)
	}
	next.ServeHTTP(w, r)
}

func ToShareCookieDomain(hostport string) string {
	host, _ := libnet.SplitHostPort(hostport)
	if ip := net.ParseIP(host); ip != nil {
		return host
	}
	if elems := strings.Split(host, "."); len(elems) > 2 {
		host = strings.Join(elems[1:], ".")
	}
	// The "." prefix is invalid according to RFC 6265
	// as a workaround, if domain set, it auto set to domains and subdomains
	return host
}

func (a *API) OnSession(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, session string) (any, error)) {
	api.On(w, r, func(ctx context.Context) (any, error) {
		if authn := r.Header.Get("Authorization"); authn != "" {
			if after, ok := strings.CutPrefix(authn, "Bearer "); ok {
				return fn(ctx, after)
			}
			return fn(ctx, authn)
		}
		if key := api.GetCookie(r, a.Options.Session.CookieName); key != "" {
			return fn(ctx, key)
		}
		if token := r.URL.Query().Get("token"); token != "" {
			return fn(ctx, token)
		}
		return fn(ctx, "")
	})
}
