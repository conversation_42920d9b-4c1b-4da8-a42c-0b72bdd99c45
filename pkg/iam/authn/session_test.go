package authn

import "testing"

func TestToShareCookieDomain(t *testing.T) {
	tests := []struct {
		host string
		want string
	}{
		{"example.com", ".example.com"},
		{"sub.example.com", ".example.com"},
		{"sub.sub.example.com", ".sub.example.com"},
		{"localhost", ".localhost"},
		{"*******", "*******"},
		{"example.com:8080", ".example.com"},
		{"[2001:db8::1]", "[2001:db8::1]"}, // IPv6 address
	}
	for _, tt := range tests {
		t.Run(tt.host, func(t *testing.T) {
			if got := ToShareCookieDomain(tt.host); got != tt.want {
				t.<PERSON>rrorf("ToShareCookieDomain() = %v, want %v", got, tt.want)
			}
		})
	}
}
