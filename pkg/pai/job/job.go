package job

import (
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"xiaoshiai.cn/common/controller"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/pai/storageset"
)

type Job struct {
	store.ObjectMeta `json:",inline"`
	Kind             string           `json:"kind"`
	Preempt          bool             `json:"preempt"`
	Priority         string           `json:"priority"`
	Queue            string           `json:"queue"`
	Paused           bool             `json:"paused"`
	TTL              *metav1.Duration `json:"ttl"`
	RDMAEabaled      bool             `json:"rdmaEnabled"`
	Roles            []JobRole        `json:"roles"`
	Status           JobStatus        `json:"status"`
}

type JobRole struct {
	Name          string            `json:"name"  validate:"required"`
	Labels        map[string]string `json:"labels"`
	Annotations   map[string]string `json:"annotations"`
	Image         string            `json:"image"  validate:"required"`
	Replicas      int64             `json:"replicas"  validate:"required"`
	SKU           string            `json:"sku"  validate:"required"`
	Env           []v1.EnvVar       `json:"env"`
	Command       []string          `json:"command"`
	Args          []string          `json:"args"`
	Configs       []ConfigFile      `json:"configs"`
	Mounts        []VolumeMount     `json:"mounts"`
	SHMSize       resource.Quantity `json:"shmSize"`
	Ports         []Port            `json:"ports"`
	HostNetwork   bool              `json:"hostNetwork"`
	Privileged    bool              `json:"privileged"`
	WorkingDir    string            `json:"workingDir"`
	NodeSelector  map[string]string `json:"nodeSelector" `
	Affinity      *v1.Affinity      `json:"affinity"`
	Lifecycle     *v1.Lifecycle     `json:"lifecycle"`
	RestartPolicy v1.RestartPolicy  `json:"restartPolicy"`
}

type ConfigFile struct {
	// +kubebuilder:validation:Required
	Path  string `json:"path,omitempty"`
	Value string `json:"value,omitempty"`
}

type VolumeMount struct {
	Name       string                    `json:"name" validate:"required,gt=3,lt=20"` // 数据卷名字
	Kind       storageset.StorageSetKind `json:"kind" validate:"required"`
	TargetPath string                    `json:"targetPath" validate:"required,dirpath"` // 挂载路径
}

type Port struct {
	Name string `json:"name,omitempty"`
	Port int32  `json:"port,omitempty"`
}

type JobStatus struct {
	Replicas           int32                  `json:"replicas,omitempty"`
	Selector           string                 `json:"selector,omitempty"` // scale subresource selector
	Conditions         []controller.Condition `json:"conditions,omitempty"`
	Roles              []JobRoleStatus        `json:"roles,omitempty"`
	State              JobStatusState         `json:"state,omitempty"`
	History            []JobStatusState       `json:"history,omitempty"`
	Message            string                 `json:"message,omitempty"`
	ObservedGeneration int64                  `json:"observedGeneration,omitempty"`
}

type JobRoleStatus struct {
	Name      string                  `json:"name,omitempty"`
	Resources v1.ResourceRequirements `json:"resources,omitempty"`
	Ports     []PortStatus            `json:"ports,omitempty"`
	Replicas  ReplicasStatus          `json:"replicas,omitempty"`
}

type PortStatus struct {
	Name   string      `json:"name,omitempty"`
	Port   int32       `json:"port,omitempty"`
	Access *PortAccess `json:"access,omitempty"`
}

type JobAccessType string

const (
	JobAccessTypeNone  JobAccessType = ""
	JobAccessTypeHTTP  JobAccessType = "HTTP"
	JobAccessTypeHTTPS JobAccessType = "HTTPS"
	JobAccessTypeSSH   JobAccessType = "SSH"
)

type PortAccess struct {
	Type     JobAccessType `json:"type,omitempty"`
	Host     string        `json:"host,omitempty"`
	Username string        `json:"username,omitempty"`
	Password string        `json:"password,omitempty"`
}

type ReplicasStatus struct {
	Desired int32 `json:"desired,omitempty"`
	Current int32 `json:"current,omitempty"`
	Running int32 `json:"running,omitempty"`
}

type JobStatusState struct {
	LastTransitionTime metav1.Time  `json:"lastTransitionTime,omitempty"`
	StartTimestamp     *metav1.Time `json:"startTimestamp,omitempty"`
	RunningTimestamp   *metav1.Time `json:"runningTimestamp,omitempty"` // job is come into running state
	FinishTimestamp    *metav1.Time `json:"finishTimestamp,omitempty"`
	Phase              JobPhase     `json:"phase,omitempty"`
	Reason             string       `json:"reason,omitempty"`
}

type JobPhase string

const (
	JobPhasePaused    JobPhase = "Paused"
	JobPhasePending   JobPhase = "Pending"
	JobPhaseQueued    JobPhase = "Queued"
	JobPhaseScheduled JobPhase = "Scheduled"
	JobPhaseRunning   JobPhase = "Running"
	JobPhaseFailed    JobPhase = "Failed"
	JobPhaseCompleted JobPhase = "Completed"
	JobPhaseUnknown   JobPhase = "Unknown"
)
