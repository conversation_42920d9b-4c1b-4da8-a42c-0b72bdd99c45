package storageset

import (
	"context"
	"net/http"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/rune/pkg/base"
)

type API struct {
	Store store.Store
}

func NewAPI(store store.Store) *API {
	return &API{Store: store}
}

func (a *API) ListStorageSet(w http.ResponseWriter, r *http.Request) {
	a.onTenant(w, r, func(ctx context.Context, storage store.Store) (any, error) {
		list := store.List[StorageSet]{}
		return base.GenericList(r, storage, &list)
	})
}

func (a *API) GetStorageSet(w http.ResponseWriter, r *http.Request) {
	a.onStorageSet(w, r, func(ctx context.Context, storage store.Store, storageset string) (any, error) {
		return base.GenericGet(r, storage, &StorageSet{}, storageset)
	})
}

func (a *API) CreateStorageSet(w http.ResponseWriter, r *http.Request) {
	a.onTenant(w, r, func(ctx context.Context, storage store.Store) (any, error) {
		storageset := &StorageSet{}
		if err := api.Body(r, storageset); err != nil {
			return nil, errors.NewBadRequest(err.Error())
		}
		if err := validateStorageSet(storageset); err != nil {
			return nil, err
		}
		return base.GenericCreate(r, storage, storageset)
	})
}

func validateStorageSet(storageset *StorageSet) error {
	if storageset.Name == "" {
		return errors.NewBadRequest("storageset name is required")
	}
	if storageset.StorageClusterName == "" {
		return errors.NewBadRequest("storageClusterName is required")
	}
	if storageset.Capacity.IsZero() {
		return errors.NewBadRequest("capacity is required")
	}
	return nil
}

func (a *API) UpdateStorageSet(w http.ResponseWriter, r *http.Request) {
	a.onStorageSet(w, r, func(ctx context.Context, storage store.Store, storageset string) (any, error) {
		storagesetObj := &StorageSet{}
		if err := api.Body(r, storagesetObj); err != nil {
			return nil, errors.NewBadRequest(err.Error())
		}
		if err := validateStorageSet(storagesetObj); err != nil {
			return nil, err
		}
		return base.GenericUpdate(r, storage, storagesetObj, storageset)
	})
}

func (a *API) DeleteStorageSet(w http.ResponseWriter, r *http.Request) {
	a.onStorageSet(w, r, func(ctx context.Context, storage store.Store, storageset string) (any, error) {
		return base.GenericDelete(r, storage, &StorageSet{}, storageset)
	})
}

func (a *API) onTenant(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, storage store.Store) (any, error)) {
	base.OnTenant(w, r, func(ctx context.Context, tenant string) (any, error) {
		return fn(ctx, a.Store.Scope(base.ScopeTenant(tenant)))
	})
}

func (a *API) onStorageSet(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, storage store.Store, storageset string) (any, error)) {
	a.onTenant(w, r, func(ctx context.Context, storage store.Store) (any, error) {
		storageset := api.Path(r, "storageset", "")
		if storageset == "" {
			return nil, errors.NewBadRequest("storageset name is required")
		}
		return fn(ctx, storage, storageset)
	})
}

func (a *API) Group() api.Group {
	return api.
		NewGroup("").
		Tag("StorageSet").
		SubGroup(
			base.NewTenantGroup("storageset").
				Route(
					api.GET("").
						Operation("list storage sets").
						Param(api.PageParams...).
						To(a.ListStorageSet).
						Response(store.List[StorageSet]{}),

					api.GET("/{storageset}").
						Operation("get storage set").
						To(a.GetStorageSet).
						Response(StorageSet{}),

					api.POST("").
						Operation("create storage set").
						To(a.CreateStorageSet).
						Param(api.BodyParam("storageset", StorageSet{})).
						Response(StorageSet{}),

					api.PUT("/{storageset}").
						Operation("update storage set").
						To(a.UpdateStorageSet).
						Param(api.BodyParam("storageset", StorageSet{})).
						Response(StorageSet{}),

					api.DELETE("/{storageset}").
						Operation("delete storage set").
						To(a.DeleteStorageSet),
				),
		)
}
