package storageset

import (
	"k8s.io/apimachinery/pkg/api/resource"
	"xiaoshiai.cn/common/store"
)

type StorageSet struct {
	store.ObjectMeta   `json:",inline"`
	StorageClusterName string            `json:"storageClusterName" validate:"required"` // storageClusterName is the name of the storage cluster that this storage set belongs to
	Kind               StorageSetKind    `json:"kind"`
	Capacity           resource.Quantity `json:"capacity" validate:"required"` // capacity is the total capacity of the storage set, e.g., "100Gi"
	Config             map[string]string `json:"config"`
	Public             bool              `json:"public"` // public storage set can be accessed by all users
	Status             StorageSetStatus  `json:"status"`
}

type StorageSetKind string

type StorageSetStatus struct {
	Path string `json:"path"` // path is the storage path of the storage set
	S3   S3Info `json:"s3"`   // s3 is the S3 storage information of the storage set
}

type S3Info struct {
	Endpoint        string `json:"endpoint,omitempty"`        // Endpoint is the S3 endpoint URL
	Bucket          string `json:"bucket,omitempty"`          // Bucket is the S3 bucket name
	AccessKeyID     string `json:"accessKeyID,omitempty"`     // AccessKeyID is the S3 access key ID
	SecretAccessKey string `json:"secretAccessKey,omitempty"` // SecretAccessKey is the S3 secret access key

	ReadOnlyAccessKeyID     string `json:"readOnlyAccessKeyID,omitempty"`     // ReadOnlyAccessKeyID is the S3 read-only access key ID
	ReadOnlySecretAccessKey string `json:"readOnlySecretAccessKey,omitempty"` // ReadOnlySecretAccessKey is the S3 read-only secret access key
}
