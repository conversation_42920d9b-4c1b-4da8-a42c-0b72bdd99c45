package downloader

import (
	"xiaoshiai.cn/common/store"
)

type Downloader struct {
	store.ObjectMeta `json:",inline"`
	Kind             DownloaderKind    `json:"kind,omitempty"`
	Config           map[string]string `json:"config,omitempty"`
	Status           DownloaderStatus  `json:"status"`
}

type DownloaderKind string

type DownloaderStatus struct {
	Phase               DownloaderPhase `json:"phase,omitempty"`
	Message             string          `json:"message,omitempty"`
	PodName             string          `json:"podName,omitempty"`
	PodStatus           string          `json:"podStatus,omitempty"`
	StartTimestamp      *store.Time     `json:"startTimestamp,omitempty"`
	CompletionTimestamp *store.Time     `json:"completionTimestamp,omitempty"`
	RestartCount        int32           `json:"restartCount,omitempty"`
}

type DownloaderPhase string

const (
	DownloaderPhasePending   DownloaderPhase = "Pending"
	DownloaderPhaseRunning   DownloaderPhase = "Running"
	DownloaderPhaseSucceeded DownloaderPhase = "Succeeded"
	DownloaderPhaseFailed    DownloaderPhase = "Failed"
)
