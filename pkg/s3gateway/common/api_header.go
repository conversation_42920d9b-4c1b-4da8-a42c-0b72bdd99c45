package common

import (
	"bytes"
	"encoding/xml"
	"net/http"

	"xiaoshiai.cn/rune/pkg/s3gateway/internal/crypto"
)

func encodeResponse(response interface{}) []byte {
	var bytesBuffer bytes.Buffer
	bytesBuffer.WriteString(xml.Header)
	e := xml.NewEncoder(&bytesBuffer)
	e.Encode(response)
	return bytesBuffer.Bytes()
}

func setCommonHeaders(w http.ResponseWriter) {
	// Set the "Server" http header.
	w.Header().Set("Server", "S3Gateway")
	w.Header().Set("X-Amz-Bucket-Region", "default")
	w.Header().Set("Accept-Ranges", "bytes")

	// Remove sensitive information
	crypto.RemoveSensitiveHeaders(w.<PERSON><PERSON>())
}
