package common

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"strconv"
)

type mimeType string

const (
	// Means no response type.
	mimeNone mimeType = ""
	// Means response type is JSON.
	mimeJSON mimeType = "application/json"
	// Means response type is XML.
	mimeXML mimeType = "application/xml"
)

func WriteErrorResponse(ctx context.Context, w http.ResponseWriter, err APIError, reqURL *url.URL) {
	switch err.Code {
	case "SlowDown", "XMinioServerNotInitialized", "XMinioReadQuorum", "XMinioWriteQuorum":
		// Set retry-after header to indicate user-agents to retry request after 120secs.
		// https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Retry-After
		w.Header().Set("Retry-After", "120")
	case "InvalidRegion":
		err.Description = fmt.Sprintf("Region does not match")
	case "AuthorizationHeaderMalformed":
		err.Description = fmt.Sprintf("The authorization header is malformed; the region is wrong")
	}

	// Generate error response.
	errorResponse := getAPIErrorResponse(ctx, err, reqURL.Path,
		w.Header().Get("x-amz-request-id"), globalDeploymentID)
	encodedErrorResponse := encodeResponse(errorResponse)
	WriteResponse(w, err.HTTPStatusCode, encodedErrorResponse, mimeXML)
}
func WriteResponse(w http.ResponseWriter, statusCode int, response []byte, mType mimeType) {
	setCommonHeaders(w)
	if mType != mimeNone {
		w.Header().Set("Content-Type", string(mType))
	}
	w.Header().Set("Content-Length", strconv.Itoa(len(response)))
	w.WriteHeader(statusCode)
	if response != nil {
		w.Write(response)
	}
}
