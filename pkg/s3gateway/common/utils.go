package common

import (
	"errors"
	"net/url"
	"os"
	"path"
	"path/filepath"

	"github.com/google/uuid"
)

const (
	SlashSeparator = "/"
)

// not create
func GetValidPath(path string) (string, error) {
	if path == "" {
		return path, errors.New("Invalid arguments specified")
	}

	var err error
	// Disallow relative paths, figure out absolute paths.
	path, err = filepath.Abs(path)
	if err != nil {
		return path, err
	}

	fi, err := os.Lstat(path)
	if err != nil && !errors.Is(err, os.ErrNotExist) {
		return path, err
	}
	if errors.Is(err, os.ErrNotExist) {
		return path, errors.New("Path not existed")
	}
	if fi != nil && !fi.IsDir() {
		return path, errors.New("path is not directory or mountpoint")
	}
	return path, nil
}

func MustGetUUID() string {
	u, err := uuid.NewRandom()
	if err != nil {
		return ""
	}

	return u.String()
}

func UnescapePath(p string) (string, error) {
	return unescapeGeneric(p, url.PathUnescape)
}
func unescapeGeneric(p string, escapeFn func(string) (string, error)) (string, error) {
	ep, err := escapeFn(p)
	if err != nil {
		return "", err
	}
	return trimLeadingSlash(ep), nil
}
func trimLeadingSlash(ep string) string {
	if len(ep) > 0 && ep[0] == '/' {
		// Path ends with '/' preserve it
		if ep[len(ep)-1] == '/' && len(ep) > 1 {
			ep = path.Clean(ep)
			ep += SlashSeparator
		} else {
			ep = path.Clean(ep)
		}
		ep = ep[1:]
	}
	return ep
}
