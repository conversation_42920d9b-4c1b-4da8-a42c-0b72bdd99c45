package common

import "time"

type ObjectOptions struct {
	NoLock bool
}
type ObjectInfo struct {
	Bucket          string
	Name            string
	ModTime         time.Time
	Size            int64
	IsDir           bool
	ETag            string
	ContentType     string
	ContentEncoding string
	StorageClass    string
	Expires         time.Time
	UserTags        string
	UserDefined     map[string]string
	Parts           []ObjectPartInfo `json:"-"`
}

type ObjectPartInfo struct {
}

type BucketOptions struct {
}

type BucketInfo struct {
	Name    string
	Created time.Time
}
type DeleteBucketOptions struct {
	Force      bool // Force deletion
	NoRecreate bool // Do not recreate on delete failures
}
type ListObjectsInfo struct {
	IsTruncated bool
	NextMarker  string
	Objects     []ObjectInfo
	Prefixes    []string
}
type ListObjectsV2Info struct {
}
type ListObjectVersionsInfo struct {
}
type GetObjectReader struct {
}
type PutObjReader struct {
}
type ObjectToDelete struct {
}

type DeletedObject struct {
}

type ListMultipartsInfo struct {
}
type PartInfo struct {
}

type MultipartInfo struct {
}
type ListPartsInfo struct {
}
type CompletePart struct {
}

// 挂载点信息
type MountStatus struct {
}
