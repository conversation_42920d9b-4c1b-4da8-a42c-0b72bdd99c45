package crypto

import (
	"bytes"
	"crypto/md5"
	"encoding/base64"
	"errors"
	"net/http"
)

// RemoveSensitiveHeaders removes confidential encryption
// information - e.g. the SSE-C key - from the HTTP headers.
// It has the same semantics as RemoveSensitiveEntires.
func RemoveSensitiveHeaders(h http.Header) {
	h.Del("X-Amz-Server-Side-Encryption-Customer-Key")
	h.Del("X-Amz-Copy-Source-Server-Side-Encryption-Customer-Key")
	h.Del("X-Amz-Meta-X-Amz-Unencrypted-Content-Length")
	h.Del("X-Amz-Meta-X-Amz-Unencrypted-Content-Md5")
}

// SSECopy represents AWS SSE-C for copy requests. It provides
// functionality to handle SSE-C copy requests.
var SSECopy = ssecCopy{}

type ssecCopy struct{}

// IsRequested returns true if the HTTP headers contains
// at least one SSE-C copy header. Regular SSE-C headers
// are ignored.
func (ssecCopy) IsRequested(h http.Header) bool {
	if _, ok := h["X-Amz-Copy-Source-Server-Side-Encryption-Customer-Algorithm"]; ok {
		return true
	}
	if _, ok := h["X-Amz-Copy-Source-Server-Side-Encryption-Customer-Key"]; ok {
		return true
	}
	if _, ok := h["X-Amz-Copy-Source-Server-Side-Encryption-Customer-Key-Md5"]; ok {
		return true
	}
	return false
}

// ParseHTTP parses the SSE-C copy headers and returns the SSE-C client key
// on success. Regular SSE-C headers are ignored.
func (ssecCopy) ParseHTTP(h http.Header) (key [32]byte, err error) {
	if h.Get("X-Amz-Copy-Source-Server-Side-Encryption-Customer-Algorithm") != "AES256" {
		return key, errors.New("The SSE-C algorithm is not supported")
	}
	if h.Get("X-Amz-Copy-Source-Server-Side-Encryption-Customer-Key") == "" {
		return key, errors.New("The SSE-C request is missing the customer key")
	}
	if h.Get("X-Amz-Copy-Source-Server-Side-Encryption-Customer-Key-Md5") == "" {
		return key, errors.New("The SSE-C request is missing the customer key MD5")
	}

	clientKey, err := base64.StdEncoding.DecodeString(h.Get("X-Amz-Copy-Source-Server-Side-Encryption-Customer-Key"))
	if err != nil || len(clientKey) != 32 { // The client key must be 256 bits long
		return key, errors.New("The SSE-C client key is invalid")
	}
	keyMD5, err := base64.StdEncoding.DecodeString(h.Get("X-Amz-Copy-Source-Server-Side-Encryption-Customer-Key-Md5"))
	if md5Sum := md5.Sum(clientKey); err != nil || !bytes.Equal(md5Sum[:], keyMD5) {
		return key, errors.New("The provided SSE-C key MD5 does not match the computed MD5 of the SSE-C key")
	}
	copy(key[:], clientKey)
	return key, nil
}
