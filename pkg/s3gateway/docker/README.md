NFS:
    mkdir -p /app/nfs
    rpcbind -f &
    mount -t "nfs4" -o "nfsvers=4" ************:/nfs-export /app/nfs

CephFS:
    touch /etc/ceph/ceph.conf
    [global]
    fsid = xxxxxxxxxxxxxx
    mon_initial_members = ************
    mon_host = ************

    touch cat /etc/ceph/ceph.client.admin.keyring
    [client.admin]
    key = xxxxxxxxxxxxxxxxx
    caps mds = "allow *"
    caps mgr = "allow *"
    caps mon = "allow *"
    caps osd = "allow *"

    mkdir /app/cephfs
    ceph-fuse -n client.admin -k /etc/ceph/ceph.client.admin.keyring -c /etc/ceph/ceph.conf  /app/cephfs