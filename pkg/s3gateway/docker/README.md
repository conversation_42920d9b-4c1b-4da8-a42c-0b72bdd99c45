NFS:
    mkdir -p /s3gateway/nfs
    rpcbind -f &
    mount -t "nfs4" -o "nfsvers=4" ************:/nfs-export /s3gateway/nfs

CephFS:
    touch /etc/ceph/ceph.conf
    [global]
    fsid = xxxxxxxxxxxxxx
    mon_initial_members = ************
    mon_host = ************

    touch cat /etc/ceph/ceph.client.admin.keyring
    [client.admin]
    key = xxxxxxxxxxxxxxxxx
    caps mds = "allow *"
    caps mgr = "allow *"
    caps mon = "allow *"
    caps osd = "allow *"

    mkdir /s3gateway/cephfs
    ceph-fuse -n client.admin -k /etc/ceph/ceph.client.admin.keyring -c /etc/ceph/ceph.conf  /s3gateway/cephfs