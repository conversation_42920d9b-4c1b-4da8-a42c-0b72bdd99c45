package filesystem

import (
	"context"
	"net/http"

	"xiaoshiai.cn/rune/pkg/s3gateway/common"
)

type JuicefsConfig struct {
}

var _ FilesystemAdapter = &JuicefsAdapter{}

type JuicefsAdapter struct {
	*BaseAdapter
	config *JuicefsConfig
}

// DeleteBucket implements FilesystemAdapter.
func (j *JuicefsAdapter) DeleteBucket(ctx context.Context, bucket string, opts common.DeleteBucketOptions) error {
	panic("unimplemented")
}

// GetBucketInfo implements FilesystemAdapter.
func (j *JuicefsAdapter) GetBucketInfo(ctx context.Context, bucket string) (bucketInfo common.BucketInfo, err error) {
	panic("unimplemented")
}

// GetObjectNInfo implements FilesystemAdapter.
func (j *JuicefsAdapter) GetObjectNInfo(ctx context.Context, bucket string, object string, rs *common.HTTPRangeSpec, h http.Header, lockType LockType, opts common.ObjectOptions) (reader *common.GetObjectReader, err error) {
	panic("unimplemented")
}

// ListBuckets implements FilesystemAdapter.
func (j *JuicefsAdapter) ListBuckets(ctx context.Context) (buckets []common.BucketInfo, err error) {
	panic("unimplemented")
}

// ListObjectVersions implements FilesystemAdapter.
func (j *JuicefsAdapter) ListObjectVersions(ctx context.Context, bucket string, prefix string, marker string, versionMarker string, delimiter string, maxKeys int) (result common.ListObjectVersionsInfo, err error) {
	panic("unimplemented")
}

// ListObjects implements FilesystemAdapter.
func (j *JuicefsAdapter) ListObjects(ctx context.Context, bucket string, prefix string, marker string, delimiter string, maxKeys int) (result common.ListObjectsInfo, err error) {
	panic("unimplemented")
}

// ListObjectsV2 implements FilesystemAdapter.
func (j *JuicefsAdapter) ListObjectsV2(ctx context.Context, bucket string, prefix string, continuationToken string, delimiter string, maxKeys int, fetchOwner bool, startAfter string) (result common.ListObjectsV2Info, err error) {
	panic("unimplemented")
}

// MakeBucketWithLocation implements FilesystemAdapter.
func (j *JuicefsAdapter) MakeBucketWithLocation(ctx context.Context, bucket string, opts common.BucketOptions) error {
	panic("unimplemented")
}

// Walk implements FilesystemAdapter.
func (j *JuicefsAdapter) Walk(ctx context.Context, bucket string, prefix string, results chan<- common.ObjectInfo, opts common.ObjectOptions) error {
	panic("unimplemented")
}

// GetObjectInfo implements FilesystemAdapter.
func (j *JuicefsAdapter) GetObjectInfo(ctx context.Context, bucket string, object string, opts common.ObjectOptions) (objInfo common.ObjectInfo, err error) {
	panic("unimplemented")
}

func NewJuicefsAdapter(config *JuicefsConfig) (FilesystemAdapter, error) {
	return &JuicefsAdapter{
		BaseAdapter: &BaseAdapter{},
		config:      config,
	}, nil
}

// GetType implements FilesystemAdapter.
func (j *JuicefsAdapter) GetType() FilesystemType {
	return FilesystemTypeJuiceFS
}
