package filesystem

import (
	clientv3 "go.etcd.io/etcd/client/v3"
	"go.etcd.io/etcd/client/v3/namespace"
)

type EtcdConfig struct {
	Enabled    bool   `json:"enabled"`
	PathPrefix string `json:"pathPrefix"`
	clientv3.Config
}

func NewEtcdClient(cfg EtcdConfig) (*clientv3.Client, error) {
	if !cfg.Enabled {
		return nil, nil
	}
	cli, err := clientv3.New(cfg.Config)
	if err != nil {
		return nil, err
	}
	cli.KV = namespace.NewKV(cli.KV, cfg.PathPrefix)
	cli.Watcher = namespace.NewWatcher(cli.Watcher, cfg.PathPrefix)
	cli.Lease = namespace.NewLease(cli.Lease, cfg.PathPrefix)
	return cli, nil
}

// func parseEndpoints(endpoints string) ([]string, bool, error) {
// 	etcdEndpoints := strings.Split(endpoints, ",")

// 	var etcdSecure bool
// 	for _, endpoint := range etcdEndpoints {
// 		u, err := url.Parse(endpoint)
// 		if err != nil {
// 			return nil, false, err
// 		}
// 		if etcdSecure && u.Scheme == "http" {
// 			return nil, false, fmt.Errorf("all endpoints should be https or http: %s", endpoint)
// 		}
// 		// If one of the endpoint is https, we will use https directly.
// 		etcdSecure = etcdSecure || u.Scheme == "https"
// 	}

// 	return etcdEndpoints, etcdSecure, nil
// }
