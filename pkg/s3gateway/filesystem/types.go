package filesystem

import (
	"context"
	"net/http"
	"time"

	"xiaoshiai.cn/rune/pkg/s3gateway/common"
)

// FilesystemType represents the type of filesystem
type FilesystemType string

const (
	// Supported filesystem types
	FilesystemTypeNFS     FilesystemType = "nfs"
	FilesystemTypeCephFS  FilesystemType = "cephfs"
	FilesystemTypeJuiceFS FilesystemType = "juicefs"
	FilesystemTypeLocal   FilesystemType = "local"
)

// LockType represents required locking for ObjectLayer operations
type LockType int

const (
	noLock LockType = iota
	readLock
	writeLock
)

type FilesystemAdapter interface {
	// fs相关操作
	GetMountPoint() string
	GetName() string
	GetType() FilesystemType
	IsMounted() bool
	Mount(timeout time.Duration) error //挂载操作
	Unmount(timeout time.Duration, force bool) error
	GetMountStatus() (common.MountStatus, error)
	// 对象相关操作
	MakeBucketWithLocation(ctx context.Context, bucket string, opts common.BucketOptions) error
	GetBucketInfo(ctx context.Context, bucket string) (bucketInfo common.BucketInfo, err error)
	ListBuckets(ctx context.Context) (buckets []common.BucketInfo, err error)
	DeleteBucket(ctx context.Context, bucket string, opts common.DeleteBucketOptions) error
	ListObjects(ctx context.Context, bucket, prefix, marker, delimiter string, maxKeys int) (result common.ListObjectsInfo, err error)
	ListObjectsV2(ctx context.Context, bucket, prefix, continuationToken, delimiter string, maxKeys int, fetchOwner bool, startAfter string) (result common.ListObjectsV2Info, err error)
	ListObjectVersions(ctx context.Context, bucket, prefix, marker, versionMarker, delimiter string, maxKeys int) (result common.ListObjectVersionsInfo, err error)
	Walk(ctx context.Context, bucket, prefix string, results chan<- common.ObjectInfo, opts common.ObjectOptions) error
	GetObjectNInfo(ctx context.Context, bucket, object string, rs *common.HTTPRangeSpec, h http.Header, lockType LockType, opts common.ObjectOptions) (reader *common.GetObjectReader, err error)
	GetObjectInfo(ctx context.Context, bucket, object string, opts common.ObjectOptions) (objInfo common.ObjectInfo, err error)
	PutObject(ctx context.Context, bucket, object string, data *common.PutObjReader, opts common.ObjectOptions) (objInfo common.ObjectInfo, err error)
	CopyObject(ctx context.Context, srcBucket, srcObject, destBucket, destObject string, srcInfo common.ObjectInfo, srcOpts, dstOpts common.ObjectOptions) (objInfo common.ObjectInfo, err error)
	DeleteObject(ctx context.Context, bucket, object string, opts common.ObjectOptions) (common.ObjectInfo, error)
	DeleteObjects(ctx context.Context, bucket string, objects []common.ObjectToDelete, opts common.ObjectOptions) ([]common.DeletedObject, []error)
	ListMultipartUploads(ctx context.Context, bucket, prefix, keyMarker, uploadIDMarker, delimiter string, maxUploads int) (result common.ListMultipartsInfo, err error)
	NewMultipartUpload(ctx context.Context, bucket, object string, opts common.ObjectOptions) (uploadID string, err error)
	CopyObjectPart(ctx context.Context, srcBucket, srcObject, destBucket, destObject string, uploadID string, partID int,
		startOffset int64, length int64, srcInfo common.ObjectInfo, srcOpts, dstOpts common.ObjectOptions) (info common.PartInfo, err error)
	PutObjectPart(ctx context.Context, bucket, object, uploadID string, partID int, data *common.PutObjReader, opts common.ObjectOptions) (info common.PartInfo, err error)
	GetMultipartInfo(ctx context.Context, bucket, object, uploadID string, opts common.ObjectOptions) (info common.MultipartInfo, err error)
	ListObjectParts(ctx context.Context, bucket, object, uploadID string, partNumberMarker int, maxParts int, opts common.ObjectOptions) (result common.ListPartsInfo, err error)
	AbortMultipartUpload(ctx context.Context, bucket, object, uploadID string, opts common.ObjectOptions) error
	CompleteMultipartUpload(ctx context.Context, bucket, object, uploadID string, uploadedParts []common.CompletePart, opts common.ObjectOptions) (objInfo common.ObjectInfo, err error)
}
