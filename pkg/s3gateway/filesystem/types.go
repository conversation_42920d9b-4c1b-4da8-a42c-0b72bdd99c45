package filesystem

import (
	"context"
	"net/http"

	"xiaoshiai.cn/rune/pkg/s3gateway/common"
)

// FilesystemType represents the type of filesystem
type FilesystemType string

const (
	// Supported filesystem types
	FilesystemTypeNFS     FilesystemType = "nfs"
	FilesystemTypeCephFS  FilesystemType = "cephfs"
	FilesystemTypeJuiceFS FilesystemType = "juicefs"
	FilesystemTypeLocal   FilesystemType = "local"
)

// LockType represents required locking for ObjectLayer operations
type LockType int

const (
	noLock LockType = iota
	readLock
	writeLock
)

type FilesystemAdapter interface {
	GetType() FilesystemType
	MakeBucketWithLocation(ctx context.Context, bucket string, opts common.BucketOptions) error
	GetBucketInfo(ctx context.Context, bucket string) (bucketInfo common.BucketInfo, err error)
	ListBuckets(ctx context.Context) (buckets []common.BucketInfo, err error)
	DeleteBucket(ctx context.Context, bucket string, opts common.DeleteBucketOptions) error
	ListObjects(ctx context.Context, bucket, prefix, marker, delimiter string, maxKeys int) (result common.ListObjectsInfo, err error)
	ListObjectsV2(ctx context.Context, bucket, prefix, continuationToken, delimiter string, maxKeys int, fetchOwner bool, startAfter string) (result common.ListObjectsV2Info, err error)
	ListObjectVersions(ctx context.Context, bucket, prefix, marker, versionMarker, delimiter string, maxKeys int) (result common.ListObjectVersionsInfo, err error)
	Walk(ctx context.Context, bucket, prefix string, results chan<- common.ObjectInfo, opts common.ObjectOptions) error
	GetObjectNInfo(ctx context.Context, bucket, object string, rs *common.HTTPRangeSpec, h http.Header, lockType LockType, opts common.ObjectOptions) (reader *common.GetObjectReader, err error)

	GetObjectInfo(ctx context.Context, bucket, object string, opts common.ObjectOptions) (objInfo common.ObjectInfo, err error)
}
