package filesystem

import (
	"errors"
	"fmt"
	"os/user"

	"xiaoshiai.cn/rune/pkg/s3gateway/common"
)

type BaseAdapter struct {
	fsType       FilesystemType
	fsPath       string
	fsUUID       string
	isMountPoint bool
}

func NewBaseAdapter(fsPath string, fsType FilesystemType) (*BaseAdapter, error) {
	var err error
	if fsPath, err = common.GetValidPath(fsPath); err != nil {
		var username string
		if u, err := user.Current(); err == nil {
			username = u.Username
		} else {
			username = "<your-username>"
		}
		hint := fmt.Sprintf("Use 'sudo chown -R %s %s && sudo chmod u+rxw %s' to provide sufficient permissions.", username, fsPath, fsPath)
		return nil, errors.New(hint)
	}
	return &BaseAdapter{
		fsPath:       fsPath,
		fsType:       fsType,
		fsUUID:       common.MustGetUUID(),
		isMountPoint: common.IsLikelyMountPoint(fsPath),
	}, nil
}
