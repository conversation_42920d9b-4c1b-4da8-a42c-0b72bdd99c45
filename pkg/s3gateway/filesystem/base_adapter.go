package filesystem

import (
	"context"
	"errors"
	"fmt"
	"os"
	"os/user"
	"path"
	"sort"

	"xiaoshiai.cn/rune/pkg/s3gateway/common"
)

type BaseAdapter struct {
	fsType       FilesystemType
	fsPath       string
	fsUUID       string
	isMountPoint bool
}

func NewBaseAdapter(fsPath string, fsType FilesystemType) (*BaseAdapter, error) {
	var err error
	if fsPath, err = common.GetValidPath(fsPath); err != nil {
		var username string
		if u, err := user.Current(); err == nil {
			username = u.Username
		} else {
			username = "<your-username>"
		}
		hint := fmt.Sprintf("Use 'sudo chown -R %s %s && sudo chmod u+rxw %s' to provide sufficient permissions.", username, fsPath, fsPath)
		return nil, errors.New(hint)
	}
	return &BaseAdapter{
		fsPath:       fsPath,
		fsType:       fsType,
		fsUUID:       common.MustGetUUID(),
		isMountPoint: common.IsLikelyMountPoint(fsPath),
	}, nil
}
func (b *BaseAdapter) getBucketDir(_ context.Context, bucket string) (string, error) {
	if bucket == "" || bucket == "." || bucket == ".." {
		return "", errors.New("volume not found")
	}
	bucketDir := path.Join(b.fsPath, bucket)
	return bucketDir, nil
}

func (b *BaseAdapter) statBucketDir(ctx context.Context, bucket string) (os.FileInfo, error) {
	bucketDir, err := b.getBucketDir(ctx, bucket)
	if err != nil {
		return nil, err
	}
	fi, err := os.Stat(bucketDir)
	if err != nil {
		return nil, err
	}
	if !fi.IsDir() {
		return nil, errors.New("bucket is not dir")
	}
	return fi, nil
}

func (b *BaseAdapter) MakeBucketWithLocation(ctx context.Context, bucket string, opts common.BucketOptions) error {
	if common.CheckBucketNameCommon(bucket, true) != nil {
		return errors.New("Bucket name invalid: " + bucket)
	}
	bucketDir, err := b.getBucketDir(ctx, bucket)
	if err != nil {
		return err
	}
	return os.Mkdir((bucketDir), 0o777)
}

func (b *BaseAdapter) GetBucketInfo(ctx context.Context, bucket string) (common.BucketInfo, error) {
	st, err := b.statBucketDir(ctx, bucket)
	if err != nil {
		return common.BucketInfo{}, err
	}
	return common.BucketInfo{
		Name:    bucket,
		Created: st.ModTime(),
	}, nil
}

func (b *BaseAdapter) ListBuckets(ctx context.Context) ([]common.BucketInfo, error) {
	entries, err := common.ReadDirWithOpts(b.fsPath, common.ReadDirOpts{Count: -1, FollowDirSymlink: true})
	if err != nil {
		return nil, err
	}
	bucketInfos := make([]common.BucketInfo, 0, len(entries))
	for _, entry := range entries {
		if common.IsReservedOrInvalidBucket(entry, false) {
			continue
		}
		fi, err := os.Stat(path.Join(b.fsPath, entry))
		if err != nil {
			continue
		}
		bucketInfos = append(bucketInfos, common.BucketInfo{
			Name:    fi.Name(),
			Created: fi.ModTime(),
		})
	}
	sort.Slice(bucketInfos, func(i, j int) bool {
		return bucketInfos[i].Name < bucketInfos[j].Name
	})
	return bucketInfos, nil
}

func (b *BaseAdapter) DeleteBucket(ctx context.Context, bucket string, opts common.DeleteBucketOptions) error {
	if common.IsReservedOrInvalidBucket(bucket, false) {
		return fmt.Errorf("Invalid bucket name: %s", bucket)
	}
	bucketDir, err := b.getBucketDir(ctx, bucket)
	if err != nil {
		return err
	}
	return os.Remove(bucketDir)
}

// todo - ListObjects
func (b *BaseAdapter) ListObjects(ctx context.Context, bucket, prefix, marker, delimiter string, maxKeys int) (common.ListObjectsInfo, error) {
	return common.ListObjectsInfo{}, nil
}

// todo - ListObjectsV2
func (b *BaseAdapter) ListObjectsV2(ctx context.Context, bucket, prefix, continuationToken, delimiter string, maxKeys int, fetchOwner bool, startAfter string) (common.ListObjectsV2Info, error) {
	return common.ListObjectsV2Info{}, nil
}
