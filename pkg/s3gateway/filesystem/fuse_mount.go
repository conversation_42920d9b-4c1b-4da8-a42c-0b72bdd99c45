package filesystem

import (
	"context"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"sync"
	"syscall"
	"time"

	"xiaoshiai.cn/common/log"
)

// FuseMountManager manages FUSE mounts for different filesystem types
type FuseMountManager struct {
	mounts map[string]*MountInfo
	mutex  sync.RWMutex
}

type MountInfo struct {
	MountPath   string
	SourcePath  string
	FsType      string
	Options     []string
	Process     *os.Process
	Mounted     bool
	CreatedAt   time.Time
}

var globalMountManager = &FuseMountManager{
	mounts: make(map[string]*MountInfo),
}

// GetMountManager returns the global mount manager instance
func GetMountManager() *FuseMountManager {
	return globalMountManager
}

// MountCephFS mounts CephFS using ceph-fuse
func (m *FuseMountManager) MountCephFS(ctx context.Context, config *CephfsConfig) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if config.MountPath == "" {
		return fmt.Errorf("mount path is required")
	}

	// Check if already mounted
	if info, exists := m.mounts[config.MountPath]; exists && info.Mounted {
		return nil
	}

	// Create mount point if it doesn't exist
	if err := os.MkdirAll(config.MountPath, 0755); err != nil {
		return fmt.Errorf("failed to create mount point: %w", err)
	}

	// Build ceph-fuse command
	args := []string{
		config.MountPath,
		"-f", // foreground mode
	}

	// Add monitor addresses
	if len(config.MonitorAddresses) > 0 {
		for _, addr := range config.MonitorAddresses {
			args = append(args, "-m", addr)
		}
	}

	// Add authentication
	if config.Username != "" {
		args = append(args, "--id", config.Username)
	}

	// Add CephFS path
	if config.CephfsPath != "" && config.CephfsPath != "/" {
		args = append(args, "-r", config.CephfsPath)
	}

	// Add FUSE options
	if config.AllowOther {
		args = append(args, "-o", "allow_other")
	}
	if config.DefaultPermissions {
		args = append(args, "-o", "default_permissions")
	}

	log.FromContext(ctx).Info("Mounting CephFS", "command", "ceph-fuse", "args", args)

	cmd := exec.CommandContext(ctx, "ceph-fuse", args...)
	if err := cmd.Start(); err != nil {
		return fmt.Errorf("failed to start ceph-fuse: %w", err)
	}

	// Wait a moment for mount to complete
	time.Sleep(2 * time.Second)

	// Verify mount
	if !isMounted(config.MountPath) {
		if cmd.Process != nil {
			cmd.Process.Kill()
		}
		return fmt.Errorf("ceph-fuse mount failed")
	}

	m.mounts[config.MountPath] = &MountInfo{
		MountPath:  config.MountPath,
		SourcePath: fmt.Sprintf("cephfs://%v", config.MonitorAddresses),
		FsType:     "cephfs",
		Process:    cmd.Process,
		Mounted:    true,
		CreatedAt:  time.Now(),
	}

	return nil
}

// MountNFS mounts NFS using standard mount command
func (m *FuseMountManager) MountNFS(ctx context.Context, config *NFSConfig) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if config.MountPath == "" || config.ServerPath == "" {
		return fmt.Errorf("mount path and server path are required")
	}

	// Check if already mounted
	if info, exists := m.mounts[config.MountPath]; exists && info.Mounted {
		return nil
	}

	// Create mount point if it doesn't exist
	if err := os.MkdirAll(config.MountPath, 0755); err != nil {
		return fmt.Errorf("failed to create mount point: %w", err)
	}

	// Build mount options
	options := []string{
		"rw",
		fmt.Sprintf("vers=%s", config.Version),
	}

	if config.ReadSize > 0 {
		options = append(options, fmt.Sprintf("rsize=%d", config.ReadSize))
	}
	if config.WriteSize > 0 {
		options = append(options, fmt.Sprintf("wsize=%d", config.WriteSize))
	}
	if config.Timeout > 0 {
		options = append(options, fmt.Sprintf("timeo=%d", config.Timeout))
	}

	optionsStr := ""
	if len(options) > 0 {
		optionsStr = "-o"
		for i, opt := range options {
			if i > 0 {
				optionsStr += ","
			}
			optionsStr += opt
		}
	}

	args := []string{
		"-t", "nfs",
	}
	if optionsStr != "" {
		args = append(args, optionsStr)
	}
	args = append(args, config.ServerPath, config.MountPath)

	log.FromContext(ctx).Info("Mounting NFS", "command", "mount", "args", args)

	cmd := exec.CommandContext(ctx, "mount", args...)
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("failed to mount NFS: %w", err)
	}

	// Verify mount
	if !isMounted(config.MountPath) {
		return fmt.Errorf("NFS mount failed")
	}

	m.mounts[config.MountPath] = &MountInfo{
		MountPath:  config.MountPath,
		SourcePath: config.ServerPath,
		FsType:     "nfs",
		Mounted:    true,
		CreatedAt:  time.Now(),
	}

	return nil
}

// MountJuiceFS mounts JuiceFS using juicefs command
func (m *FuseMountManager) MountJuiceFS(ctx context.Context, config *JuicefsConfig) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if config.MountPath == "" || config.MetaURL == "" {
		return fmt.Errorf("mount path and meta URL are required")
	}

	// Check if already mounted
	if info, exists := m.mounts[config.MountPath]; exists && info.Mounted {
		return nil
	}

	// Create mount point if it doesn't exist
	if err := os.MkdirAll(config.MountPath, 0755); err != nil {
		return fmt.Errorf("failed to create mount point: %w", err)
	}

	// Build juicefs mount command
	args := []string{
		"mount",
		config.MetaURL,
		config.MountPath,
		"-d", // daemon mode
	}

	// Add cache options
	if config.CacheDir != "" {
		args = append(args, "--cache-dir", config.CacheDir)
	}
	if config.CacheSize != "" {
		args = append(args, "--cache-size", config.CacheSize)
	}

	// Add other options
	if config.AllowOther {
		args = append(args, "-o", "allow_other")
	}

	log.FromContext(ctx).Info("Mounting JuiceFS", "command", "juicefs", "args", args)

	cmd := exec.CommandContext(ctx, "juicefs", args...)
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("failed to mount JuiceFS: %w", err)
	}

	// Wait a moment for mount to complete
	time.Sleep(2 * time.Second)

	// Verify mount
	if !isMounted(config.MountPath) {
		return fmt.Errorf("JuiceFS mount failed")
	}

	m.mounts[config.MountPath] = &MountInfo{
		MountPath:  config.MountPath,
		SourcePath: config.MetaURL,
		FsType:     "juicefs",
		Mounted:    true,
		CreatedAt:  time.Now(),
	}

	return nil
}

// Unmount unmounts a filesystem
func (m *FuseMountManager) Unmount(mountPath string) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	info, exists := m.mounts[mountPath]
	if !exists {
		return fmt.Errorf("mount not found: %s", mountPath)
	}

	// Try graceful unmount first
	cmd := exec.Command("umount", mountPath)
	if err := cmd.Run(); err != nil {
		// Try force unmount
		cmd = exec.Command("umount", "-f", mountPath)
		if err := cmd.Run(); err != nil {
			return fmt.Errorf("failed to unmount %s: %w", mountPath, err)
		}
	}

	// Kill process if it exists
	if info.Process != nil {
		info.Process.Kill()
	}

	delete(m.mounts, mountPath)
	return nil
}

// isMounted checks if a path is mounted
func isMounted(path string) bool {
	// Check if the path is a mount point by comparing device IDs
	pathInfo, err := os.Stat(path)
	if err != nil {
		return false
	}

	parentPath := filepath.Dir(path)
	parentInfo, err := os.Stat(parentPath)
	if err != nil {
		return false
	}

	pathStat := pathInfo.Sys().(*syscall.Stat_t)
	parentStat := parentInfo.Sys().(*syscall.Stat_t)

	return pathStat.Dev != parentStat.Dev
}

// ListMounts returns all current mounts
func (m *FuseMountManager) ListMounts() map[string]*MountInfo {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	result := make(map[string]*MountInfo)
	for k, v := range m.mounts {
		result[k] = v
	}
	return result
}
