package filesystem

import (
	"fmt"
	"log"
)

// ExampleMountCephFS demonstrates how to mount CephFS
func ExampleMountCephFS() {
	config := &CephfsConfig{
		MonitorAddresses: []string{
			"************:6789",
			"************:6789",
			"************:6789",
		},
		Username:           "admin",
		<PERSON><PERSON>ey:          "your-ceph-secret-key",
		MountPath:          "/mnt/cephfs",
		CephfsPath:         "/",
		AllowOther:         true,
		DefaultPermissions: true,
	}

	adapter, err := NewCephfsAdapter(config)
	if err != nil {
		log.Fatalf("Failed to create CephFS adapter: %v", err)
	}

	fmt.Printf("CephFS mounted successfully at %s\n", config.MountPath)
	fmt.Printf("Adapter type: %s\n", adapter.GetType())
}

// ExampleMountNFS demonstrates how to mount NFS
func ExampleMountNFS() {
	config := &NFSConfig{
		ServerPath: "************0:/export/data",
		MountPath:  "/mnt/nfs",
		Version:    "4.1",
		ReadSize:   65536,
		WriteSize:  65536,
		Timeout:    600,
		ReadOnly:   false,
		Soft:       false,
	}

	adapter, err := NewNFSAdapter(config)
	if err != nil {
		log.Fatalf("Failed to create NFS adapter: %v", err)
	}

	fmt.Printf("NFS mounted successfully at %s\n", config.MountPath)
	fmt.Printf("Adapter type: %s\n", adapter.GetType())
}

// ExampleMountJuiceFS demonstrates how to mount JuiceFS
func ExampleMountJuiceFS() {
	config := &JuicefsConfig{
		MetaURL:    "redis://localhost:6379/1",
		MountPath:  "/mnt/juicefs",
		CacheDir:   "/var/cache/juicefs",
		CacheSize:  "100G",
		AllowOther: true,
		ReadOnly:   false,
	}

	adapter, err := NewJuicefsAdapter(config)
	if err != nil {
		log.Fatalf("Failed to create JuiceFS adapter: %v", err)
	}

	fmt.Printf("JuiceFS mounted successfully at %s\n", config.MountPath)
	fmt.Printf("Adapter type: %s\n", adapter.GetType())
}

// ExampleListMounts demonstrates how to list all current mounts
func ExampleListMounts() {
	mountManager := GetMountManager()
	mounts := mountManager.ListMounts()

	fmt.Println("Current mounts:")
	for mountPath, info := range mounts {
		fmt.Printf("  %s -> %s (%s) [%s]\n",
			mountPath, info.SourcePath, info.FsType, info.CreatedAt.Format("2006-01-02 15:04:05"))
	}
}

// ExampleUnmount demonstrates how to unmount a filesystem
func ExampleUnmount() {
	mountManager := GetMountManager()

	// Unmount a specific path
	mountPath := "/mnt/cephfs"
	if err := mountManager.Unmount(mountPath); err != nil {
		log.Printf("Failed to unmount %s: %v", mountPath, err)
	} else {
		fmt.Printf("Successfully unmounted %s\n", mountPath)
	}
}

// ExampleFullWorkflow demonstrates a complete workflow
func ExampleFullWorkflow() {

	// 1. Mount CephFS
	cephConfig := &CephfsConfig{
		MonitorAddresses: []string{"************:6789"},
		Username:         "admin",
		MountPath:        "/mnt/cephfs",
		CephfsPath:       "/data",
		AllowOther:       true,
	}

	cephAdapter, err := NewCephfsAdapter(cephConfig)
	if err != nil {
		log.Printf("Failed to mount CephFS: %v", err)
		return
	}

	// 2. Mount NFS
	nfsConfig := &NFSConfig{
		ServerPath: "************0:/export/shared",
		MountPath:  "/mnt/nfs",
		Version:    "4",
	}

	nfsAdapter, err := NewNFSAdapter(nfsConfig)
	if err != nil {
		log.Printf("Failed to mount NFS: %v", err)
		return
	}

	// 3. Mount JuiceFS
	juiceConfig := &JuicefsConfig{
		MetaURL:   "redis://localhost:6379/1",
		MountPath: "/mnt/juicefs",
		CacheSize: "50G",
	}

	juiceAdapter, err := NewJuicefsAdapter(juiceConfig)
	if err != nil {
		log.Printf("Failed to mount JuiceFS: %v", err)
		return
	}

	// 4. List all mounts
	fmt.Println("All filesystems mounted successfully:")
	fmt.Printf("- CephFS: %s (%s)\n", cephConfig.MountPath, cephAdapter.GetType())
	fmt.Printf("- NFS: %s (%s)\n", nfsConfig.MountPath, nfsAdapter.GetType())
	fmt.Printf("- JuiceFS: %s (%s)\n", juiceConfig.MountPath, juiceAdapter.GetType())

	// 5. Use the adapters for S3 operations (placeholder)
	// In real usage, these adapters would be used by your S3 gateway
	// to perform bucket and object operations on the mounted filesystems

	fmt.Println("Filesystems are ready for S3 operations!")
}

// Configuration examples for different scenarios

// ProductionCephFSConfig returns a production-ready CephFS configuration
func ProductionCephFSConfig() *CephfsConfig {
	return &CephfsConfig{
		MonitorAddresses: []string{
			"ceph-mon-1.example.com:6789",
			"ceph-mon-2.example.com:6789",
			"ceph-mon-3.example.com:6789",
		},
		Username:           "s3gateway",
		SecretKey:          "your-production-secret",
		MountPath:          "/mnt/cephfs-s3",
		CephfsPath:         "/s3-buckets",
		AllowOther:         true,
		DefaultPermissions: true,
	}
}

// ProductionNFSConfig returns a production-ready NFS configuration
func ProductionNFSConfig() *NFSConfig {
	return &NFSConfig{
		ServerPath: "nfs-server.example.com:/export/s3-storage",
		MountPath:  "/mnt/nfs-s3",
		Version:    "4.1",
		ReadSize:   1048576, // 1MB
		WriteSize:  1048576, // 1MB
		Timeout:    600,     // 60 seconds
		ReadOnly:   false,
		Soft:       false,
	}
}

// ProductionJuiceFSConfig returns a production-ready JuiceFS configuration
func ProductionJuiceFSConfig() *JuicefsConfig {
	return &JuicefsConfig{
		MetaURL:    "redis://redis-cluster.example.com:6379/0",
		MountPath:  "/mnt/juicefs-s3",
		CacheDir:   "/var/cache/juicefs",
		CacheSize:  "500G",
		AllowOther: true,
		ReadOnly:   false,
	}
}
