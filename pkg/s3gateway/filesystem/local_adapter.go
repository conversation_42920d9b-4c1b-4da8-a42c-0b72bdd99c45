package filesystem

import (
	"context"
	"net/http"

	"xiaoshiai.cn/rune/pkg/s3gateway/common"
)

type LocalConfig struct {
	FSPath string
}

var _ FilesystemAdapter = (*LocalAdapter)(nil)

type LocalAdapter struct {
	*BaseAdapter
	config *LocalConfig
}

// DeleteBucket implements FilesystemAdapter.
func (l *LocalAdapter) DeleteBucket(ctx context.Context, bucket string, opts common.DeleteBucketOptions) error {
	panic("unimplemented")
}

// GetBucketInfo implements FilesystemAdapter.
func (l *LocalAdapter) GetBucketInfo(ctx context.Context, bucket string) (bucketInfo common.BucketInfo, err error) {
	panic("unimplemented")
}

// GetObjectNInfo implements FilesystemAdapter.
func (l *LocalAdapter) GetObjectNInfo(ctx context.Context, bucket string, object string, rs *common.HTTPRangeSpec, h http.Header, lockType LockType, opts common.ObjectOptions) (reader *common.GetObjectReader, err error) {
	panic("unimplemented")
}

// ListBuckets implements FilesystemAdapter.
func (l *LocalAdapter) ListBuckets(ctx context.Context) (buckets []common.BucketInfo, err error) {
	panic("unimplemented")
}

// ListObjectVersions implements FilesystemAdapter.
func (l *LocalAdapter) ListObjectVersions(ctx context.Context, bucket string, prefix string, marker string, versionMarker string, delimiter string, maxKeys int) (result common.ListObjectVersionsInfo, err error) {
	panic("unimplemented")
}

// ListObjects implements FilesystemAdapter.
func (l *LocalAdapter) ListObjects(ctx context.Context, bucket string, prefix string, marker string, delimiter string, maxKeys int) (result common.ListObjectsInfo, err error) {
	panic("unimplemented")
}

// ListObjectsV2 implements FilesystemAdapter.
func (l *LocalAdapter) ListObjectsV2(ctx context.Context, bucket string, prefix string, continuationToken string, delimiter string, maxKeys int, fetchOwner bool, startAfter string) (result common.ListObjectsV2Info, err error) {
	panic("unimplemented")
}

// MakeBucketWithLocation implements FilesystemAdapter.
func (l *LocalAdapter) MakeBucketWithLocation(ctx context.Context, bucket string, opts common.BucketOptions) error {
	panic("unimplemented")
}

// Walk implements FilesystemAdapter.
func (l *LocalAdapter) Walk(ctx context.Context, bucket string, prefix string, results chan<- common.ObjectInfo, opts common.ObjectOptions) error {
	panic("unimplemented")
}

// GetObjectInfo implements FilesystemAdapter.
func (l *LocalAdapter) GetObjectInfo(ctx context.Context, bucket string, object string, opts common.ObjectOptions) (objInfo common.ObjectInfo, err error) {
	panic("unimplemented")
}

func NewLocalAdapter(config *LocalConfig) (FilesystemAdapter, error) {
	return &LocalAdapter{
		BaseAdapter: &BaseAdapter{},
		config:      config,
	}, nil
}

// GetType implements FilesystemAdapter.
func (l *LocalAdapter) GetType() FilesystemType {
	return FilesystemTypeLocal
}
