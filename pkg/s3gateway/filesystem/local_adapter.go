package filesystem

import (
	"context"
	"net/http"
	"time"

	"xiaoshiai.cn/rune/pkg/s3gateway/common"
)

type LocalConfig struct {
	FSPath string
}

var _ FilesystemAdapter = (*LocalAdapter)(nil)

type LocalAdapter struct {
	*BaseAdapter
	config *LocalConfig
}

// GetMountStatus implements FilesystemAdapter.
func (l *LocalAdapter) GetMountStatus() (common.MountStatus, error) {
	panic("unimplemented")
}

// IsMounted implements FilesystemAdapter.
func (l *LocalAdapter) IsMounted() bool {
	panic("unimplemented")
}

// Mount implements FilesystemAdapter.
func (l *LocalAdapter) Mount(timeout time.Duration) error {
	panic("unimplemented")
}

// Unmount implements FilesystemAdapter.
func (l *LocalAdapter) Unmount(timeout time.Duration, force bool) error {
	panic("unimplemented")
}

// AbortMultipartUpload implements FilesystemAdapter.
func (l *LocalAdapter) AbortMultipartUpload(ctx context.Context, bucket string, object string, uploadID string, opts common.ObjectOptions) error {
	panic("unimplemented")
}

// CompleteMultipartUpload implements FilesystemAdapter.
func (l *LocalAdapter) CompleteMultipartUpload(ctx context.Context, bucket string, object string, uploadID string, uploadedParts []common.CompletePart, opts common.ObjectOptions) (objInfo common.ObjectInfo, err error) {
	panic("unimplemented")
}

// CopyObject implements FilesystemAdapter.
func (l *LocalAdapter) CopyObject(ctx context.Context, srcBucket string, srcObject string, destBucket string, destObject string, srcInfo common.ObjectInfo, srcOpts common.ObjectOptions, dstOpts common.ObjectOptions) (objInfo common.ObjectInfo, err error) {
	panic("unimplemented")
}

// CopyObjectPart implements FilesystemAdapter.
func (l *LocalAdapter) CopyObjectPart(ctx context.Context, srcBucket string, srcObject string, destBucket string, destObject string, uploadID string, partID int, startOffset int64, length int64, srcInfo common.ObjectInfo, srcOpts common.ObjectOptions, dstOpts common.ObjectOptions) (info common.PartInfo, err error) {
	panic("unimplemented")
}

// DeleteObject implements FilesystemAdapter.
func (l *LocalAdapter) DeleteObject(ctx context.Context, bucket string, object string, opts common.ObjectOptions) (common.ObjectInfo, error) {
	panic("unimplemented")
}

// DeleteObjects implements FilesystemAdapter.
func (l *LocalAdapter) DeleteObjects(ctx context.Context, bucket string, objects []common.ObjectToDelete, opts common.ObjectOptions) ([]common.DeletedObject, []error) {
	panic("unimplemented")
}

// GetMultipartInfo implements FilesystemAdapter.
func (l *LocalAdapter) GetMultipartInfo(ctx context.Context, bucket string, object string, uploadID string, opts common.ObjectOptions) (info common.MultipartInfo, err error) {
	panic("unimplemented")
}

// ListMultipartUploads implements FilesystemAdapter.
func (l *LocalAdapter) ListMultipartUploads(ctx context.Context, bucket string, prefix string, keyMarker string, uploadIDMarker string, delimiter string, maxUploads int) (result common.ListMultipartsInfo, err error) {
	panic("unimplemented")
}

// ListObjectParts implements FilesystemAdapter.
func (l *LocalAdapter) ListObjectParts(ctx context.Context, bucket string, object string, uploadID string, partNumberMarker int, maxParts int, opts common.ObjectOptions) (result common.ListPartsInfo, err error) {
	panic("unimplemented")
}

// NewMultipartUpload implements FilesystemAdapter.
func (l *LocalAdapter) NewMultipartUpload(ctx context.Context, bucket string, object string, opts common.ObjectOptions) (uploadID string, err error) {
	panic("unimplemented")
}

// PutObject implements FilesystemAdapter.
func (l *LocalAdapter) PutObject(ctx context.Context, bucket string, object string, data *common.PutObjReader, opts common.ObjectOptions) (objInfo common.ObjectInfo, err error) {
	panic("unimplemented")
}

// PutObjectPart implements FilesystemAdapter.
func (l *LocalAdapter) PutObjectPart(ctx context.Context, bucket string, object string, uploadID string, partID int, data *common.PutObjReader, opts common.ObjectOptions) (info common.PartInfo, err error) {
	panic("unimplemented")
}

// GetMountPoint implements FilesystemAdapter.
func (l *LocalAdapter) GetMountPoint() string {
	panic("unimplemented")
}

// GetName implements FilesystemAdapter.
func (l *LocalAdapter) GetName() string {
	panic("unimplemented")
}

// DeleteBucket implements FilesystemAdapter.
func (l *LocalAdapter) DeleteBucket(ctx context.Context, bucket string, opts common.DeleteBucketOptions) error {
	panic("unimplemented")
}

// GetBucketInfo implements FilesystemAdapter.
func (l *LocalAdapter) GetBucketInfo(ctx context.Context, bucket string) (bucketInfo common.BucketInfo, err error) {
	panic("unimplemented")
}

// GetObjectNInfo implements FilesystemAdapter.
func (l *LocalAdapter) GetObjectNInfo(ctx context.Context, bucket string, object string, rs *common.HTTPRangeSpec, h http.Header, lockType LockType, opts common.ObjectOptions) (reader *common.GetObjectReader, err error) {
	panic("unimplemented")
}

// ListBuckets implements FilesystemAdapter.
func (l *LocalAdapter) ListBuckets(ctx context.Context) (buckets []common.BucketInfo, err error) {
	panic("unimplemented")
}

// ListObjectVersions implements FilesystemAdapter.
func (l *LocalAdapter) ListObjectVersions(ctx context.Context, bucket string, prefix string, marker string, versionMarker string, delimiter string, maxKeys int) (result common.ListObjectVersionsInfo, err error) {
	panic("unimplemented")
}

// ListObjects implements FilesystemAdapter.
func (l *LocalAdapter) ListObjects(ctx context.Context, bucket string, prefix string, marker string, delimiter string, maxKeys int) (result common.ListObjectsInfo, err error) {
	panic("unimplemented")
}

// ListObjectsV2 implements FilesystemAdapter.
func (l *LocalAdapter) ListObjectsV2(ctx context.Context, bucket string, prefix string, continuationToken string, delimiter string, maxKeys int, fetchOwner bool, startAfter string) (result common.ListObjectsV2Info, err error) {
	panic("unimplemented")
}

// MakeBucketWithLocation implements FilesystemAdapter.
func (l *LocalAdapter) MakeBucketWithLocation(ctx context.Context, bucket string, opts common.BucketOptions) error {
	panic("unimplemented")
}

// Walk implements FilesystemAdapter.
func (l *LocalAdapter) Walk(ctx context.Context, bucket string, prefix string, results chan<- common.ObjectInfo, opts common.ObjectOptions) error {
	panic("unimplemented")
}

// GetObjectInfo implements FilesystemAdapter.
func (l *LocalAdapter) GetObjectInfo(ctx context.Context, bucket string, object string, opts common.ObjectOptions) (objInfo common.ObjectInfo, err error) {
	panic("unimplemented")
}

func NewLocalAdapter(config *LocalConfig) (FilesystemAdapter, error) {
	return &LocalAdapter{
		BaseAdapter: &BaseAdapter{},
		config:      config,
	}, nil
}

// GetType implements FilesystemAdapter.
func (l *LocalAdapter) GetType() FilesystemType {
	return FilesystemTypeLocal
}
