package filesystem

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"sync"
	"time"

	clientv3 "go.etcd.io/etcd/client/v3"
	"xiaoshiai.cn/common/log"
)

const (
	defaultDialTimeout    = 5 * time.Second
	defaultDialKeepAlive  = 30 * time.Second
	defaultMountTimeout   = 10 * time.Second
	defaultUnmountTimeout = 10 * time.Second
)

type FilesystemManager struct {
	adapters   map[string]FilesystemAdapter // 存储集群名称
	etcdclient *clientv3.Client
	prefix     string
	mutex      sync.RWMutex
	ctx        context.Context
	cancel     context.CancelFunc
}

func NewFilesystemManager(address []string, pathprefix string) (*FilesystemManager, error) {
	if !strings.HasSuffix(pathprefix, "/") {
		pathprefix = pathprefix + "/"
	}
	etcdConf := EtcdConfig{
		Enabled:    true,
		PathPrefix: pathprefix,
		Config: clientv3.Config{
			DialTimeout:       defaultDialTimeout,
			DialKeepAliveTime: defaultDialKeepAlive,
			Endpoints:         address,
		},
	}
	etcdclient, err := NewEtcdClient(etcdConf)
	if err != nil {
		return nil, err
	}
	ctx, cancel := context.WithCancel(context.Background())
	fsm := &FilesystemManager{
		adapters:   make(map[string]FilesystemAdapter),
		etcdclient: etcdclient,
		prefix:     pathprefix,
		ctx:        ctx,
		cancel:     cancel,
	}
	err = fsm.loadExistingFilesystems()
	if err != nil {
		return nil, err
	}
	return fsm, nil
}

func (fm *FilesystemManager) Start() error {
	log.FromContext(fm.ctx).Info("Starting filesystem manager")
	if err := fm.loadExistingFilesystems(); err != nil {
		return fmt.Errorf("failed to load existing filesystems: %w", err)
	}
	go fm.watchFilesystemChanges()
	return nil
}

func (fm *FilesystemManager) Stop() error {
	log.FromContext(fm.ctx).Info("Stopping filesystem manager")
	fm.cancel()
	fm.mutex.Lock()
	defer fm.mutex.Unlock()

	for name, adapter := range fm.adapters {
		if err := fm.unmountFilesystem(name, adapter); err != nil {
			log.FromContext(fm.ctx).Error(err, "Failed to unmount filesystem", "name", name)
		}
	}
	fm.adapters = make(map[string]FilesystemAdapter)
	return nil
}

// todo - 通过请求中用户签名，转到用户所在的文件系统的实现
func (fm *FilesystemManager) ForwardToFilesystemAdapter(r *http.Request) (FilesystemAdapter, error) {
	return nil, nil
}

func (fm *FilesystemManager) GetAdapter(name string) (FilesystemAdapter, bool) {
	fm.mutex.RLock()
	defer fm.mutex.RUnlock()

	adapter, exists := fm.adapters[name]
	return adapter, exists
}

func (fm *FilesystemManager) ListAdapters() map[string]FilesystemAdapter {
	fm.mutex.RLock()
	defer fm.mutex.RUnlock()

	result := make(map[string]FilesystemAdapter)
	for name, adapter := range fm.adapters {
		result[name] = adapter
	}
	return result
}

func (fm *FilesystemManager) AddFilesystem(fs FilesystemAdapter) error {
	return fm.addFilesystem(fs)
}

func (fm *FilesystemManager) addFilesystem(fs FilesystemAdapter) error {
	_, err := fs.GetMountStatus()
	if err != nil {
		return fmt.Errorf("add filesystem failed,because of get mount status: %w", err)
	}
	fm.mutex.Lock()
	defer fm.mutex.Unlock()
	if _, exists := fm.adapters[fs.GetName()]; exists {
		return nil
	}
	fm.adapters[fs.GetName()] = fs
	log.FromContext(fm.ctx).Info("Created filesystem adapter", "name", fs.GetName(), "type", fs.GetType())
	return nil
}

func (fm *FilesystemManager) RemoveFilesystem(name string) error {
	fm.mutex.Lock()
	defer fm.mutex.Unlock()

	adapter, exists := fm.adapters[name]
	if !exists {
		return fmt.Errorf("filesystem %s not found", name)
	}

	if err := fm.unmountFilesystem(name, adapter); err != nil {
		return fmt.Errorf("failed to unmount filesystem %s: %w", name, err)
	}

	delete(fm.adapters, name)
	log.FromContext(fm.ctx).Info("Removed filesystem adapter", "name", name)
	return nil
}

func (fm *FilesystemManager) loadExistingFilesystems() error {
	resp, err := fm.etcdclient.Get(fm.ctx, fm.prefix, clientv3.WithPrefix())
	if err != nil {
		return err
	}
	if resp.Count == 0 {
		return nil
	}
	for _, ev := range resp.Kvs {
		key := string(ev.Key)
		fsa, errParse := ParseFilesystemAdapterFromBytes(ev.Value)
		if errParse != nil {
			return errParse
		}
		if strings.TrimPrefix(key, fm.prefix) != fsa.GetName() {
			return fmt.Errorf("filesystem name %s not match etcd key %s", fsa.GetName(), key)
		}
		if err := fsa.Mount(defaultMountTimeout); err != nil {
			return err
		}
		if err := fm.addFilesystem(fsa); err != nil {
			return err
		}
	}
	log.FromContext(fm.ctx).Info("Loading existing filesystem configurations")
	return nil
}
func (fm *FilesystemManager) unmountFilesystem(name string, adapter FilesystemAdapter) error {
	if !adapter.IsMounted() {
		return nil
	}
	if err := adapter.Unmount(defaultUnmountTimeout, false); err != nil {
		log.FromContext(fm.ctx).Info("failed to unmount filesystem", "name", name, "type", adapter.GetType())
		if err1 := adapter.Unmount(defaultUnmountTimeout, true); err1 != nil {
			return fmt.Errorf("failed to force unmount name:%s,type:%s,error: %w", name, adapter.GetType(), err)
		}
	}
	return nil
}

func (fm *FilesystemManager) watchFilesystemChanges() {
	ch := fm.watch(fm.ctx)
	for event := range ch {
		switch event.Type {
		case storageClusterEventCreate:
			fsa := event.Adapter
			if err := fsa.Mount(defaultMountTimeout); err != nil {
				log.FromContext(fm.ctx).Error(err, "failed to mount filesystem")
				continue
			}
			if err := fm.addFilesystem(fsa); err != nil {
				log.FromContext(fm.ctx).Error(err, "failed to add filesystem")
				continue
			}
		case storageClusterEventDelete:
			name := event.Name
			if err := fm.RemoveFilesystem(name); err != nil {
				log.FromContext(fm.ctx).Error(err, "failed to remove filesystem", "name", name)
			}
		}
	}
}

type storageClusterEventType string

const (
	storageClusterEventCreate storageClusterEventType = "create"
	storageClusterEventDelete storageClusterEventType = "delete"
)

type storageClusterEvent struct {
	Type    storageClusterEventType
	Name    string
	Adapter FilesystemAdapter
}

func (fm *FilesystemManager) watch(ctx context.Context) <-chan storageClusterEvent {
	ch := make(chan storageClusterEvent)
	go func() {
		defer close(ch)
		for {
		outerLoop:
			watchCh := fm.etcdclient.Watch(ctx, fm.prefix, clientv3.WithPrefix())
			for {
				select {
				case <-ctx.Done():
					return
				case watchResp, ok := <-watchCh:
					if !ok {
						time.Sleep(time.Second)
						goto outerLoop
					}
					if err := watchResp.Err(); err != nil {
						log.FromContext(fm.ctx).Error(err, "watch storagecluster")
						time.Sleep(time.Second)
						goto outerLoop
					}
					for _, event := range watchResp.Events {
						if event.IsCreate() {
							fsa, err := ParseFilesystemAdapterFromBytes(event.Kv.Value)
							if err != nil {
								log.FromContext(fm.ctx).Error(err, "failed to parse filesystem adapter")
								continue
							}
							ch <- storageClusterEvent{
								Type:    storageClusterEventCreate,
								Name:    fsa.GetName(),
								Adapter: fsa,
							}

						} else if event.Type == clientv3.EventTypeDelete {
							name := strings.TrimPrefix(string(event.Kv.Key), fm.prefix)
							ch <- storageClusterEvent{
								Name: name,
							}
						} else {
							log.FromContext(fm.ctx).Info("ignore storagecluster update")
						}
					}
				}
			}
		}
	}()
	return ch
}
