package filesystem

import (
	"encoding/json"
	"fmt"
)

type StorageCluster struct {
	FsType  FilesystemType `json:"fs_type"`
	Juicefs *JuicefsConfig `json:"juicefs,omitempty"`
	Local   *LocalConfig   `json:"local,omitempty"`
	Cephfs  *CephfsConfig  `json:"cephfs,omitempty"`
	NFS     *NFSConfig     `json:"nfs,omitempty"`
}

func (s StorageCluster) ToFilesystemAdapter() (FilesystemAdapter, error) {
	var (
		adapter FilesystemAdapter
		err     error
	)
	switch s.FsType {
	case FilesystemTypeJuiceFS:
		adapter, err = NewJuicefsAdapter(s.Juicefs)
	case FilesystemTypeLocal:
		adapter, err = NewLocalAdapter(s.Local)
	case FilesystemTypeCephFS:
		adapter, err = NewCephfsAdapter(s.Cephfs)
	case FilesystemTypeNFS:
		adapter, err = NewNFSAdapter(s.NFS)
	default:
		return nil, fmt.Errorf("unsupported filesystem type %s", s.FsType)
	}
	if err != nil {
		return nil, err
	}
	return adapter, nil
}

func ParseFilesystemAdapterFromBytes(data []byte) (FilesystemAdapter, error) {
	var storagecluster StorageCluster
	if err := json.Unmarshal(data, &storagecluster); err != nil {
		return nil, err
	}
	return storagecluster.ToFilesystemAdapter()
}
