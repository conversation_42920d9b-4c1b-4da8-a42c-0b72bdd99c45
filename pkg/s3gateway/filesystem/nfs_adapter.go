package filesystem

import (
	"context"
	"net/http"

	"xiaoshiai.cn/rune/pkg/s3gateway/common"
)

type NFSConfig struct {
}

var _ FilesystemAdapter = (*NFSAdapter)(nil)

type NFSAdapter struct {
	*BaseAdapter
	config *NFSConfig
}

// DeleteBucket implements FilesystemAdapter.
func (n *NFSAdapter) DeleteBucket(ctx context.Context, bucket string, opts common.DeleteBucketOptions) error {
	panic("unimplemented")
}

// GetBucketInfo implements FilesystemAdapter.
func (n *NFSAdapter) GetBucketInfo(ctx context.Context, bucket string) (bucketInfo common.BucketInfo, err error) {
	panic("unimplemented")
}

// GetObjectNInfo implements FilesystemAdapter.
func (n *NFSAdapter) GetObjectNInfo(ctx context.Context, bucket string, object string, rs *common.HTTPRangeSpec, h http.Header, lockType LockType, opts common.ObjectOptions) (reader *common.GetObjectReader, err error) {
	panic("unimplemented")
}

// ListBuckets implements FilesystemAdapter.
func (n *NFSAdapter) ListBuckets(ctx context.Context) (buckets []common.BucketInfo, err error) {
	panic("unimplemented")
}

// ListObjectVersions implements FilesystemAdapter.
func (n *NFSAdapter) ListObjectVersions(ctx context.Context, bucket string, prefix string, marker string, versionMarker string, delimiter string, maxKeys int) (result common.ListObjectVersionsInfo, err error) {
	panic("unimplemented")
}

// ListObjects implements FilesystemAdapter.
func (n *NFSAdapter) ListObjects(ctx context.Context, bucket string, prefix string, marker string, delimiter string, maxKeys int) (result common.ListObjectsInfo, err error) {
	panic("unimplemented")
}

// ListObjectsV2 implements FilesystemAdapter.
func (n *NFSAdapter) ListObjectsV2(ctx context.Context, bucket string, prefix string, continuationToken string, delimiter string, maxKeys int, fetchOwner bool, startAfter string) (result common.ListObjectsV2Info, err error) {
	panic("unimplemented")
}

// MakeBucketWithLocation implements FilesystemAdapter.
func (n *NFSAdapter) MakeBucketWithLocation(ctx context.Context, bucket string, opts common.BucketOptions) error {
	panic("unimplemented")
}

// Walk implements FilesystemAdapter.
func (n *NFSAdapter) Walk(ctx context.Context, bucket string, prefix string, results chan<- common.ObjectInfo, opts common.ObjectOptions) error {
	panic("unimplemented")
}

// GetObjectInfo implements FilesystemAdapter.
func (n *NFSAdapter) GetObjectInfo(ctx context.Context, bucket string, object string, opts common.ObjectOptions) (objInfo common.ObjectInfo, err error) {
	panic("unimplemented")
}

// GetType implements FilesystemAdapter.
func (n *NFSAdapter) GetType() FilesystemType {
	return FilesystemTypeNFS
}

func NewNFSAdapter(config *NFSConfig) (FilesystemAdapter, error) {
	return &NFSAdapter{
		BaseAdapter: &BaseAdapter{},
		config:      config,
	}, nil
}
