package filesystem

import (
	"context"
	"fmt"
	"net/http"

	"xiaoshiai.cn/rune/pkg/s3gateway/common"
)

type NFSConfig struct {
	// NFS connection parameters
	ServerPath string `json:"server_path"` // NFS server path (e.g., "server:/path/to/share")
	MountPath  string `json:"mount_path"`  // Local mount point
	Version    string `json:"version"`     // NFS version (3, 4, 4.1, etc.)

	// Performance options
	ReadSize  int `json:"read_size"`  // Read buffer size
	WriteSize int `json:"write_size"` // Write buffer size
	Timeout   int `json:"timeout"`    // Timeout in deciseconds

	// Mount options
	ReadOnly bool `json:"read_only"` // Mount as read-only
	Soft     bool `json:"soft"`      // Use soft mount
}

var _ FilesystemAdapter = (*NFSAdapter)(nil)

type NFSAdapter struct {
	*BaseAdapter
	config    *NFSConfig
	mountPath string
}

// DeleteBucket implements FilesystemAdapter.
func (n *NFSAdapter) DeleteBucket(ctx context.Context, bucket string, opts common.DeleteBucketOptions) error {
	panic("unimplemented")
}

// GetBucketInfo implements FilesystemAdapter.
func (n *NFSAdapter) GetBucketInfo(ctx context.Context, bucket string) (bucketInfo common.BucketInfo, err error) {
	panic("unimplemented")
}

// GetObjectNInfo implements FilesystemAdapter.
func (n *NFSAdapter) GetObjectNInfo(ctx context.Context, bucket string, object string, rs *common.HTTPRangeSpec, h http.Header, lockType LockType, opts common.ObjectOptions) (reader *common.GetObjectReader, err error) {
	panic("unimplemented")
}

// ListBuckets implements FilesystemAdapter.
func (n *NFSAdapter) ListBuckets(ctx context.Context) (buckets []common.BucketInfo, err error) {
	panic("unimplemented")
}

// ListObjectVersions implements FilesystemAdapter.
func (n *NFSAdapter) ListObjectVersions(ctx context.Context, bucket string, prefix string, marker string, versionMarker string, delimiter string, maxKeys int) (result common.ListObjectVersionsInfo, err error) {
	panic("unimplemented")
}

// ListObjects implements FilesystemAdapter.
func (n *NFSAdapter) ListObjects(ctx context.Context, bucket string, prefix string, marker string, delimiter string, maxKeys int) (result common.ListObjectsInfo, err error) {
	panic("unimplemented")
}

// ListObjectsV2 implements FilesystemAdapter.
func (n *NFSAdapter) ListObjectsV2(ctx context.Context, bucket string, prefix string, continuationToken string, delimiter string, maxKeys int, fetchOwner bool, startAfter string) (result common.ListObjectsV2Info, err error) {
	panic("unimplemented")
}

// MakeBucketWithLocation implements FilesystemAdapter.
func (n *NFSAdapter) MakeBucketWithLocation(ctx context.Context, bucket string, opts common.BucketOptions) error {
	panic("unimplemented")
}

// Walk implements FilesystemAdapter.
func (n *NFSAdapter) Walk(ctx context.Context, bucket string, prefix string, results chan<- common.ObjectInfo, opts common.ObjectOptions) error {
	panic("unimplemented")
}

// GetObjectInfo implements FilesystemAdapter.
func (n *NFSAdapter) GetObjectInfo(ctx context.Context, bucket string, object string, opts common.ObjectOptions) (objInfo common.ObjectInfo, err error) {
	panic("unimplemented")
}

// GetType implements FilesystemAdapter.
func (n *NFSAdapter) GetType() FilesystemType {
	return FilesystemTypeNFS
}

func NewNFSAdapter(config *NFSConfig) (FilesystemAdapter, error) {
	if config == nil {
		return nil, fmt.Errorf("NFS config is required")
	}

	// Set default values
	if config.Version == "" {
		config.Version = "4"
	}
	if config.MountPath == "" {
		return nil, fmt.Errorf("mount path is required")
	}
	if config.ServerPath == "" {
		return nil, fmt.Errorf("server path is required")
	}

	// Create base adapter
	baseAdapter, err := NewBaseAdapter(config.MountPath, FilesystemTypeNFS)
	if err != nil {
		return nil, fmt.Errorf("failed to create base adapter: %w", err)
	}

	adapter := &NFSAdapter{
		BaseAdapter: baseAdapter,
		config:      config,
		mountPath:   config.MountPath,
	}

	// Mount NFS using the mount manager
	mountManager := GetMountManager()
	if err := mountManager.MountNFS(context.Background(), config); err != nil {
		return nil, fmt.Errorf("failed to mount NFS: %w", err)
	}

	return adapter, nil
}
