package s3gateway

import (
	"fmt"
	"net/http"
	"time"

	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/rune/pkg/s3gateway/common"
)

const (
	maxSkewTime = 15 * time.Minute
)

var supportedS3AuthTypes = map[common.AuthType]struct{}{
	common.AuthTypeAnonymous:       {},
	common.AuthTypePresigned:       {},
	common.AuthTypePresignedV2:     {},
	common.AuthTypeSigned:          {},
	common.AuthTypeSignedV2:        {},
	common.AuthTypePostPolicy:      {},
	common.AuthTypeStreamingSigned: {},
}

func isSupportedS3AuthType(aType common.AuthType) bool {
	_, ok := supportedS3AuthTypes[aType]
	return ok
}

func S3Auth() api.Filter {
	return api.FilterFunc(func(w http.ResponseWriter, r *http.Request, next http.Handler) {
		aType := common.GetRequestAuthType(r)
		if aType == common.AuthTypeSigned || aType == common.AuthTypeSignedV2 || aType == common.AuthTypeStreamingSigned {
			amzDate, errCode := common.ParseAmzDateHeader(r)
			if errCode.Code != "" {
				common.WriteErrorResponse(r.Context(), w, errCode, r.URL)
				return
			}
			// 确认请求时间
			curTime := time.Now().UTC()
			if curTime.Sub(amzDate) > maxSkewTime || amzDate.Sub(curTime) > maxSkewTime {
				common.WriteErrorResponse(r.Context(), w, common.APIError{
					Code:           "RequestTimeTooSkewed",
					Description:    "The difference between the request time and the server's time is too large.",
					HTTPStatusCode: http.StatusForbidden,
				}, r.URL)
				return
			}
		}
		if isSupportedS3AuthType(aType) || aType == common.AuthTypeJWT || aType == common.AuthTypeSTS {
			next.ServeHTTP(w, r)
			return
		}
		common.WriteErrorResponse(r.Context(), w, common.APIError{
			Code:           "InvalidRequest",
			Description:    "The authorization mechanism you have provided is not supported. Please use AWS4-HMAC-SHA256.",
			HTTPStatusCode: http.StatusBadRequest,
		}, r.URL)

	})
}

// todo - 请求合法性校验
func S3RequestValidity() api.Filter {
	return api.FilterFunc(func(w http.ResponseWriter, r *http.Request, next http.Handler) {
		next.ServeHTTP(w, r)
	})
}

func S3CustomHeaders() api.Filter {
	return api.FilterFunc(func(w http.ResponseWriter, r *http.Request, next http.Handler) {
		header := w.Header()
		header.Set("X-XSS-Protection", "1; mode=block")                                // Prevents against XSS attacks
		header.Set("Content-Security-Policy", "block-all-mixed-content")               // prevent mixed (HTTP / HTTPS content)
		header.Set("X-Content-Type-Options", "nosniff")                                // Prevent mime-sniff
		header.Set("Strict-Transport-Security", "max-age=31536000; includeSubDomains") // HSTS mitigates variants of MITM attacks
		w.Header().Set("x-amz-request-id", fmt.Sprintf("%X", time.Now().UTC().UnixNano()))
		next.ServeHTTP(w, r)
	})
}
