package s3gateway

import (
	"context"

	"golang.org/x/sync/errgroup"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/pprof"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/rune/pkg/s3gateway/filesystem"
)

type Options struct {
	Listen         string
	AccessKey      string
	SecretKey      string
	FileSystemType string
	Juicefs        *filesystem.JuicefsConfig
	Cephfs         *filesystem.CephfsConfig
	NFS            *filesystem.NFSConfig
	Local          *filesystem.LocalConfig

	CacheEnabled bool
	CacheDrivers string
	CacheQuota   int
	CacheExclude string
}

func NewDefaultOptions() *Options {
	return &Options{
		Listen:         ":8080",
		AccessKey:      "rune-user",
		SecretKey:      "rune-password",
		FileSystemType: "nfs",
		Juicefs:        &filesystem.JuicefsConfig{},
		Cephfs:         &filesystem.CephfsConfig{},
		NFS:            &filesystem.NFSConfig{},
		Local:          &filesystem.LocalConfig{},
		CacheEnabled:   false,
		CacheDrivers:   "",
		CacheQuota:     0,
		CacheExclude:   "",
	}
}

func Run(ctx context.Context, opts *Options) error {
	eg, ctx := errgroup.WithContext(ctx)
	eg.Go(func() error {
		return runS3Gateway(ctx, opts)
	})
	eg.Go(func() error {
		return pprof.Run(ctx)
	})
	return eg.Wait()
}

func runS3Gateway(ctx context.Context, options *Options) error {
	o, err := NewObjectOperation(options)
	if err != nil {
		return err
	}
	return api.New().
		Plugin(
			api.HealthCheckPlugin{},
			api.NewAPIDocPlugin("/docs", nil),
		).
		Filter(
			api.LoggingFilter(log.FromContext(ctx)),
			api.NewCORSFilter(),
		).
		Group(
			api.NewGroup("/{bucket}").
				SubGroup(
					o.ObjectGroup(),
				),
		).
		Serve(ctx, options.Listen)
}
