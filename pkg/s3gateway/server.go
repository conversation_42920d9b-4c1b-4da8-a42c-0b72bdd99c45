package s3gateway

import (
	"context"

	"golang.org/x/sync/errgroup"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/pprof"
	"xiaoshiai.cn/common/rest/api"
)

type Options struct {
	Listen    string
	AccessKey string
	SecretKey string

	EtcdPoints []string
	PathPrefix string

	CacheEnabled bool
	CacheDrivers string
	CacheQuota   int
	CacheExclude string
}

func NewDefaultOptions() *Options {
	return &Options{
		Listen:       ":8080",
		AccessKey:    "rune-user",
		SecretKey:    "rune-password",
		PathPrefix:   "",
		CacheEnabled: false,
		CacheDrivers: "",
		CacheQuota:   0,
		CacheExclude: "",
	}
}

func Run(ctx context.Context, opts *Options) error {
	eg, ctx := errgroup.WithContext(ctx)
	eg.Go(func() error {
		return runS3Gateway(ctx, opts)
	})
	eg.Go(func() error {
		return pprof.Run(ctx)
	})
	return eg.Wait()
}

func runS3Gateway(ctx context.Context, options *Options) error {
	o, err := NewObjectHandler(options)
	if err != nil {
		return err
	}
	return api.New().
		Plugin(
			api.HealthCheckPlugin{},
			api.NewAPIDocPlugin("/docs", nil),
		).
		Filter(
			api.LoggingFilter(log.FromContext(ctx)),
			api.NewCORSFilter(),
			S3Auth(), //验证签名信息
			S3RequestValidity(),
			S3CustomHeaders(), //添加自定义Header信息
		).
		Group(
			api.NewGroup("/{bucket}").
				SubGroup(
					o.ObjectGroup(),
				),
		).
		Serve(ctx, options.Listen)
}
