package s3gateway

import (
	"errors"
	"net/http"

	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/rune/pkg/s3gateway/common"
	"xiaoshiai.cn/rune/pkg/s3gateway/filesystem"
)

type ObjectOperation struct {
	Fs filesystem.FilesystemAdapter
}

func NewObjectOperation(opt *Options) (*ObjectOperation, error) {
	var (
		fs  filesystem.FilesystemAdapter
		err error
	)
	switch filesystem.FilesystemType(opt.FileSystemType) {
	case filesystem.FilesystemTypeCephFS:
		fs, err = filesystem.NewCephfsAdapter(opt.Cephfs)
		if err != nil {
			return nil, err
		}
	case filesystem.FilesystemTypeJuiceFS:
		fs, err = filesystem.NewJuicefsAdapter(opt.Juicefs)
		if err != nil {
			return nil, err
		}
	case filesystem.FilesystemTypeLocal:
		fs, err = filesystem.NewLocalAdapter(opt.Local)
		if err != nil {
			return nil, err
		}
	case filesystem.FilesystemTypeNFS:
		fs, err = filesystem.NewNFSAdapter(opt.NFS)
		if err != nil {
			return nil, err
		}
	default:
		return nil, errors.New("unknown filesystem type")
	}

	return &ObjectOperation{
		Fs: fs,
	}, nil
}

func (o *ObjectOperation) ObjectGroup() api.Group {
	return api.
		NewGroup("/{object:.+}").
		Tag("Objects").
		Route(
			api.HEAD("").
				To(o.HeadObjectHandler),
		)
}

func bucketAndObject(r *http.Request) (string, string, error) {
	bucket := api.Path(r, "bucket", "")
	if bucket == "" {
		return "", "", errors.New("bucket name is empty")
	}
	object := api.Path(r, "object", "")
	if object == "" {
		return "", "", errors.New("object name is empty")
	}
	var err error
	object, err = common.UnescapePath(object)
	if err != nil {
		return "", "", err
	}
	return bucket, object, nil
}

func (o *ObjectOperation) HeadObjectHandler(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	bucket, object, err := bucketAndObject(r)
	if err != nil {
		common.WriteErrorResponse(ctx, w, common.APIError{
			Code:           "InvalidArgument",
			Description:    err.Error(),
			HTTPStatusCode: http.StatusBadRequest,
		}, r.URL)
		return
	}
	_, _ = bucket, object
}
