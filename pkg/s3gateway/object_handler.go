package s3gateway

import (
	"context"
	"errors"
	"net/http"

	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/rune/pkg/s3gateway/common"
	"xiaoshiai.cn/rune/pkg/s3gateway/filesystem"
)

type ObjectHandler struct {
	fsm *filesystem.FilesystemManager
}

func NewObjectHandler(opt *Options) (*ObjectHandler, error) {
	fsm, err := filesystem.NewFilesystemManager(opt.EtcdPoints, opt.PathPrefix)
	if err != nil {
		return nil, err
	}
	if err := fsm.Start(); err != nil {
		return nil, err
	}
	return &ObjectHandler{
		fsm: fsm,
	}, nil
}

func (o *ObjectHandler) ObjectGroup() api.Group {
	return api.
		NewGroup("/{object:.+}").
		Tag("Objects").
		Route(
			api.HEAD("").
				To(o.HeadObjectHandler),
		)
}

func bucketAndObject(r *http.Request) (string, string, error) {
	bucket := api.Path(r, "bucket", "")
	if bucket == "" {
		return "", "", errors.New("bucket name is empty")
	}
	object := api.Path(r, "object", "")
	if object == "" {
		return "", "", errors.New("object name is empty")
	}
	var err error
	object, err = common.UnescapePath(object)
	if err != nil {
		return "", "", err
	}
	return bucket, object, nil
}

func (o *ObjectHandler) onFilesystem(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, fs filesystem.FilesystemAdapter, bucket, object string)) {
	ctx := r.Context()
	fs, err := o.fsm.ForwardToFilesystemAdapter(r)
	if err != nil {
		common.WriteErrorResponse(ctx, w, common.APIError{
			Code:           "AccessDenied",
			Description:    err.Error(),
			HTTPStatusCode: http.StatusForbidden,
		}, r.URL)
		return
	}
	bucket, object, err := bucketAndObject(r)
	if err != nil {
		common.WriteErrorResponse(ctx, w, common.APIError{
			Code:           "InvalidArgument",
			Description:    err.Error(),
			HTTPStatusCode: http.StatusBadRequest,
		}, r.URL)
		return
	}
	fn(ctx, fs, bucket, object)
}

func (o *ObjectHandler) HeadObjectHandler(w http.ResponseWriter, r *http.Request) {
	o.onFilesystem(w, r, func(ctx context.Context, fs filesystem.FilesystemAdapter, bucket, object string) {
		_, err := fs.GetObjectInfo(ctx, bucket, object, common.ObjectOptions{})
		if err != nil {
			common.WriteErrorResponse(ctx, w, common.APIError{
				Code:           "NoSuchKey",
				Description:    err.Error(),
				HTTPStatusCode: http.StatusNotFound,
			}, r.URL)
			return
		}
		// todo - implement
		w.WriteHeader(http.StatusOK)
	})
}
