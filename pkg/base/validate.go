package base

import (
	"fmt"
	"net/http"
	"reflect"
	"regexp"
	"strings"

	"github.com/go-playground/validator/v10"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/rest/api"
)

var (
	// RestricNameRegexp is the most limited name regexp
	// it is used for tenant name.
	// 1-25 length, start with a-z, only a-z0-9
	RestrictNameRegexp = regexp.MustCompile(`^[a-z][a-z0-9]{0,24}$`)

	// DNS1123NameRegexp is the most common DNS-1123 name regexp
	// it is used for application name, service name, organization name, etc.
	// 1-25 length, start and end with a-z0-9, a-z0-9- in the middle
	DNS1123NameRegexp = regexp.MustCompile(`^[a-z0-9]([-a-z0-9]{0,23}[a-z0-9])?$`)

	// NameRegexp is the most common name regexp
	// 1-64 length, start and end with a-z0-9, a-z0-9.-_ in the middle
	NameRegexp = regexp.MustCompile(`^[a-zA-Z0-9]([a-zA-Z0-9_.-]{0,62}[a-zA-Z0-9])?$`)
)

func ValidateDNS1123Name(name string) error {
	return ValidateMatchRegexp(name, DNS1123NameRegexp)
}

func ValidateName(name string) error {
	return ValidateMatchRegexp(name, NameRegexp)
}

func ValidateRestrictName(name string) error {
	return ValidateMatchRegexp(name, RestrictNameRegexp)
}

func ValidateMatchRegexp(name string, regxp *regexp.Regexp) error {
	if !regxp.MatchString(name) {
		return errors.NewBadRequest(fmt.Sprintf("invalid name %s, must match %s", name, regxp.String()))
	}
	return nil
}

func init() {
	api.ValidateBody = NewDefaultBodyValidation()
}

func NewDefaultBodyValidation() func(r *http.Request, data any) error {
	v := validator.New()
	v.RegisterTagNameFunc(func(fld reflect.StructField) string {
		name := strings.SplitN(fld.Tag.Get("json"), ",", 2)[0]
		// skip if tag key says it should be ignored
		if name == "-" {
			return ""
		}
		return name
	})
	v.RegisterValidation("regexp", func(fl validator.FieldLevel) bool {
		if fl.Field().Kind() != reflect.String {
			return true
		}
		regxp := fl.Param()
		if regxp == "" {
			return true
		}
		if matched, _ := regexp.MatchString(regxp, fl.Field().String()); matched {
			return true
		}
		return false
	})
	v.RegisterValidation("name", func(fl validator.FieldLevel) bool {
		if fl.Field().Kind() != reflect.String {
			return true
		}
		return NameRegexp.MatchString(fl.Field().String())
	})
	v.RegisterValidation("dnsname", func(fl validator.FieldLevel) bool {
		if fl.Field().Kind() != reflect.String {
			return true
		}
		return DNS1123NameRegexp.MatchString(fl.Field().String())
	})
	v.RegisterValidation("restrictname", func(fl validator.FieldLevel) bool {
		if fl.Field().Kind() != reflect.String {
			return true
		}
		return RestrictNameRegexp.MatchString(fl.Field().String())
	})
	return func(r *http.Request, data any) error {
		// skip not struct
		rv := reflect.ValueOf(data)
		for rv.Kind() == reflect.Ptr {
			rv = rv.Elem()
		}
		if rv.Kind() != reflect.Struct {
			return nil
		}
		return v.StructCtx(r.Context(), data)
	}
}
