package base

import (
	"xiaoshiai.cn/common"
)

const (
	LabelInstance     = "app.kubernetes.io/instance"
	LabelApplication  = "app." + common.GroupPrefix + "/application"
	LabelOrganization = "app." + common.GroupPrefix + "/organization"
	LabelTenant       = "app." + common.GroupPrefix + "/tenant"
)

// cloud labels not allow to long
const (
	LabelCloudInstance     = "instance"
	LabelCloudApplication  = "app"
	LabelCloudTenant       = "tenant"
	LabelCloudOrganization = "organization"
)

const (
	LabelApplicationCategory    = "application." + common.GroupPrefix + "/category"
	LabelApplicationSubCategory = "application." + common.GroupPrefix + "/subcategory"
	LabelApplicationProduct     = "application." + common.GroupPrefix + "/product"

	// LabelApplicationNotOrignial mark that the app is not an original app
	// it does not show in the app list
	// it may be a "database" app, "gateway" app, etc.
	LabelApplicationNotOrignial = "application." + common.GroupPrefix + "/not-original"
)

const ValueTrue = "true"

const (
	// LabelClusterPrefix is the prefix of the cluster related labels
	LabelClusterPrefix = "cluster." + common.GroupPrefix

	// LabelResourcePoolPrefix is the prefix of the resource pool related labels
	LabelResourcePoolPrefix = LabelClusterPrefix + "/resourcepool-"

	// LabelPrivateNode mark that the node is a private node
	LabelPrivateNode = common.GroupPrefix + "/private-node"

	// LabelLoadbalancer mark that the node is allow to be used as loadbalancer
	LabelLoadbalancer = common.GroupPrefix + "/loadbalancer"
)

const (
	ConditionTypeExpired = "Expired"
	TaintKeyNodeExpired  = common.GroupPrefix + "/node-expired"
)

// Metadata
const (
	LabelIsPublic         = "ismc." + common.GroupPrefix + "/is-public"
	AnnotationDescription = "ismc." + common.GroupPrefix + "/description"
	AnnotationAlias       = "ismc." + common.GroupPrefix + "/alias"
	AnnotationVendor      = "ismc." + common.GroupPrefix + "/vendor"
	// AnnotationCapacity is the annotation key for the capacity of a resource.
	AnnotationCapacity = "ismc." + common.GroupPrefix + "/capacity"
)

// Product Category
const (
	ProductSubCategoryGateway  = "gateway"
	ProductSubCategoryDatabase = "database"
)

const (
	AnnotationCreator = common.GroupPrefix + "/creator"
	// AnnotationOrderID is the annotation key for the new order id
	AnnotationOrderID     = common.GroupPrefix + "/order-id"
	AnnotationOrderItemID = common.GroupPrefix + "/order-item-id"
	AnnotationSKUID       = common.GroupPrefix + "/sku-id"

	AnnotationSKUReleased = common.GroupPrefix + "/sku-released"
)

const LabelRenewalPolicy = common.GroupPrefix + "/renewal-policy"

// RenewalPolicy 续费策略
type RenewalPolicy string

const (
	// RenewalPolicyNotSupport 不续费
	RenewalPolicyDisable RenewalPolicy = "Disable"
	// RenewalPolicyAuto 自动续费
	RenewalPolicyAuto RenewalPolicy = "Auto"
	// RenewalPolicyManual 手动续费
	RenewalPolicyManual RenewalPolicy = "Manual"
)
