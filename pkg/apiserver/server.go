package apiserver

import (
	"context"
	"fmt"

	"golang.org/x/sync/errgroup"
	"xiaoshiai.cn/common/config"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/pprof"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store/mongo"
	"xiaoshiai.cn/common/version"
)

type Options struct {
	Listen         string                       `json:"listen" description:"Listen address for the OIDC CAS Shim service" default:":8080"`
	API            *APIOptions                  `json:"api,omitempty" description:"API options for the server"`
	Config         *config.DynamicConfigOptions `json:"config,omitempty" description:"Dynamic configuration options for the server"`
	Mongodb        *mongo.MongoDBOptions        `json:"mongodb,omitempty"`
	Authentication *AuthenticationOptions       `json:"authentication,omitempty"`
	Authorization  *AuthorizationOptions        `json:"authorization,omitempty"`
	Audit          *AuditOptions                `json:"audit,omitempty"`
	Service        *ServiceOptions              `json:"service,omitempty" description:"Service options, used to proxy requests to other services"`
}

type APIOptions struct {
	Prefix string `json:"prefix,omitempty" description:"API prefix for the server, default is '/v1'"`
}

func NewDefaultOptions() *Options {
	mongodb := mongo.NewDefaultMongoOptions("apiserver")
	mongodb.Address = "rune-mongodb-headless:27017"
	return &Options{
		Listen:         ":8080",
		Config:         config.NewDefaultDynamicConfigOptions("apiserver"),
		API:            &APIOptions{Prefix: "/api"},
		Mongodb:        mongodb,
		Authentication: DefaultAuthenticationOptions(),
		Authorization:  DefaultAuthorizationOptions(),
		Audit:          NewDefaultAuditOptions(),
		Service:        NewDefaultServiceOptions(),
	}
}

func Run(ctx context.Context, options *Options) error {
	deps, err := BuildDependencies(ctx, options)
	if err != nil {
		return err
	}
	eg, ctx := errgroup.WithContext(ctx)
	eg.Go(func() error {
		return RunServer(ctx, deps)
	})
	eg.Go(func() error {
		return pprof.Run(ctx)
	})
	return eg.Wait()
}

type Dependencies struct {
	Options               *Options
	Authn                 api.Authenticator
	Authz                 api.Authorizer
	Sink                  api.AuditSink
	Config                config.DynamicConfig
	ServicesRuntimeConfig *ServicesRuntimeConfig
}

func BuildDependencies(ctx context.Context, options *Options) (*Dependencies, error) {
	mongostore, err := mongo.NewMongoStorage(ctx, mongo.GlobalObjectsScheme, options.Mongodb)
	if err != nil {
		return nil, fmt.Errorf("failed to create mongodb store: %w", err)
	}
	authn, err := BuildAuthn(ctx, options.Authentication)
	if err != nil {
		return nil, err
	}
	authz, err := BuildAuthz(ctx, options.Authorization)
	if err != nil {
		return nil, err
	}
	auditsink, err := BuildAuditSink(ctx, mongostore, options.Audit)
	if err != nil {
		return nil, err
	}
	svcConfig, err := NewServicesRuntimeConfig(options.Service)
	if err != nil {
		return nil, fmt.Errorf("failed to create services config: %w", err)
	}
	return &Dependencies{
		Options:               options,
		Authn:                 authn,
		Authz:                 authz,
		Sink:                  auditsink,
		ServicesRuntimeConfig: svcConfig,
	}, nil
}

func RunServer(ctx context.Context, deps *Dependencies) error {
	svcapi := NewServiceAPI(deps)
	dynopenapi := ServiceDynamicOpenAPIGetter{Config: deps.ServicesRuntimeConfig}
	return api.New().
		Plugin(
			api.VersionPlugin{Version: version.Get()},
			api.HealthCheckPlugin{},
			api.NewAPIDocPlugin("/docs", nil),
			NewDynamicOpenAPIPlugin("/docs", dynopenapi.GetOpenAPISpec),
		).
		Filter(
			api.LoggingFilter(log.FromContext(ctx)),
			api.NewCORSFilter(),
		).
		Group(
			svcapi.Group(),
		).
		Serve(ctx, deps.Options.Listen)
}
