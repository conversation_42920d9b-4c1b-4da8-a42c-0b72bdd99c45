package apiserver

import (
	"net/http"
	"strings"

	libnet "xiaoshiai.cn/common/net"
	"xiaoshiai.cn/common/rest/api"
)

type ServiceAttributeExtractor struct {
	Prefix string // prefix for the service path, e.g. "/api/"
	Config *ServicesRuntimeConfig
}

func (s ServiceAttributeExtractor) PathStyle(r *http.Request) (*api.Attributes, error) {
	reqpath, ok := strings.CutPrefix(r.URL.Path, s.Prefix)
	if !ok {
		return nil, nil
	}
	svc, reqpath := SplitServicePath(reqpath)
	attr := s.extract(r.Method, reqpath)
	attr.Service = svc
	return attr, nil
}

func SplitServicePath(path string) (string, string) {
	if path == "" {
		return "", ""
	}
	if path[0] == '/' {
		path = path[1:] // remove leading slash
	}
	if slashIndex := strings.IndexRune(path[1:], '/') + 1; slashIndex > 0 {
		// if there is a slash after the first character, split the path
		// e.g. /iam/foo -> service: iam, path: /foo
		return path[:slashIndex], path[slashIndex:]
	}
	return "", "/" + path // if no slash found, return the whole path as service
}

func (s ServiceAttributeExtractor) HostStyle(r *http.Request) (*api.Attributes, error) {
	reqpath, ok := strings.CutPrefix(r.URL.Path, s.Prefix)
	if !ok {
		return nil, nil
	}
	method, reqpath := r.Method, reqpath
	attr := s.extract(method, reqpath)
	if host := r.Host; host != "" {
		host, _ := libnet.SplitHostPort(host)
		cfg, ok := s.Config.Hosts[host]
		if ok {
			attr.Service = cfg.ServiceName
		} else {
			// if not found, we use the host as service name
			attr.Service = host
		}
	}
	return attr, nil
}

func (s ServiceAttributeExtractor) extract(method, reqpath string) *api.Attributes {
	attr := &api.Attributes{
		Path: reqpath,
	}
	attr.Action, attr.Resources = api.DefaultRestAttributeExtractor(method, reqpath)
	if reqpath != "" {
		if idx := strings.IndexRune(reqpath, '/'); idx != -1 {
			// if there is a slash in the path, we only take the first part as service
			// e.g. /api/iam/*
			attr.Service = strings.TrimPrefix(reqpath[:idx], "/")
		}
	}
	return attr
}
