package apiserver

import (
	stderrors "errors"
	"fmt"
	"maps"
	"net"
	"net/http"
	"net/http/httputil"
	"net/url"
	"slices"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/httpclient"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/wildcard"
)

type ServiceOptions struct {
	ConfigFile string `json:"configFile,omitempty" description:"proxy config file,default is config/services.yaml"`
}

func NewDefaultServiceOptions() *ServiceOptions {
	return &ServiceOptions{
		ConfigFile: "config/services.yaml", // default config file path
	}
}

type ServicesRuntimeConfig struct {
	Services map[string]ServiceRuntimeConfig // service name -> ServiceRuntimeConfig
	Hosts    map[string]ServiceRuntimeConfig // host -> ServiceRuntimeConfig
}

type ServiceRuntimeConfig struct {
	ServiceConfig        // embedded ServiceConfig for additional fields
	ServiceName   string // name of the service, used to identify the service
	Transport     http.RoundTripper
	Target        *url.URL
}

func NewServicesRuntimeConfig(options *ServiceOptions) (*ServicesRuntimeConfig, error) {
	config, err := LoadServicesConfig(options.ConfigFile)
	if err != nil {
		return nil, fmt.Errorf("load services config %s: %w", options.ConfigFile, err)
	}
	basetp := httpclient.NewDefaultHTTPTransport()

	runtimeconfigs := &ServicesRuntimeConfig{
		Services: make(map[string]ServiceRuntimeConfig),
		Hosts:    make(map[string]ServiceRuntimeConfig),
	}
	for svcName, svcConfig := range config.Services {
		if svcConfig.Address == "" {
			svcConfig.Address = fmt.Sprintf("http://%s", svcName)
		}
		target, err := url.Parse(svcConfig.Address)
		if err != nil {
			return nil, fmt.Errorf("parse service address %s: %w", svcConfig.Address, err)
		}
		runtimeconfig := ServiceRuntimeConfig{
			ServiceConfig: svcConfig,
			ServiceName:   svcName,
			Transport:     basetp,
			Target:        target,
		}
		if config.BaseHost != "" {
			svchost := fmt.Sprintf("%s.%s", svcName, config.BaseHost)
			runtimeconfigs.Hosts[svchost] = runtimeconfig
		}
		runtimeconfigs.Services[svcName] = runtimeconfig
	}
	return runtimeconfigs, nil
}

func IsPublicServicePath(config ServiceRuntimeConfig, path string) bool {
	for _, publicPath := range config.PublicPaths {
		if wildcard.Match(publicPath, path) {
			return true
		}
	}
	return false
}

type ServiceAPI struct {
	Prefix      string
	Config      *ServicesRuntimeConfig
	Filters     api.Filters
	HostFilters api.Filters
}

func NewServiceAPI(deps *Dependencies) *ServiceAPI {
	prefix := deps.Options.API.Prefix
	attributesExtractor := ServiceAttributeExtractor{
		Config: deps.ServicesRuntimeConfig,
		Prefix: prefix,
	}
	filters := api.Filters{
		api.NewSimpleAuditFilter(deps.Sink, api.NewDefaultAuditOptions()),
		api.NewAttributeFilter(attributesExtractor.PathStyle),
		api.NewAuthenticateFilter(deps.Authn, nil),
		api.NewAuthorizationFilter(deps.Authz),
		ExtractCreationNameAttrFilter(),
	}
	hostFilter := api.Filters{
		api.NewSimpleAuditFilter(deps.Sink, api.NewDefaultAuditOptions()),
		api.NewAttributeFilter(attributesExtractor.HostStyle),
		api.NewAuthenticateFilter(deps.Authn, nil),
		api.NewAuthorizationFilter(deps.Authz),
		ExtractCreationNameAttrFilter(),
	}
	return &ServiceAPI{
		Prefix:      prefix,
		Filters:     filters,
		HostFilters: hostFilter,
		Config:      deps.ServicesRuntimeConfig,
	}
}

func (s *ServiceAPI) PathProxy(w http.ResponseWriter, r *http.Request) {
	svc, reqpath := api.Path(r, "service", ""), "/"+api.Path(r, "path", "")
	svcConfig, ok := s.Config.Services[svc]
	if !ok {
		svcConfig = ServiceRuntimeConfig{Target: &url.URL{Scheme: "http", Host: svc}}
	}
	handler := ForwardHandler{
		Config:      svcConfig,
		RequestPath: svcConfig.APIPath + reqpath,
	}
	if IsPublicServicePath(svcConfig, reqpath) {
		// If the path is public, we don't need to authenticate or authorize.
		// We can skip the filters and directly serve the request.
		handler.ServeHTTP(w, r)
	} else {
		// For non-public paths, we apply the filters.
		s.Filters.Process(w, r, handler)
	}
}

func (s *ServiceAPI) HostProxy(w http.ResponseWriter, r *http.Request) {
	reqpath := "/" + api.Path(r, "path", "")
	svcConfig, ok := s.Config.Hosts[r.Host]
	if !ok {
		http.NotFound(w, r)
		return
	}
	handler := ForwardHandler{
		Config:      svcConfig,
		RequestPath: svcConfig.APIPath + reqpath,
	}
	if IsPublicServicePath(svcConfig, reqpath) {
		// If the path is public, we don't need to authenticate or authorize.
		// We can skip the filters and directly serve the request.
		handler.ServeHTTP(w, r)
	} else {
		// For non-public paths, we apply the filters.
		s.HostFilters.Process(w, r, handler)
	}
}

type ForwardHandler struct {
	Config      ServiceRuntimeConfig
	RequestPath string
}

func (s ForwardHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	userinfo := api.AuthenticateFromContext(r.Context()).User
	if userinfo.Name != "" {
		httpclient.SetAuthProxyHeaders(r, userinfo.Name, userinfo.Groups, userinfo.Extra)
	}
	reverseProxy := &httputil.ReverseProxy{
		Transport: s.Config.Transport,
		Rewrite: func(pr *httputil.ProxyRequest) {
			pr.Out.URL.Path = s.RequestPath
			pr.SetURL(s.Config.Target)
			// keep the original host header
			pr.Out.Host = pr.In.Host
		},
		FlushInterval: -1, // immediately flush response
		ErrorHandler:  ErrorHandler,
	}
	reverseProxy.ServeHTTP(w, r)
}

func ErrorHandler(w http.ResponseWriter, req *http.Request, err error) {
	dnse := &net.DNSError{}
	if stderrors.As(err, &dnse) {
		err = errors.NewServiceUnavailable(err.Error())
	}
	log.Error(err, "proxy error", "url", req.URL.String())
	api.Error(w, err)
}

func (s *ServiceAPI) Group() api.Group {
	subgroups := []api.Group{
		api.NewGroup("").
			Route(
				api.Any("/{service}/{path}*").To(s.PathProxy),
			),
	}
	if hosts := slices.Collect(maps.Keys(s.Config.Hosts)); len(hosts) > 0 {
		hostsGroup := api.
			NewGroup("").
			Host(hosts...).
			Route(
				api.Any("/{path}*").To(s.HostProxy),
			)
		subgroups = append(subgroups, hostsGroup)
	}
	return api.NewGroup(s.Prefix).SubGroup(subgroups...)
}
