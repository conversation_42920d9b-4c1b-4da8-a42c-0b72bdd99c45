package apiserver

import (
	"context"
	"fmt"
	"strings"
	"time"

	"xiaoshiai.cn/common/rest/api"
)

const (
	SystemAdminGroup = "system:admin"
	SystemBanedGroup = "system:baned"
)

const (
	AuthorizationModeNone    = "none"
	AuthorizationModeWebhook = "webhook"
)

type AuthorizationOptions struct {
	Webhook   *api.WebhookAuthorizerOptions `json:"webhook,omitempty" description:"webhook authorizer options,only used when mode is 'webhook'"`
	Mode      string                        `json:"mode,omitempty" description:"authorization mode,support 'webhook' and 'none'"`
	CacheSize int                           `json:"cacheSize,omitempty" description:"cache size for authorizer"`
	CacheTime time.Duration                 `json:"cacheTime,omitempty" description:"cache time for authorizer"`
}

func DefaultAuthorizationOptions() *AuthorizationOptions {
	return &AuthorizationOptions{
		Mode: AuthorizationModeWebhook,
		Webhook: &api.WebhookAuthorizerOptions{
			Server: "http://iam-server/v1/webhook/authorize",
		},
	}
}

func BuildAuthz(ctx context.Context, options *AuthorizationOptions, others ...api.Authorizer) (api.Authorizer, error) {
	authzchain := api.AuthorizerChain{}
	authzchain = append(authzchain, others...)
	authzchain = append(authzchain, api.NewGroupAuthorizer([]string{SystemAdminGroup}, []string{SystemBanedGroup}))

	switch strings.ToLower(options.Mode) {
	case AuthorizationModeNone:
		authzchain = append(authzchain, api.NewAlwaysAllowAuthorizer())
	case AuthorizationModeWebhook:
		if options.Webhook.Server == "" {
			return nil, fmt.Errorf("webhook server is required when mode is 'webhook'")
		}
		webhookauthz, err := api.NewWebhookAuthorizer(options.Webhook)
		if err != nil {
			return nil, fmt.Errorf("failed to create webhook authorizer: %w", err)
		}
		authzchain = append(authzchain, webhookauthz)
	}
	finalauthz := api.Authorizer(authzchain)
	if options.CacheTime > 0 {
		size := options.CacheSize
		if size <= 0 {
			size = 128
		}
		finalauthz = api.NewCacheAuthorizer(finalauthz, size, options.CacheTime)
	}
	return finalauthz, nil
}
