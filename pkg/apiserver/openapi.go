package apiserver

import (
	"net/http"
	"path"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/rest/api"
)

var _ api.Plugin = &DynamicOpenAPIPlugin{}

type DynamicOpenAPIPlugin struct {
	Basepath    string
	SwaggerFunc func(service string, w http.ResponseWriter, r *http.Request)
}

func NewDynamicOpenAPIPlugin(basepath string, fn func(service string, w http.ResponseWriter, r *http.Request)) *DynamicOpenAPIPlugin {
	if basepath == "" {
		basepath = "/docs"
	}
	if fn == nil {
		fn = func(service string, w http.ResponseWriter, r *http.Request) {
			http.Error(w, "Dynamic OpenAPI not enabled", http.StatusNotImplemented)
		}
	}
	return &DynamicOpenAPIPlugin{
		Basepath:    basepath,
		SwaggerFunc: fn,
	}
}

func (s *DynamicOpenAPIPlugin) GetOpenAPISpec(w http.ResponseWriter, r *http.Request) {
	service := api.Path(r, "service", "")
	if service == "" {
		api.Error(w, errors.NewBadRequest("service is required"))
		return
	}
	s.SwaggerFunc(service, w, r)
}

func (s *DynamicOpenAPIPlugin) GetUI(w http.ResponseWriter, r *http.Request) {
	service := api.Path(r, "service", "")
	if service == "" {
		api.Error(w, errors.NewBadRequest("service is required"))
		return
	}
	specpath := path.Join(s.Basepath, service, "/openapi.json")
	switch r.URL.Query().Get("provider") {
	case "swagger", "":
		api.RenderHTML(w, api.NewSwaggerUI(specpath))
	case "redoc":
		api.RenderHTML(w, api.NewRedocUI(specpath))
	}
}

// Install implements Plugin.
func (s *DynamicOpenAPIPlugin) Install(m *api.API) error {
	uipath := path.Join(s.Basepath, "{service}")
	specpath := uipath + "/openapi.json"
	m.Route(
		api.GET(specpath).Doc("openapi spec").To(s.GetOpenAPISpec),
	)
	m.Route(
		api.GET(uipath).
			Operation("openapi ui").
			Param(
				api.QueryParam("provider", "UI provider").In("swagger", "redoc")).
			To(s.GetUI),
	)
	return nil
}

type ServiceDynamicOpenAPIGetter struct {
	Config *ServicesRuntimeConfig
}

func (s ServiceDynamicOpenAPIGetter) GetOpenAPISpec(service string, w http.ResponseWriter, r *http.Request) {
	svcConfig, ok := s.Config.Services[service]
	if !ok {
		api.Error(w, errors.NewNotFound("service", service))
		return
	}
	openapiPath := svcConfig.OpenapiPath
	if openapiPath == "" {
		openapiPath = "/docs/openapi.json" // default path if not specified
	}
	handler := ForwardHandler{
		Config:      svcConfig,
		RequestPath: openapiPath,
	}
	handler.ServeHTTP(w, r)
}
