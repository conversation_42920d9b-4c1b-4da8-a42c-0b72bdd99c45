package apiserver

import (
	"bytes"
	"context"
	"net/http"
	"strconv"
	"time"

	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/common/store/mongo"
)

var _ api.AuditSink = &DummySink{}

type DummySink struct{}

// Save implements api.AuditSink.
func (d DummySink) Save(log *api.AuditLog) error {
	return nil
}

func BuildAuditSink(ctx context.Context, storage store.Store, options *AuditOptions) (api.AuditSink, error) {
	if !options.Enabled {
		return DummySink{}, nil
	}
	auditsink := NewStorageAuditSink(storage, options)
	auditsink = api.NewCachedAuditSink(ctx, auditsink, api.DefaultAuditLogCacheSize)
	return auditsink, nil
}

var _ api.AuditSink = &StorageAuditSink{}

func NewStorageAuditSink(storage store.Store, options *AuditOptions) api.AuditSink {
	return &StorageAuditSink{Store: storage, Options: options}
}

type StorageAuditSink struct {
	Store   store.Store
	Options *AuditOptions
}

type Audit struct {
	store.ObjectMeta `json:",inline"`
	api.AuditLog     `json:",inline"`
	Tenant           string `json:"tenant,omitempty"`
	Organization     string `json:"organization,omitempty"`
}

func init() {
	mongo.GlobalObjectsScheme.Register(&Audit{}, mongo.ObjectDefination{
		Uniques: []mongo.UnionFields{
			{"name"},
		},
		Indexes: []mongo.UnionFields{
			{"action"},
			{"resourceType"},
			{"username"},
			{"startTime"},
			{"tenant"},
		},
	})
}

// Save implements api.AuditSink.
func (s *StorageAuditSink) Save(log *api.AuditLog) error {
	if log.Request.Method == http.MethodGet {
		return nil
	}
	scopes := []store.Scope{}
	for _, scope := range log.Parents {
		scopes = append(scopes, store.Scope{
			Resource: scope.Resource,
			Name:     scope.Name,
		})
	}
	audit := Audit{
		AuditLog: *log,
		ObjectMeta: store.ObjectMeta{
			// use unixnano as name to make sure it's unique
			Name: strconv.FormatInt(time.Now().UnixNano(), 10),
		},
	}
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	return s.Store.Scope(scopes...).Create(ctx, &audit)
}

func ExtractCreationNameAttrFilter() api.Filter {
	return api.FilterFunc(func(w http.ResponseWriter, r *http.Request, next http.Handler) {
		if r.Method != http.MethodPost {
			next.ServeHTTP(w, r)
			return
		}
		if auditlog := api.AuditLogFromContext(r.Context()); auditlog != nil && len(auditlog.Request.Body) > 0 {
			attr := api.AttributesFromContext(r.Context())
			if len(attr.Resources) > 0 && attr.Resources[len(attr.Resources)-1].Name == "" {
				type meta struct {
					Name string `json:"name"`
				}
				var m meta
				_ = api.ReadContent(r.Header.Get("Content-Type"), r.Header.Get("Content-Encoding"), bytes.NewReader(auditlog.Request.Body), &m)
				if m.Name != "" {
					attr.Resources[len(attr.Resources)-1].Name = m.Name
				}
			}
		}
		next.ServeHTTP(w, r)
	})
}

type AuditOptions struct {
	Enabled bool `json:"enabled,omitempty"`
}

func NewDefaultAuditOptions() *AuditOptions {
	return &AuditOptions{
		Enabled: true,
	}
}
