package apiserver

import (
	"context"
	"time"

	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/rest/api"
)

type AuthenticationOptions struct {
	Anonymous     bool                                  `json:"anonymous,omitempty"`
	OIDC          *api.OIDCOptions                      `json:"oidc,omitempty"`
	Webhook       *api.TokenWebhookAuthenticatorOptions `json:"webhook,omitempty" description:"webhook authenticator options"`
	CacheDuration time.Duration                         `json:"cacheDuration,omitempty" description:"cache duration for an valid token"`
	CacheSize     int                                   `json:"cacheSize,omitempty" description:"cache size for an valid token"`
}

func DefaultAuthenticationOptions() *AuthenticationOptions {
	return &AuthenticationOptions{
		Anonymous: false,
		OIDC:      api.NewDefaultOIDCOptions(),
		Webhook: &api.TokenWebhookAuthenticatorOptions{
			Server:     "http://iam-server/v1/webhook/authenticate",
			CookieName: "user_session",
		},
	}
}

func BuildAuthn(ctx context.Context, options *AuthenticationOptions, others ...api.Authenticator) (api.Authenticator, error) {
	tokenAuthnChain := api.TokenAuthenticatorChain{}
	if options.OIDC.Issuer != "" {
		// https://kubernetes.io/docs/reference/access-authn-authz/authentication/#openid-connect-tokens
		// same with k8s, use id_token(not access_token) as bearer token
		// we only need to know who the user is, not what the user can do
		oidcauthenticator, err := api.NewOIDCAuthenticator(ctx, options.OIDC)
		if err != nil {
			return nil, err
		}
		tokenAuthnChain = append(tokenAuthnChain, oidcauthenticator)
	}
	if options.Webhook.Server != "" {
		webhookauthn, err := api.NewTokenWebhookAuthenticator(options.Webhook)
		if err != nil {
			return nil, err
		}
		tokenAuthnChain = append(tokenAuthnChain, webhookauthn)
	}
	tokenauthn := api.TokenAuthenticator(tokenAuthnChain)
	// if cahce enabled, cache token
	if options.CacheDuration > 0 {
		cachesize := options.CacheSize
		if cachesize <= 0 {
			cachesize = 64
		}
		tokenauthn = api.NewCacheTokenAuthenticator(tokenauthn, cachesize, options.CacheDuration)
	}
	webhookbasic, err := api.NewBasicAuthWebhookAuthenticator(options.Webhook)
	if err != nil {
		return nil, err
	}
	authnlist := api.DelegateAuthenticator{
		api.SessionAuthenticatorWrap(tokenauthn, options.Webhook.CookieName),
		api.BearerTokenAuthenticatorWrap(tokenauthn),
		api.BasicAuthenticatorWrap(webhookbasic),
	}
	// add other authenticators
	authnlist = append(authnlist, others...)
	if options.Anonymous {
		log.FromContext(ctx).Info("anonymous authentication enabled")
		// anonymous cames at last in the chain, so that it users use token can be authenticated
		authnlist = append(authnlist, api.NewAnonymousAuthenticator())
	}
	return authnlist, nil
}
