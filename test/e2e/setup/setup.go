package setup

import (
	"context"
	"fmt"

	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/common/store/etcdcache"
	"xiaoshiai.cn/common/store/mongo"
	"xiaoshiai.cn/rune/pkg/cloud/cluster"
)

func SetupEtcdStore(ctx context.Context) (store.Store, error) {
	etcdoptions := etcdcache.NewDefaultOptions()
	etcdoptions.Servers = []string{
		"http://rune-etcd-0.rune-etcd-headless.rune:2379",
		"http://rune-etcd-1.rune-etcd-headless.rune:2379",
		"http://rune-etcd-2.rune-etcd-headless.rune:2379",
	}
	etcdstore, err := etcdcache.NewEtcdCacher(etcdoptions, etcdcache.ResourceFieldsMap{})
	if err != nil {
		return nil, fmt.Errorf("failed to create etcd store: %w", err)
	}
	return etcdstore, nil
}

func SetupMongoStore(ctx context.Context) (*mongo.MongoStorage, error) {
	mongooptions := mongo.NewDefaultMongoOptions("rune")
	mongooptions.Address = "rune-mongodb-headless.rune:27017"
	mongooptions.Database = "rune"
	mongooptions.Username = "root"
	mongooptions.Password = "q1u9D20L0I"
	mongostore, err := mongo.NewMongoStorage(ctx, mongo.GlobalObjectsScheme, mongooptions)
	if err != nil {
		return nil, fmt.Errorf("failed to create mongo store: %w", err)
	}
	return mongostore, nil
}

func SetupTestCloudInfo(ctx context.Context, storage store.Store) (cluster.CloudInfoGetter, func(), error) {
	etcdstore, err := SetupEtcdStore(ctx)
	if err != nil {
		return nil, func() {}, fmt.Errorf("failed to setup etcd store: %w", err)
	}
	cloudinfo := cluster.NewDefaultCloudInfoHolder(ctx)
	if err := cluster.InitCloudInfoHolder(ctx, etcdstore, cloudinfo); err != nil {
		return nil, func() {}, fmt.Errorf("failed to init cloud info holder: %w", err)
	}
	return cloudinfo, func() {}, nil
}
