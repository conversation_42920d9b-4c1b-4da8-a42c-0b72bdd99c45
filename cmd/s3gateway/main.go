package main

import (
	"fmt"
	"os"

	"github.com/spf13/cobra"
	"xiaoshiai.cn/common/config"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/rune/pkg/s3gateway"
)

func main() {
	cmd := NewCommand()
	if err := cmd.Execute(); err != nil {
		fmt.Fprintf(os.Stderr, "%v\n", err)
		os.Exit(1)
	}
}

func NewCommand() *cobra.Command {
	options := s3gateway.NewDefaultOptions()
	cmd := &cobra.Command{
		Use:                "s3gateway",
		Long:               "S3 Gateway For FS",
		DisableFlagParsing: true,
		RunE: func(cmd *cobra.Command, args []string) error {
			if err := config.Parse(cmd.Flags()); err != nil {
				return err
			}
			ctx := config.SetupSignalContext()
			ctx = log.NewContext(ctx, log.DefaultLogger)

			return s3gateway.Run(ctx, options)
		},
	}
	config.RegisterFlags(cmd.Flags(), "", options)
	return cmd
}
