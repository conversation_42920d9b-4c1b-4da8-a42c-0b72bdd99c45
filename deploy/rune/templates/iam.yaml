apiVersion: {{ include "common.capabilities.deployment.apiVersion" . }}
kind: Deployment
metadata:
  name: {{ include "rune.iam.fullname" . }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: iam
  {{- if or .Values.iam.deploymentAnnotations .Values.commonAnnotations }}
  {{- $annotations := include "common.tplvalues.merge" (dict "values" (list .Values.iam.deploymentAnnotations .Values.commonAnnotations) "context" .) }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" $annotations "context" $ ) | nindent 4 }}
  {{- end }}
spec:
  replicas: {{ .Values.iam.replicaCount }}
  {{- if .Values.iam.updateStrategy }}
  strategy: {{- toYaml .Values.iam.updateStrategy | nindent 4 }}
  {{- end }}
  {{- $podLabels := include "common.tplvalues.merge" (dict "values" (list .Values.iam.podLabels .Values.commonLabels) "context" .) }}
  selector:
    matchLabels: {{- include "common.labels.matchLabels" ( dict "customLabels" $podLabels "context" $ ) | nindent 6 }}
      app.kubernetes.io/component: iam
  template:
    metadata:
      {{- if .Values.iam.podAnnotations }}
      annotations: {{- include "common.tplvalues.render" (dict "value" .Values.iam.podAnnotations "context" $) | nindent 8 }}
      {{- end }}
      labels: {{- include "common.labels.standard" ( dict "customLabels" $podLabels "context" $ ) | nindent 8 }}
        app.kubernetes.io/component: iam
    spec:
      {{- include "rune.iam.imagePullSecrets" . | nindent 6 }}
      serviceAccountName: {{ include "rune.iam.serviceAccountName" . }}
      automountServiceAccountToken: {{ .Values.iam.serviceAccount.automountServiceAccountToken }}
      {{- if .Values.iam.hostAliases }}
      hostAliases: {{- include "common.tplvalues.render" (dict "value" .Values.iam.hostAliases "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.iam.affinity }}
      affinity: {{- include "common.tplvalues.render" ( dict "value" .Values.iam.affinity "context" $) | nindent 8 }}
      {{- else }}
      affinity:
        podAffinity: {{- include "common.affinities.pods" (dict "type" .Values.iam.podAffinityPreset "component" "iam" "customLabels" $podLabels "context" $) | nindent 10 }}
        podAntiAffinity: {{- include "common.affinities.pods" (dict "type" .Values.iam.podAntiAffinityPreset "component" "iam" "customLabels" $podLabels "context" $) | nindent 10 }}
        nodeAffinity: {{- include "common.affinities.nodes" (dict "type" .Values.iam.nodeAffinityPreset.type "key" .Values.iam.nodeAffinityPreset.key "values" .Values.iam.nodeAffinityPreset.values) | nindent 10 }}
      {{- end }}
      {{- if .Values.iam.nodeSelector }}
      nodeSelector: {{- include "common.tplvalues.render" ( dict "value" .Values.iam.nodeSelector "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.iam.tolerations }}
      tolerations: {{- include "common.tplvalues.render" (dict "value" .Values.iam.tolerations "context" .) | nindent 8 }}
      {{- end }}
      {{- if .Values.iam.priorityClassName }}
      priorityClassName: {{ .Values.iam.priorityClassName | quote }}
      {{- end }}
      {{- if .Values.iam.schedulerName }}
      schedulerName: {{ .Values.iam.schedulerName | quote }}
      {{- end }}
      {{- if .Values.iam.topologySpreadConstraints }}
      topologySpreadConstraints: {{- include "common.tplvalues.render" (dict "value" .Values.iam.topologySpreadConstraints "context" .) | nindent 8 }}
      {{- end }}
      {{- if .Values.iam.podSecurityContext.enabled }}
      securityContext: {{- omit .Values.iam.podSecurityContext "enabled" | toYaml | nindent 8 }}
      {{- end }}
      {{- if .Values.iam.terminationGracePeriodSeconds }}
      terminationGracePeriodSeconds: {{ .Values.iam.terminationGracePeriodSeconds }}
      {{- end }}
      initContainers:
        {{- if .Values.iam.initContainers }}
          {{- include "common.tplvalues.render" (dict "value" .Values.iam.initContainers "context" $) | nindent 8 }}
        {{- end }}
      containers:
        - name: iam
          image: {{ template "rune.iam.image" . }}
          imagePullPolicy: {{ .Values.iam.image.pullPolicy }}
          {{- if .Values.iam.containerSecurityContext.enabled }}
          securityContext: {{- include "common.compatibility.renderSecurityContext" (dict "secContext" .Values.iam.containerSecurityContext "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.iam.command }}
          command: {{- include "common.tplvalues.render" (dict "value" .Values.iam.command "context" $) | nindent 12 }}
          {{- else }}
          command:
            - iam
            - --leaderelection={{ .Values.iam.leaderElect }}
            - --v={{ .Values.iam.logLevel }}
            {{- if .Values.iam.extraArgs }}
            {{- include "common.tplvalues.render" (dict "value" .Values.iam.extraArgs "context" $) | nindent 12 }}
            {{- end }}
          {{- end }}
          {{- if .Values.iam.args }}
          args: {{- include "common.tplvalues.render" (dict "value" .Values.iam.args "context" $) | nindent 12 }}
          {{- end }}
          env:
            {{- include "rune.common.env" . | nindent 12 }}
            {{- include "rune.iam.env" . | nindent 12 }}
            {{- include "rune.mongodb.env" . | nindent 12 }}
            {{- include "rune.etcd.env" . | nindent 12 }}
            {{- if .Values.iam.extraEnvVars }}
            {{- include "common.tplvalues.render" (dict "value" .Values.iam.extraEnvVars "context" $) | nindent 12 }}
            {{- end }}
          envFrom:
            {{- if .Values.iam.extraEnvVarsCM }}
            - configMapRef:
                name: {{ include "common.tplvalues.render" (dict "value" .Values.iam.extraEnvVarsCM "context" $) }}
            {{- end }}
            {{- if .Values.iam.extraEnvVarsSecret }}
            - secretRef:
                name: {{ include "common.tplvalues.render" (dict "value" .Values.iam.extraEnvVarsSecret "context" $) }}
            {{- end }}
          {{- if .Values.iam.resources }}
          resources: {{- toYaml .Values.iam.resources | nindent 12 }}
          {{- else if ne .Values.iam.resourcesPreset "none" }}
          resources: {{- include "common.resources.preset" (dict "type" .Values.iam.resourcesPreset) | nindent 12 }}
          {{- end }}
          ports:
            - name: http
              containerPort: {{ .Values.iam.containerPorts.http }}
            {{- if .Values.iam.extraContainerPorts }}
            {{- include "common.tplvalues.render" (dict "value" .Values.iam.extraContainerPorts "context" $) | nindent 12 }}
            {{- end }}
          {{- if .Values.iam.customLivenessProbe }}
          livenessProbe: {{- include "common.tplvalues.render" (dict "value" .Values.iam.customLivenessProbe "context" $) | nindent 12 }}
          {{- else if .Values.iam.livenessProbe.enabled }}
          livenessProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.iam.livenessProbe "enabled") "context" $) | nindent 12 }}
            httpGet:
              path: /healthz
              port: http
          {{- end }}
          {{- if .Values.iam.customReadinessProbe }}
          readinessProbe: {{- include "common.tplvalues.render" (dict "value" .Values.iam.customReadinessProbe "context" $) | nindent 12 }}
          {{- else if .Values.iam.readinessProbe.enabled }}
          readinessProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.iam.readinessProbe "enabled") "context" $) | nindent 12 }}
            httpGet:
              path: /healthz
              port: http
          {{- end }}
          {{- if .Values.iam.customStartupProbe }}
          startupProbe: {{- include "common.tplvalues.render" (dict "value" .Values.iam.customStartupProbe "context" $) | nindent 12 }}
          {{- else if .Values.iam.startupProbe.enabled }}
          startupProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.iam.startupProbe "enabled") "context" $) | nindent 12 }}
            httpGet:
              path: /healthz
              port: http
          {{- end }}
          {{- if .Values.iam.lifecycleHooks }}
          lifecycle: {{- include "common.tplvalues.render" (dict "value" .Values.iam.lifecycleHooks "context" $) | nindent 12 }}
          {{- end }}
          volumeMounts:
          {{- if .Values.iam.extraVolumeMounts }}
          {{- include "common.tplvalues.render" (dict "value" .Values.iam.extraVolumeMounts "context" $) | nindent 12 }}
          {{- end }}
        {{- if .Values.iam.sidecars }}
        {{- include "common.tplvalues.render" ( dict "value" .Values.iam.sidecars "context" $) | nindent 8 }}
        {{- end }}
      volumes:
        {{- if .Values.iam.extraVolumes }}
        {{- include "common.tplvalues.render" (dict "value" .Values.iam.extraVolumes "context" $) | nindent 8 }}
        {{- end }}
---
apiVersion: v1
kind: Service
metadata:
  name: {{ include "rune.iam.fullname" . }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: apiserver
  {{- if or .Values.iam.service.annotations .Values.commonAnnotations }}
  {{- $annotations := include "common.tplvalues.merge" (dict "values" (list .Values.iam.service.annotations .Values.commonAnnotations) "context" .) }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" $annotations "context" $ ) | nindent 4 }}
  {{- end }}
spec:
  type: {{ .Values.iam.service.type }}
  {{- if and .Values.iam.service.clusterIP (eq .Values.iam.service.type "ClusterIP") }}
  clusterIP: {{ .Values.iam.service.clusterIP }}
  {{- end }}
  {{- if .Values.iam.service.sessionAffinity }}
  sessionAffinity: {{ .Values.iam.service.sessionAffinity }}
  {{- end }}
  {{- if .Values.iam.service.sessionAffinityConfig }}
  sessionAffinityConfig: {{- include "common.tplvalues.render" (dict "value" .Values.iam.service.sessionAffinityConfig "context" $) | nindent 4 }}
  {{- end }}
  {{- if or (eq .Values.iam.service.type "LoadBalancer") (eq .Values.iam.service.type "NodePort") }}
  externalTrafficPolicy: {{ .Values.iam.service.externalTrafficPolicy | quote }}
  {{- end }}
  {{- if and (eq .Values.iam.service.type "LoadBalancer") (not (empty .Values.iam.service.loadBalancerSourceRanges)) }}
  loadBalancerSourceRanges: {{ .Values.iam.service.loadBalancerSourceRanges }}
  {{- end }}
  {{- if and (eq .Values.iam.service.type "LoadBalancer") (not (empty .Values.iam.service.loadBalancerIP)) }}
  loadBalancerIP: {{ .Values.iam.service.loadBalancerIP }}
  {{- end }}
  ports:
    - name: http
      port: {{ .Values.iam.service.ports.http }}
      {{- if and (or (eq .Values.iam.service.type "NodePort") (eq .Values.iam.service.type "LoadBalancer")) (not (empty .Values.iam.service.nodePorts.https)) }}
      nodePort: {{ .Values.iam.service.nodePorts.http }}
      {{- else if eq .Values.iam.service.type "ClusterIP" }}
      nodePort: null
      {{- end }}
    {{- if .Values.iam.service.extraPorts }}
    {{- include "common.tplvalues.render" (dict "value" .Values.iam.service.extraPorts "context" $) | nindent 4 }}
    {{- end }}
  {{- $podLabels := include "common.tplvalues.merge" (dict "values" (list .Values.iam.podLabels .Values.commonLabels) "context" .) | fromYaml }}
  selector: {{- include "common.labels.matchLabels" ( dict "customLabels" $podLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: apiserver