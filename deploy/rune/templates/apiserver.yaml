apiVersion: {{ include "common.capabilities.deployment.apiVersion" . }}
kind: Deployment
metadata:
  name: {{ include "rune.apiserver.fullname" . }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: apiserver
  {{- if or .Values.apiserver.deploymentAnnotations .Values.commonAnnotations }}
  {{- $annotations := include "common.tplvalues.merge" (dict "values" (list .Values.apiserver.deploymentAnnotations .Values.commonAnnotations) "context" .) }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" $annotations "context" $ ) | nindent 4 }}
  {{- end }}
spec:
  replicas: {{ .Values.apiserver.replicaCount }}
  {{- if .Values.apiserver.updateStrategy }}
  strategy: {{- toYaml .Values.apiserver.updateStrategy | nindent 4 }}
  {{- end }}
  {{- $podLabels := include "common.tplvalues.merge" (dict "values" (list .Values.apiserver.podLabels .Values.commonLabels) "context" .) }}
  selector:
    matchLabels: {{- include "common.labels.matchLabels" ( dict "customLabels" $podLabels "context" $ ) | nindent 6 }}
      app.kubernetes.io/component: apiserver
  template:
    metadata:
      {{- if .Values.apiserver.podAnnotations }}
      annotations: {{- include "common.tplvalues.render" (dict "value" .Values.apiserver.podAnnotations "context" $) | nindent 8 }}
      {{- end }}
      labels: {{- include "common.labels.standard" ( dict "customLabels" $podLabels "context" $ ) | nindent 8 }}
        app.kubernetes.io/component: apiserver
    spec:
      {{- include "rune.apiserver.imagePullSecrets" . | nindent 6 }}
      serviceAccountName: {{ include "rune.apiserver.serviceAccountName" . }}
      automountServiceAccountToken: {{ .Values.apiserver.serviceAccount.automountServiceAccountToken }}
      {{- if .Values.apiserver.hostAliases }}
      hostAliases: {{- include "common.tplvalues.render" (dict "value" .Values.apiserver.hostAliases "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.apiserver.affinity }}
      affinity: {{- include "common.tplvalues.render" ( dict "value" .Values.apiserver.affinity "context" $) | nindent 8 }}
      {{- else }}
      affinity:
        podAffinity: {{- include "common.affinities.pods" (dict "type" .Values.apiserver.podAffinityPreset "component" "apiserver" "customLabels" $podLabels "context" $) | nindent 10 }}
        podAntiAffinity: {{- include "common.affinities.pods" (dict "type" .Values.apiserver.podAntiAffinityPreset "component" "apiserver" "customLabels" $podLabels "context" $) | nindent 10 }}
        nodeAffinity: {{- include "common.affinities.nodes" (dict "type" .Values.apiserver.nodeAffinityPreset.type "key" .Values.apiserver.nodeAffinityPreset.key "values" .Values.apiserver.nodeAffinityPreset.values) | nindent 10 }}
      {{- end }}
      {{- if .Values.apiserver.nodeSelector }}
      nodeSelector: {{- include "common.tplvalues.render" ( dict "value" .Values.apiserver.nodeSelector "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.apiserver.tolerations }}
      tolerations: {{- include "common.tplvalues.render" (dict "value" .Values.apiserver.tolerations "context" .) | nindent 8 }}
      {{- end }}
      {{- if .Values.apiserver.priorityClassName }}
      priorityClassName: {{ .Values.apiserver.priorityClassName | quote }}
      {{- end }}
      {{- if .Values.apiserver.schedulerName }}
      schedulerName: {{ .Values.apiserver.schedulerName | quote }}
      {{- end }}
      {{- if .Values.apiserver.topologySpreadConstraints }}
      topologySpreadConstraints: {{- include "common.tplvalues.render" (dict "value" .Values.apiserver.topologySpreadConstraints "context" .) | nindent 8 }}
      {{- end }}
      {{- if .Values.apiserver.podSecurityContext.enabled }}
      securityContext: {{- omit .Values.apiserver.podSecurityContext "enabled" | toYaml | nindent 8 }}
      {{- end }}
      {{- if .Values.apiserver.terminationGracePeriodSeconds }}
      terminationGracePeriodSeconds: {{ .Values.apiserver.terminationGracePeriodSeconds }}
      {{- end }}
      hostNetwork: false
      initContainers:
        {{- if .Values.apiserver.initContainers }}
          {{- include "common.tplvalues.render" (dict "value" .Values.apiserver.initContainers "context" $) | nindent 8 }}
        {{- end }}
      containers:
        - name: apiserver
          image: {{ template "rune.apiserver.image" . }}
          imagePullPolicy: {{ .Values.apiserver.image.pullPolicy }}
          {{- if .Values.apiserver.containerSecurityContext.enabled }}
          securityContext: {{- include "common.compatibility.renderSecurityContext" (dict "secContext" .Values.apiserver.containerSecurityContext "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.apiserver.command }}
          command: {{- include "common.tplvalues.render" (dict "value" .Values.apiserver.command "context" $) | nindent 12 }}
           {{- else }}
          command:
            - apiserver
            - --listen=:{{ .Values.apiserver.service.ports.http }}
            - --v={{ .Values.apiserver.logLevel }}
            {{- if .Values.apiserver.extraArgs }}
            {{- include "common.tplvalues.render" (dict "value" .Values.apiserver.extraArgs "context" $) | nindent 12 }}
            {{- end }}
          {{- end }}
          {{- if .Values.apiserver.args }}
          args: {{- include "common.tplvalues.render" (dict "value" .Values.apiserver.args "context" $) | nindent 12 }}
          {{- end }}
          env:
            {{- include "rune.common.env" . | nindent 12 }}
            {{- include "rune.mongodb.env" . | nindent 12 }}
            {{- include "rune.etcd.env" . | nindent 12 }}
            {{- if .Values.apiserver.extraEnvVars }}
            {{- include "common.tplvalues.render" (dict "value" .Values.apiserver.extraEnvVars "context" $) | nindent 12 }}
            {{- end }}
          envFrom:
            {{- if .Values.apiserver.extraEnvVarsCM }}
            - configMapRef:
                name: {{ include "common.tplvalues.render" (dict "value" .Values.apiserver.extraEnvVarsCM "context" $) }}
            {{- end }}
            {{- if .Values.apiserver.extraEnvVarsSecret }}
            - secretRef:
                name: {{ include "common.tplvalues.render" (dict "value" .Values.apiserver.extraEnvVarsSecret "context" $) }}
            {{- end }}
          {{- if .Values.apiserver.resources }}
          resources: {{- toYaml .Values.apiserver.resources | nindent 12 }}
          {{- else if ne .Values.apiserver.resourcesPreset "none" }}
          resources: {{- include "common.resources.preset" (dict "type" .Values.apiserver.resourcesPreset) | nindent 12 }}
          {{- end }}
          ports:
            - name: http
              containerPort: {{ .Values.apiserver.service.ports.http }}
          {{- if .Values.apiserver.customLivenessProbe }}
          livenessProbe: {{- include "common.tplvalues.render" (dict "value" .Values.apiserver.customLivenessProbe "context" $) | nindent 12 }}
          {{- else if .Values.apiserver.livenessProbe.enabled }}
          livenessProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.apiserver.livenessProbe "enabled") "context" $) | nindent 12 }}
            httpGet:
              path: /healthz
              port: http
          {{- end }}
          {{- if .Values.apiserver.customReadinessProbe }}
          readinessProbe: {{- include "common.tplvalues.render" (dict "value" .Values.apiserver.customReadinessProbe "context" $) | nindent 12 }}
          {{- else if .Values.apiserver.readinessProbe.enabled }}
          readinessProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.apiserver.readinessProbe "enabled") "context" $) | nindent 12 }}
            httpGet:
              path: /healthz
              port: http
          {{- end }}
          {{- if .Values.apiserver.customStartupProbe }}
          startupProbe: {{- include "common.tplvalues.render" (dict "value" .Values.apiserver.customStartupProbe "context" $) | nindent 12 }}
          {{- else if .Values.apiserver.startupProbe.enabled }}
          startupProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.apiserver.startupProbe "enabled") "context" $) | nindent 12 }}
            httpGet:
              path: /healthz
              port: http
          {{- end }}
          {{- if .Values.apiserver.lifecycleHooks }}
          lifecycle: {{- include "common.tplvalues.render" (dict "value" .Values.apiserver.lifecycleHooks "context" $) | nindent 12 }}
          {{- end }}
        {{- if .Values.apiserver.sidecars }}
        {{- include "common.tplvalues.render" ( dict "value" .Values.apiserver.sidecars "context" $) | nindent 8 }}
        {{- end }}
---
apiVersion: v1
kind: Service
metadata:
  name: {{ include "rune.apiserver.fullname" . }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: apiserver
  {{- if or .Values.apiserver.service.annotations .Values.commonAnnotations }}
  {{- $annotations := include "common.tplvalues.merge" (dict "values" (list .Values.apiserver.service.annotations .Values.commonAnnotations) "context" .) }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" $annotations "context" $ ) | nindent 4 }}
  {{- end }}
spec:
  type: {{ .Values.apiserver.service.type }}
  {{- if and .Values.apiserver.service.clusterIP (eq .Values.apiserver.service.type "ClusterIP") }}
  clusterIP: {{ .Values.apiserver.service.clusterIP }}
  {{- end }}
  {{- if .Values.apiserver.service.sessionAffinity }}
  sessionAffinity: {{ .Values.apiserver.service.sessionAffinity }}
  {{- end }}
  {{- if .Values.apiserver.service.sessionAffinityConfig }}
  sessionAffinityConfig: {{- include "common.tplvalues.render" (dict "value" .Values.apiserver.service.sessionAffinityConfig "context" $) | nindent 4 }}
  {{- end }}
  {{- if or (eq .Values.apiserver.service.type "LoadBalancer") (eq .Values.apiserver.service.type "NodePort") }}
  externalTrafficPolicy: {{ .Values.apiserver.service.externalTrafficPolicy | quote }}
  {{- end }}
  {{- if and (eq .Values.apiserver.service.type "LoadBalancer") (not (empty .Values.apiserver.service.loadBalancerSourceRanges)) }}
  loadBalancerSourceRanges: {{ .Values.apiserver.service.loadBalancerSourceRanges }}
  {{- end }}
  {{- if and (eq .Values.apiserver.service.type "LoadBalancer") (not (empty .Values.apiserver.service.loadBalancerIP)) }}
  loadBalancerIP: {{ .Values.apiserver.service.loadBalancerIP }}
  {{- end }}
  ports:
    - name: http
      port: {{ .Values.apiserver.service.ports.http }}
      {{- if and (or (eq .Values.apiserver.service.type "NodePort") (eq .Values.apiserver.service.type "LoadBalancer")) (not (empty .Values.apiserver.service.nodePorts.https)) }}
      nodePort: {{ .Values.apiserver.service.nodePorts.http }}
      {{- else if eq .Values.apiserver.service.type "ClusterIP" }}
      nodePort: null
      {{- end }}
    {{- if .Values.apiserver.service.extraPorts }}
    {{- include "common.tplvalues.render" (dict "value" .Values.apiserver.service.extraPorts "context" $) | nindent 4 }}
    {{- end }}
  {{- $podLabels := include "common.tplvalues.merge" (dict "values" (list .Values.apiserver.podLabels .Values.commonLabels) "context" .) | fromYaml }}
  selector: {{- include "common.labels.matchLabels" ( dict "customLabels" $podLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: apiserver