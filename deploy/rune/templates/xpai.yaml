apiVersion: {{ include "common.capabilities.deployment.apiVersion" . }}
kind: Deployment
metadata:
  name: {{ include "rune.xpai.fullname" . }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: xpai
  {{- if or .Values.xpai.deploymentAnnotations .Values.commonAnnotations }}
  {{- $annotations := include "common.tplvalues.merge" (dict "values" (list .Values.xpai.deploymentAnnotations .Values.commonAnnotations) "context" .) }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" $annotations "context" $ ) | nindent 4 }}
  {{- end }}
spec:
  replicas: {{ .Values.xpai.replicaCount }}
  {{- if .Values.xpai.updateStrategy }}
  strategy: {{- toYaml .Values.xpai.updateStrategy | nindent 4 }}
  {{- end }}
  {{- $podLabels := include "common.tplvalues.merge" (dict "values" (list .Values.xpai.podLabels .Values.commonLabels) "context" .) }}
  selector:
    matchLabels: {{- include "common.labels.matchLabels" ( dict "customLabels" $podLabels "context" $ ) | nindent 6 }}
      app.kubernetes.io/component: xpai
  template:
    metadata:
      {{- if .Values.xpai.podAnnotations }}
      annotations: {{- include "common.tplvalues.render" (dict "value" .Values.xpai.podAnnotations "context" $) | nindent 8 }}
      {{- end }}
      labels: {{- include "common.labels.standard" ( dict "customLabels" $podLabels "context" $ ) | nindent 8 }}
        app.kubernetes.io/component: xpai
    spec:
      {{- include "rune.xpai.imagePullSecrets" . | nindent 6 }}
      serviceAccountName: {{ include "rune.xpai.serviceAccountName" . }}
      automountServiceAccountToken: {{ .Values.xpai.serviceAccount.automountServiceAccountToken }}
      {{- if .Values.xpai.hostAliases }}
      hostAliases: {{- include "common.tplvalues.render" (dict "value" .Values.xpai.hostAliases "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.xpai.affinity }}
      affinity: {{- include "common.tplvalues.render" ( dict "value" .Values.xpai.affinity "context" $) | nindent 8 }}
      {{- else }}
      affinity:
        podAffinity: {{- include "common.affinities.pods" (dict "type" .Values.xpai.podAffinityPreset "component" "xpai" "customLabels" $podLabels "context" $) | nindent 10 }}
        podAntiAffinity: {{- include "common.affinities.pods" (dict "type" .Values.xpai.podAntiAffinityPreset "component" "xpai" "customLabels" $podLabels "context" $) | nindent 10 }}
        nodeAffinity: {{- include "common.affinities.nodes" (dict "type" .Values.xpai.nodeAffinityPreset.type "key" .Values.xpai.nodeAffinityPreset.key "values" .Values.xpai.nodeAffinityPreset.values) | nindent 10 }}
      {{- end }}
      {{- if .Values.xpai.nodeSelector }}
      nodeSelector: {{- include "common.tplvalues.render" ( dict "value" .Values.xpai.nodeSelector "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.xpai.tolerations }}
      tolerations: {{- include "common.tplvalues.render" (dict "value" .Values.xpai.tolerations "context" .) | nindent 8 }}
      {{- end }}
      {{- if .Values.xpai.priorityClassName }}
      priorityClassName: {{ .Values.xpai.priorityClassName | quote }}
      {{- end }}
      {{- if .Values.xpai.schedulerName }}
      schedulerName: {{ .Values.xpai.schedulerName | quote }}
      {{- end }}
      {{- if .Values.xpai.topologySpreadConstraints }}
      topologySpreadConstraints: {{- include "common.tplvalues.render" (dict "value" .Values.xpai.topologySpreadConstraints "context" .) | nindent 8 }}
      {{- end }}
      {{- if .Values.xpai.podSecurityContext.enabled }}
      securityContext: {{- omit .Values.xpai.podSecurityContext "enabled" | toYaml | nindent 8 }}
      {{- end }}
      {{- if .Values.xpai.terminationGracePeriodSeconds }}
      terminationGracePeriodSeconds: {{ .Values.xpai.terminationGracePeriodSeconds }}
      {{- end }}
      hostNetwork: false
      initContainers:
        {{- if .Values.xpai.initContainers }}
          {{- include "common.tplvalues.render" (dict "value" .Values.xpai.initContainers "context" $) | nindent 8 }}
        {{- end }}
      containers:
        - name: xpai
          image: {{ template "rune.xpai.image" . }}
          imagePullPolicy: {{ .Values.xpai.image.pullPolicy }}
          {{- if .Values.xpai.containerSecurityContext.enabled }}
          securityContext: {{- include "common.compatibility.renderSecurityContext" (dict "secContext" .Values.xpai.containerSecurityContext "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.xpai.command }}
          command: {{- include "common.tplvalues.render" (dict "value" .Values.xpai.command "context" $) | nindent 12 }}
           {{- else }}
          command:
            - xpai
            - --listen=:{{ .Values.xpai.service.ports.http }}
            - --v={{ .Values.xpai.logLevel }}
            {{- if .Values.xpai.extraArgs }}
            {{- include "common.tplvalues.render" (dict "value" .Values.xpai.extraArgs "context" $) | nindent 12 }}
            {{- end }}
          {{- end }}
          {{- if .Values.xpai.args }}
          args: {{- include "common.tplvalues.render" (dict "value" .Values.xpai.args "context" $) | nindent 12 }}
          {{- end }}
          env:
            {{- include "rune.common.env" . | nindent 12 }}
            {{- include "rune.mongodb.env" . | nindent 12 }}
            {{- include "rune.etcd.env" . | nindent 12 }}
            {{- if .Values.xpai.extraEnvVars }}
            {{- include "common.tplvalues.render" (dict "value" .Values.xpai.extraEnvVars "context" $) | nindent 12 }}
            {{- end }}
          envFrom:
            {{- if .Values.xpai.extraEnvVarsCM }}
            - configMapRef:
                name: {{ include "common.tplvalues.render" (dict "value" .Values.xpai.extraEnvVarsCM "context" $) }}
            {{- end }}
            {{- if .Values.xpai.extraEnvVarsSecret }}
            - secretRef:
                name: {{ include "common.tplvalues.render" (dict "value" .Values.xpai.extraEnvVarsSecret "context" $) }}
            {{- end }}
          {{- if .Values.xpai.resources }}
          resources: {{- toYaml .Values.xpai.resources | nindent 12 }}
          {{- else if ne .Values.xpai.resourcesPreset "none" }}
          resources: {{- include "common.resources.preset" (dict "type" .Values.xpai.resourcesPreset) | nindent 12 }}
          {{- end }}
          ports:
            - name: http
              containerPort: {{ .Values.xpai.service.ports.http }}
          {{- if .Values.xpai.customLivenessProbe }}
          livenessProbe: {{- include "common.tplvalues.render" (dict "value" .Values.xpai.customLivenessProbe "context" $) | nindent 12 }}
          {{- else if .Values.xpai.livenessProbe.enabled }}
          livenessProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.xpai.livenessProbe "enabled") "context" $) | nindent 12 }}
            httpGet:
              path: /healthz
              port: http
          {{- end }}
          {{- if .Values.xpai.customReadinessProbe }}
          readinessProbe: {{- include "common.tplvalues.render" (dict "value" .Values.xpai.customReadinessProbe "context" $) | nindent 12 }}
          {{- else if .Values.xpai.readinessProbe.enabled }}
          readinessProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.xpai.readinessProbe "enabled") "context" $) | nindent 12 }}
            httpGet:
              path: /healthz
              port: http
          {{- end }}
          {{- if .Values.xpai.customStartupProbe }}
          startupProbe: {{- include "common.tplvalues.render" (dict "value" .Values.xpai.customStartupProbe "context" $) | nindent 12 }}
          {{- else if .Values.xpai.startupProbe.enabled }}
          startupProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.xpai.startupProbe "enabled") "context" $) | nindent 12 }}
            httpGet:
              path: /healthz
              port: http
          {{- end }}
          {{- if .Values.xpai.lifecycleHooks }}
          lifecycle: {{- include "common.tplvalues.render" (dict "value" .Values.xpai.lifecycleHooks "context" $) | nindent 12 }}
          {{- end }}
        {{- if .Values.xpai.sidecars }}
        {{- include "common.tplvalues.render" ( dict "value" .Values.xpai.sidecars "context" $) | nindent 8 }}
        {{- end }}
---
apiVersion: v1
kind: Service
metadata:
  name: {{ include "rune.xpai.fullname" . }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: xpai
  {{- if or .Values.xpai.service.annotations .Values.commonAnnotations }}
  {{- $annotations := include "common.tplvalues.merge" (dict "values" (list .Values.xpai.service.annotations .Values.commonAnnotations) "context" .) }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" $annotations "context" $ ) | nindent 4 }}
  {{- end }}
spec:
  type: {{ .Values.xpai.service.type }}
  {{- if and .Values.xpai.service.clusterIP (eq .Values.xpai.service.type "ClusterIP") }}
  clusterIP: {{ .Values.xpai.service.clusterIP }}
  {{- end }}
  {{- if .Values.xpai.service.sessionAffinity }}
  sessionAffinity: {{ .Values.xpai.service.sessionAffinity }}
  {{- end }}
  {{- if .Values.xpai.service.sessionAffinityConfig }}
  sessionAffinityConfig: {{- include "common.tplvalues.render" (dict "value" .Values.xpai.service.sessionAffinityConfig "context" $) | nindent 4 }}
  {{- end }}
  {{- if or (eq .Values.xpai.service.type "LoadBalancer") (eq .Values.xpai.service.type "NodePort") }}
  externalTrafficPolicy: {{ .Values.xpai.service.externalTrafficPolicy | quote }}
  {{- end }}
  {{- if and (eq .Values.xpai.service.type "LoadBalancer") (not (empty .Values.xpai.service.loadBalancerSourceRanges)) }}
  loadBalancerSourceRanges: {{ .Values.xpai.service.loadBalancerSourceRanges }}
  {{- end }}
  {{- if and (eq .Values.xpai.service.type "LoadBalancer") (not (empty .Values.xpai.service.loadBalancerIP)) }}
  loadBalancerIP: {{ .Values.xpai.service.loadBalancerIP }}
  {{- end }}
  ports:
    - name: http
      port: {{ .Values.xpai.service.ports.http }}
      {{- if and (or (eq .Values.xpai.service.type "NodePort") (eq .Values.xpai.service.type "LoadBalancer")) (not (empty .Values.xpai.service.nodePorts.https)) }}
      nodePort: {{ .Values.xpai.service.nodePorts.http }}
      {{- else if eq .Values.xpai.service.type "ClusterIP" }}
      nodePort: null
      {{- end }}
    {{- if .Values.xpai.service.extraPorts }}
    {{- include "common.tplvalues.render" (dict "value" .Values.xpai.service.extraPorts "context" $) | nindent 4 }}
    {{- end }}
  {{- $podLabels := include "common.tplvalues.merge" (dict "values" (list .Values.xpai.podLabels .Values.commonLabels) "context" .) | fromYaml }}
  selector: {{- include "common.labels.matchLabels" ( dict "customLabels" $podLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: xpai