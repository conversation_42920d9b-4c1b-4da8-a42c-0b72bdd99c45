apiVersion: v2
appVersion: 0.0.0
dependencies:
  - name: common
    version: 2.x.x
    repository: oci://registry-1.docker.io/bitnamicharts
  - name: etcd
    version: 9.x.x
    repository: oci://registry-1.docker.io/bitnamicharts
    condition: etcd.enabled
  - name: mongodb
    version: 16.x.x
    repository: oci://registry-1.docker.io/bitnamicharts
    condition: mongodb.enabled
  - name: casdoor-helm-charts
    repository: oci://registry-1.docker.io/casbin
    version: v1.952.0
    condition: casdoor-helm-charts.enabled
  - name: postgresql
    repository: oci://registry-1.docker.io/bitnamicharts
    version: 16.x.x
    condition: postgresql.enabled
name: rune
version: 0.0.0
